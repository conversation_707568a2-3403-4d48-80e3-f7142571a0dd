#include "saribbonuser.h"
#include <QEventLoop>
#include <QMouseEvent>
#include <QDebug>
#include <QApplication>

/**
 * @brief The SARibbonUserPrivate class
 */
class SARibbonUserPrivate
{
public:
    SARibbonUser *Parent;
    QEventLoop *eventLoop;
    SARibbonUserPrivate(SARibbonUser *p)
        : Parent(p)
        , eventLoop(nullptr)
    {
    }

    void init()
    {

    }
};

SARibbonUser::SARibbonUser(QWidget *parent)
    : QWidget(parent)
    , m_d(new SARibbonUserPrivate(this))
{

}

SARibbonUser::~SARibbonUser()
{
    if(m_d->eventLoop) {
        m_d->eventLoop->exit();
    }
    delete m_d;
}
