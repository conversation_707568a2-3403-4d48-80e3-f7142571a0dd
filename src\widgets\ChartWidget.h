#ifndef CHARTWIDGET_H
#define CHARTWIDGET_H

#include <QWidget>
#include <QtCharts>
#include <QChartView>
#include <QLineSeries>
#include <QValueAxis>
#include <QPainter>

QT_CHARTS_USE_NAMESPACE

class ChartWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ChartWidget(QWidget *parent = nullptr);
    ~ChartWidget();

    // 添加数据系列
    void addSeries(const QString& name, const QVector<double>& x, const QVector<double>& y);
    // 清除所有数据系列
    void clearSeries();
    // 设置图表标题
    void setTitle(const QString& title);
    // 设置X轴标签
    void setXAxisLabel(const QString& label);
    // 设置Y轴标签
    void setYAxisLabel(const QString& label);
    // 设置X轴范围
    void setXAxisRange(double min, double max);
    // 设置Y轴范围
    void setYAxisRange(double min, double max);
    // 设置主题
    void setTheme(QChart::ChartTheme theme);
    // 设置动画选项
    void setAnimationOptions(QChart::AnimationOptions options);
    // 设置图例位置
    void setLegendAlignment(Qt::AlignmentFlag alignment);
    // 设置图例可见性
    void setLegendVisible(bool visible);

    QString getCurrentPath();

private:
    void initUI();
    void setupChart();
    void addSampleData();

    QChartView* chartView;
    QChart* chart;
    QValueAxis* axisX;
    QValueAxis* axisY;
};

#endif // CHARTWIDGET_H 