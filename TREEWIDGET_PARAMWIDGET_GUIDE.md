# TreeWidget和ParamWidget集成指南

## 概述

本项目已成功集成了TreeWidget和ParamWidget两个核心组件，实现了左侧工具面板的分割和功能化。

## 组件结构

### 1. CustomTreeWidget (项目导航器)
- **位置**: 左侧面板上半部分
- **功能**: 显示项目的层次结构，包括优化设置、输入参数和输出配置
- **特性**:
  - 树形结构显示项目组织
  - 支持节点选择和双击事件
  - 右键上下文菜单支持
  - 不同节点类型的图标显示

#### 节点类型
- **Root**: 项目根节点
- **Optimize**: 优化算法设置
- **Input**: 输入参数配置
- **Output**: 输出设置配置

### 2. ParamWidget (参数面板)
- **位置**: 左侧面板下半部分
- **功能**: 根据TreeWidget中选中的节点类型，动态显示相应的参数设置界面
- **特性**:
  - 堆叠式界面设计，支持多个参数页面
  - 表单布局，便于参数输入和修改
  - 分组显示相关参数

#### 参数页面
1. **优化参数页面**:
   - 算法选择
   - 种群大小
   - 最大代数
   - 变异率
   - 收敛条件
   - 并行处理选项

2. **输入参数页面**:
   - 输入文件选择
   - 参数数量
   - 数值范围设置
   - 约束条件配置

3. **输出参数页面**:
   - 输出目录设置
   - 文件命名
   - 日志保存选项
   - 报告生成设置

## 交互机制

### 1. 节点选择联动
- 在TreeWidget中选择节点时，ParamWidget自动切换到对应的参数页面
- 通过信号槽机制实现组件间通信

### 2. 双击事件
- 双击TreeWidget中的节点，主工作区会显示该节点的详细信息
- 提供节点功能的详细说明和当前配置状态

### 3. 界面布局
- 左侧面板采用垂直分割，TreeWidget和ParamWidget各占50%空间
- 支持面板大小调整，最小宽度250px，最大宽度350px
- 主工作区自适应剩余空间

## 代码结构

### 文件组织
```
src/
├── widgets/
│   ├── CustomTreeWidget.h/cpp    # 树形导航组件
│   └── ParamWidget.h/cpp          # 参数设置组件
├── ui/
│   └── MainWindow.h/cpp           # 主窗口集成
└── utils/
    └── Common.h                   # 节点类型定义
```

### 关键方法

#### MainWindow
- `setupCentralWidget()`: 创建和布局左侧面板
- `onTreeItemChanged()`: 处理树节点选择事件
- `onTreeItemDoubleClicked()`: 处理树节点双击事件

#### CustomTreeWidget
- `setupTreeStructure()`: 初始化树形结构
- `getNodeType()`: 获取节点类型
- `nodeSelected`: 节点选择信号
- `nodeDoubleClicked`: 节点双击信号

#### ParamWidget
- `showOptimizationParams()`: 显示优化参数页面
- `showInputParams()`: 显示输入参数页面
- `showOutputParams()`: 显示输出参数页面

## 使用说明

### 1. 项目导航
- 启动应用程序后，左侧会显示项目导航器
- 展开"Project"根节点查看子项目
- 点击不同节点查看对应参数

### 2. 参数配置
- 选择TreeWidget中的节点
- 在ParamWidget中修改相应参数
- 参数修改会实时反映在界面中

### 3. 详细信息查看
- 双击TreeWidget中的任意节点
- 主工作区会显示该节点的详细信息和当前配置

## 扩展性

### 添加新节点类型
1. 在`Common.h`中的`NodeType`枚举添加新类型
2. 在`CustomTreeWidget`中添加对应的图标和处理逻辑
3. 在`ParamWidget`中创建新的参数页面
4. 在`MainWindow`中添加相应的事件处理

### 自定义参数页面
1. 在`ParamWidget`中创建新的`createXXXPage()`方法
2. 设计表单布局和控件
3. 添加对应的`showXXXParams()`方法
4. 在堆叠组件中注册新页面

## 技术特点

- **模块化设计**: TreeWidget和ParamWidget独立开发，便于维护
- **信号槽通信**: 使用Qt信号槽机制实现组件间解耦
- **动态界面**: 根据选择动态切换参数界面
- **可扩展性**: 支持添加新的节点类型和参数页面
- **用户友好**: 直观的树形导航和表单式参数设置

## 日志记录

所有用户交互都会记录到日志系统中，包括:
- 节点选择事件
- 参数页面切换
- 双击事件处理

这有助于调试和用户行为分析。 