#ifndef OUTPUTWIDGET_H
#define OUTPUTWIDGET_H

#include <QWidget>
#include <QGroupBox>
#include <QFormLayout>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QTextEdit>
#include <QProgressBar>
#include <QListWidget>
#include <QTableWidget>
#include <QFileDialog>
#include <QHeaderView>

QT_BEGIN_NAMESPACE
class QLineEdit;
class QComboBox;
class QSpinBox;
class QCheckBox;
class QPushButton;
class QListWidget;
class QTextEdit;
class QProgressBar;
class QLabel;
class QTableWidget;
QT_END_NAMESPACE

namespace Ui {
class OutputWidget;
}

class OutputWidget : public QWidget
{
    Q_OBJECT

public:
    explicit OutputWidget(QWidget *parent = nullptr);
    ~OutputWidget();

    // Objective function methods
    QString getObjectiveResponse() const;
    QString getObjectiveSense() const;
    void setObjectiveResponse(const QString &response);
    void setObjectiveSense(const QString &sense);
    
    // Constraint methods
    QString getConstraintResponse() const;
    bool isConstraintEnabled() const;
    QString getUpperBound() const;
    QString getLowerBound() const;
    void setConstraintResponse(const QString &response);
    void setConstraintEnabled(bool enabled);
    void setUpperBound(const QString &bound);
    void setLowerBound(const QString &bound);
    
    // Table management methods
    void addObjectiveParameter(const QString &name, const QString &response, const QString &sense);
    void updateObjectiveParameter(int row, const QString &name, const QString &response, const QString &sense);
    void deleteObjectiveParameter(int row);
    void clearObjectiveParameters();
    
    void addConstraintParameter(const QString &name, const QString &response, const QString &upperBound, const QString &lowerBound);
    void updateConstraintParameter(int row, const QString &name, const QString &response, const QString &upperBound, const QString &lowerBound);
    void deleteConstraintParameter(int row);
    void clearConstraintParameters();
    
    // Data access methods
    QStringList getObjectiveParameterNames() const;
    QStringList getConstraintParameterNames() const;
    int getObjectiveParameterCount() const;
    int getConstraintParameterCount() const;

    // Output configuration methods
    QString getOutputDirectory() const;
    QString getFileNamePattern() const;
    QString getOutputFormat() const;
    bool isAutoSaveEnabled() const;
    int getAutoSaveInterval() const;
    bool isCompressionEnabled() const;
    QString getCompressionLevel() const;
    bool isReportEnabled() const;
    QString getReportFormat() const;
    bool includeCharts() const;
    bool includeStatistics() const;
    
    void setOutputDirectory(const QString &directory);
    void setFileNamePattern(const QString &pattern);
    void setOutputFormat(const QString &format);
    void setAutoSaveEnabled(bool enabled);
    void setAutoSaveInterval(int interval);
    void setCompressionEnabled(bool enabled);
    void setCompressionLevel(const QString &level);
    void setReportEnabled(bool enabled);
    void setReportFormat(const QString &format);
    void setIncludeCharts(bool include);
    void setIncludeStatistics(bool include);

signals:
    void parametersChanged();
    void objectiveParameterAdded(const QString &name);
    void constraintParameterAdded(const QString &name);
    void objectiveParameterRemoved(const QString &name);
    void constraintParameterRemoved(const QString &name);

public slots:
    // Objective slots
    void onObjectiveResponseChanged();
    void onObjectiveSenseChanged();
    void onAddObjectiveParameter();
    void onUpdateObjectiveParameter();
    void onDeleteObjectiveParameter();
    void onObjectiveTableSelectionChanged();
    
    // Constraint slots
    void onConstraintResponseChanged();
    void onConstraintEnabledToggled(bool enabled);
    void onUpperBoundChanged();
    void onLowerBoundChanged();
    void onAddConstraintParameter();
    void onUpdateConstraintParameter();
    void onDeleteConstraintParameter();
    void onConstraintTableSelectionChanged();

private:
    void setupConnections();
    void setupObjectiveTable();
    void setupConstraintTable();
    void updateResponseComboBoxes();
    void updateObjectiveButtonStates();
    void updateConstraintButtonStates();
    void populateResponseComboBox(QComboBox* combo);

    Ui::OutputWidget *ui;
    
    // Current selection tracking
    int m_selectedObjectiveRow;
    int m_selectedConstraintRow;
};

#endif // OUTPUTWIDGET_H 