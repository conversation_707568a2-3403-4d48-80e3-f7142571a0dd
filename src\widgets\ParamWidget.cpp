#include "ParamWidget.h"
#include "OptimizeWidget.h"
#include "SensitivityWidget.h"
#include "UQWidget.h"
#include "InputWidget.h"
#include "OutputWidget.h"
#include "SolverOutputWidget.h"
#include <QVBoxLayout>
#include <QStackedWidget>
#include "../utils/Logger.h"

ParamWidget::ParamWidget(QWidget *parent)
    : QWidget(parent)
    , m_stackedWidget(nullptr)
    , m_optimizeWidget(nullptr)
    , m_sensitivityWidget(nullptr)
    , m_uqWidget(nullptr)
    , m_inputWidget(nullptr)
    , m_outputWidget(nullptr)
    , m_solverOutputWidget(nullptr)
    , m_currentModuleWidget(nullptr)
    , m_currentModuleTitle("")
{
    initUI();
    setupConnections();
    LOG_INFO("ParamWidget initialized", "ParamWidget");
}

ParamWidget::~ParamWidget()
{
    LOG_INFO("ParamWidget destroyed", "ParamWidget");
}

void ParamWidget::initUI()
{
    // Create main layout
    QVBoxLayout* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(5, 5, 5, 5);
    mainLayout->setSpacing(5);

    // Create stacked widget
    m_stackedWidget = new QStackedWidget(this);
    mainLayout->addWidget(m_stackedWidget);

    // Create widget instances
    m_optimizeWidget = new OptimizeWidget(this);
    m_sensitivityWidget = new SensitivityWidget(this);
    m_uqWidget = new UQWidget(this);
    m_inputWidget = new InputWidget(this);
    m_outputWidget = new OutputWidget(this);
    m_solverOutputWidget = new SolverOutputWidget(this);

    // Add pages to stacked widget
    m_stackedWidget->addWidget(m_optimizeWidget);
    m_stackedWidget->addWidget(m_sensitivityWidget);
    m_stackedWidget->addWidget(m_uqWidget);
    m_stackedWidget->addWidget(m_inputWidget);
    m_stackedWidget->addWidget(m_outputWidget);
    m_stackedWidget->addWidget(m_solverOutputWidget);

    // Show optimization parameters page by default
    showOptimizationParams();
}

void ParamWidget::setupConnections()
{
    // Connect parameter change signals from each widget
    connect(m_optimizeWidget, &OptimizeWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
    connect(m_sensitivityWidget, &SensitivityWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
    connect(m_uqWidget, &UQWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
    connect(m_inputWidget, &InputWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
    connect(m_outputWidget, &OutputWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
    connect(m_solverOutputWidget, &SolverOutputWidget::parametersChanged,
            this, &ParamWidget::parametersChanged);
}

void ParamWidget::showOptimizationParams()
{
    if (m_stackedWidget && m_optimizeWidget) {
        m_stackedWidget->setCurrentWidget(m_optimizeWidget);
        LOG_INFO("Switched to optimization parameters", "ParamWidget");
    }
}

void ParamWidget::showSensitivityParams()
{
    if (m_stackedWidget && m_sensitivityWidget) {
        m_stackedWidget->setCurrentWidget(m_sensitivityWidget);
        LOG_INFO("Switched to sensitivity analysis parameters", "ParamWidget");
    }
}

void ParamWidget::showUQParams()
{
    if (m_stackedWidget && m_uqWidget) {
        m_stackedWidget->setCurrentWidget(m_uqWidget);
        LOG_INFO("Switched to UQ parameters", "ParamWidget");
    }
}

void ParamWidget::showInputParams()
{
    if (m_stackedWidget && m_inputWidget) {
        m_stackedWidget->setCurrentWidget(m_inputWidget);
        LOG_INFO("Switched to input parameters", "ParamWidget");
    }
}

void ParamWidget::showOutputParams()
{
    if (m_stackedWidget && m_outputWidget) {
        m_stackedWidget->setCurrentWidget(m_outputWidget);
        LOG_INFO("Switched to output parameters", "ParamWidget");
    }
}

void ParamWidget::showSolverOutputParams()
{
    if (m_stackedWidget && m_solverOutputWidget) {
        m_stackedWidget->setCurrentWidget(m_solverOutputWidget);
        LOG_INFO("Switched to solver output parameters", "ParamWidget");
    }
}

void ParamWidget::setModuleWidget(QWidget* widget, const QString& title)
{
    if (!widget || !m_stackedWidget) {
        return;
    }
    
    // 清除之前的模块组件编辑器
    clearModuleWidget();
    
    // 设置新的模块组件编辑器
    m_currentModuleWidget = widget;
    m_currentModuleTitle = title;
    
    // 将widget添加到stacked widget中
    widget->setParent(this);
    m_stackedWidget->addWidget(widget);
    m_stackedWidget->setCurrentWidget(widget);
    
    LOG_INFO(QString("Module widget set: %1").arg(title), "ParamWidget");
}

void ParamWidget::clearModuleWidget()
{
    if (m_currentModuleWidget && m_stackedWidget) {
        // 从stacked widget中移除当前的模块组件编辑器
        m_stackedWidget->removeWidget(m_currentModuleWidget);
        
        // 删除widget（如果它不是其他地方管理的）
        m_currentModuleWidget->deleteLater();
        m_currentModuleWidget = nullptr;
        m_currentModuleTitle.clear();
        
        // 切换回默认的优化参数页面
        showOptimizationParams();
        
        LOG_INFO("Module widget cleared", "ParamWidget");
    }
} 