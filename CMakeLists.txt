cmake_minimum_required(VERSION 3.14)

project(OptimizeApplication VERSION 1.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find Qt package
find_package(Qt5 5.14 COMPONENTS 
    Core 
    Gui 
    Widgets 
    Charts
    REQUIRED
)

# Set Qt automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Add include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/src
    ${CMAKE_CURRENT_SOURCE_DIR}/src/core
    ${CMAKE_CURRENT_SOURCE_DIR}/src/widgets
    ${CMAKE_CURRENT_SOURCE_DIR}/src/ui
    ${CMAKE_CURRENT_SOURCE_DIR}/src/utils
    ${CMAKE_CURRENT_SOURCE_DIR}/src/SARibbonBar
)

# SARibbonBar source files
set(SARIBBONBAR_SOURCES
    src/SARibbonBar/SARibbonBar.cpp
    src/SARibbonBar/SARibbonMainWindow.cpp
    src/SARibbonBar/SARibbonCategory.cpp
    src/SARibbonBar/SARibbonPannel.cpp
    src/SARibbonBar/SARibbonToolButton.cpp
    src/SARibbonBar/SARibbonActionsManager.cpp
    src/SARibbonBar/SARibbonApplicationButton.cpp
    src/SARibbonBar/SARibbonButtonGroupWidget.cpp
    src/SARibbonBar/SARibbonCategoryLayout.cpp
    src/SARibbonBar/SARibbonCheckBox.cpp
    src/SARibbonBar/SARibbonComboBox.cpp
    src/SARibbonBar/SARibbonContextCategory.cpp
    src/SARibbonBar/SARibbonControlButton.cpp
    src/SARibbonBar/SARibbonCtrlContainer.cpp
    src/SARibbonBar/SARibbonCustomizeData.cpp
    src/SARibbonBar/SARibbonCustomizeDialog.cpp
    src/SARibbonBar/SARibbonCustomizeWidget.cpp
    src/SARibbonBar/SARibbonDrawHelper.cpp
    src/SARibbonBar/SARibbonElementCreateDelegate.cpp
    src/SARibbonBar/SARibbonElementManager.cpp
    src/SARibbonBar/SARibbonGallery.cpp
    src/SARibbonBar/SARibbonGalleryGroup.cpp
    src/SARibbonBar/SARibbonGalleryItem.cpp
    src/SARibbonBar/SARibbonLineEdit.cpp
    src/SARibbonBar/SARibbonLineWidgetContainer.cpp
    src/SARibbonBar/SARibbonMenu.cpp
    src/SARibbonBar/SARibbonPannelItem.cpp
    src/SARibbonBar/SARibbonPannelLayout.cpp
    src/SARibbonBar/SARibbonPannelOptionButton.cpp
    src/SARibbonBar/SARibbonQuickAccessBar.cpp
    src/SARibbonBar/SARibbonSeparatorWidget.cpp
    src/SARibbonBar/SARibbonStackedWidget.cpp
    src/SARibbonBar/SARibbonTabBar.cpp
    src/SARibbonBar/SAFramelessHelper.cpp
    src/SARibbonBar/SAWindowButtonGroup.cpp
    src/SARibbonBar/saribbonuser.cpp
)

# SARibbonBar header files
set(SARIBBONBAR_HEADERS
    src/SARibbonBar/SARibbonBar.h
    src/SARibbonBar/SARibbonMainWindow.h
    src/SARibbonBar/SARibbonCategory.h
    src/SARibbonBar/SARibbonPannel.h
    src/SARibbonBar/SARibbonToolButton.h
    src/SARibbonBar/SARibbonActionsManager.h
    src/SARibbonBar/SARibbonApplicationButton.h
    src/SARibbonBar/SARibbonButtonGroupWidget.h
    src/SARibbonBar/SARibbonCategoryLayout.h
    src/SARibbonBar/SARibbonCheckBox.h
    src/SARibbonBar/SARibbonComboBox.h
    src/SARibbonBar/SARibbonContextCategory.h
    src/SARibbonBar/SARibbonControlButton.h
    src/SARibbonBar/SARibbonCtrlContainer.h
    src/SARibbonBar/SARibbonCustomizeData.h
    src/SARibbonBar/SARibbonCustomizeDialog.h
    src/SARibbonBar/SARibbonCustomizeWidget.h
    src/SARibbonBar/SARibbonDrawHelper.h
    src/SARibbonBar/SARibbonElementCreateDelegate.h
    src/SARibbonBar/SARibbonElementManager.h
    src/SARibbonBar/SARibbonGallery.h
    src/SARibbonBar/SARibbonGalleryGroup.h
    src/SARibbonBar/SARibbonGalleryItem.h
    src/SARibbonBar/SARibbonGlobal.h
    src/SARibbonBar/SARibbonLineEdit.h
    src/SARibbonBar/SARibbonLineWidgetContainer.h
    src/SARibbonBar/SARibbonMenu.h
    src/SARibbonBar/SARibbonPannelItem.h
    src/SARibbonBar/SARibbonPannelLayout.h
    src/SARibbonBar/SARibbonPannelOptionButton.h
    src/SARibbonBar/SARibbonQuickAccessBar.h
    src/SARibbonBar/SARibbonSeparatorWidget.h
    src/SARibbonBar/SARibbonStackedWidget.h
    src/SARibbonBar/SARibbonTabBar.h
    src/SARibbonBar/SAFramelessHelper.h
    src/SARibbonBar/SAWindowButtonGroup.h
    src/SARibbonBar/saribbonuser.h
)

# Application source files
set(APP_SOURCES
    src/main.cpp
    src/ui/MainWindow.cpp
    src/widgets/CustomTreeWidget.cpp
    src/widgets/ParamWidget.cpp
    src/widgets/OptimizeWidget.cpp
    src/widgets/InputWidget.cpp
    src/widgets/OutputWidget.cpp
    src/widgets/ChartWidget.cpp
    src/widgets/RibbonWidget.cpp
    src/core/ApplicationCore.cpp
    src/core/ConfigManager.cpp
    src/core/ThemeManager.cpp
    src/utils/Logger.cpp
    src/utils/Utils.cpp
)

# Application header files
set(APP_HEADERS
    src/ui/MainWindow.h
    src/widgets/CustomTreeWidget.h
    src/widgets/ParamWidget.h
    src/widgets/OptimizeWidget.h
    src/widgets/InputWidget.h
    src/widgets/OutputWidget.h
    src/widgets/ChartWidget.h
    src/widgets/RibbonWidget.h
    src/core/ApplicationCore.h
    src/core/ConfigManager.h
    src/core/ThemeManager.h
    src/utils/Logger.h
    src/utils/Common.h
)

# UI files
set(APP_UI_FILES
    src/widgets/OptimizeWidget.ui
    src/widgets/InputWidget.ui
    src/widgets/OutputWidget.ui
)

# Resource files
set(RESOURCE_FILES
    resources/icons.qrc
    src/SARibbonBar/resource.qrc
)

# Create executable
add_executable(${PROJECT_NAME} 
    ${APP_SOURCES}
    ${APP_HEADERS}
    ${APP_UI_FILES}
    ${RESOURCE_FILES}
    ${SARIBBONBAR_SOURCES}
    ${SARIBBONBAR_HEADERS}
)

# SARibbonBar configuration - disable DLL import/export since we're compiling sources directly
target_compile_definitions(${PROJECT_NAME} PRIVATE SA_RIBBON_BAR_NO_EXPORT)

# Link Qt libraries
target_link_libraries(${PROJECT_NAME} 
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Charts
)

# Compiler-specific settings
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE 
        /W4  # Warning level 4
        /wd4996  # Disable deprecation warnings
        /std:c++14
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
    )
endif()

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG_MODE)
    set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME "${PROJECT_NAME}_debug")
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE QT_NO_DEBUG_OUTPUT)
endif()

# Installation
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib/static
) 