CXX = g++
CXXFLAGS = -std=c++11 -I. -I/usr/include/qt5 -I/usr/include/qt5/QtCore -I/usr/include/qt5/QtWidgets -I/usr/include/qt5/QtGui
LDFLAGS = -lQt5Core -lQt5Widgets -lQt5Gui

SOURCES = test_parser.cpp src/data/InputData.cpp src/utils/Logger.cpp
OBJECTS = $(SOURCES:.cpp=.o)
TARGET = test_parser

all: $(TARGET)

$(TARGET): $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET) $(LDFLAGS)

%.o: %.cpp
	$(CXX) $(CXXFLAGS) -c $< -o $@

clean:
	rm -f $(OBJECTS) $(TARGET)

test: $(TARGET)
	./$(TARGET)

.PHONY: all clean test 