TEMPLATE     =  lib
CONFIG		 += c++11 
CONFIG		 += qt
CONFIG           += qml_debug
TARGET 		 =  SARibbonBar
QT      	 += core gui widgets
DEFINES 	 += SA_RIBBON_BAR_MAKE_LIB #定义此宏将构建库
# DEFINES 	 += SA_RIBBON_DEBUG_HELP_DRAW # 此宏将绘制辅助线用于调试
RESOURCES 	 += resource.qrc

TRANSLATIONS += ../qrc/translations/SARibbonBar_zh_CN.ts
include(./SARibbonBar.pri)

win32{
  INCLUDEPATH	+=  ./ \
                    ../ \

  Release:DESTDIR         = $$PWD/../../output/bin
  Release:MOC_DIR         = ./release/moc
  Release:RCC_DIR         = ./release/rcc
  Release:UI_DIR          = ./release/qui
  Release:OBJECTS_DIR     = ./release/obj

  Debug:CONFIG	    	+=  console
  Debug:DESTDIR         = $$PWD/../../output/bin_d
  Debug:MOC_DIR         = ./debug/moc
  Debug:RCC_DIR         = ./debug/rcc
  Debug:UI_DIR          = ./debug/qui
  Debug:OBJECTS_DIR     = ./debug/obj

  message("Windows SARibbonBar build")
}

unix{
  QMAKE_CXXFLAGS += -Wno-expansion-to-defined
  INCLUDEPATH	+=   ./ \
                     ../ \
					 
  CONFIG          += plugin
  MOC_DIR         = ./release/moc
  UI_DIR          = ./release/qui
  RCC_DIR         = ./release/rcc
  OBJECTS_DIR     = ./release/obj

  CONFIG(debug, debug|release){
    DESTDIR   = $$PWD/../../output/bin_d
    LIBS += \
  } else {
    DESTDIR   = $$PWD/../../output/bin
    LIBS += \
  }
	
  message("Linux SARibbonBar build")
}
