<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OutputWidget</class>
 <widget class="QWidget" name="OutputWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>398</width>
    <height>839</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Output Parameters</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_5">
   <item>
    <widget class="QGroupBox" name="outputPathGroup">
     <property name="title">
      <string>Objective</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_4">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_2">
        <item>
         <widget class="QLabel" name="outputDirLabel">
          <property name="text">
           <string>Response:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboResponseObj"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout_3">
        <item>
         <widget class="QLabel" name="diskSpaceTextLabel">
          <property name="text">
           <string>Sence:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="formatCombo_3">
          <item>
           <property name="text">
            <string>Maximum</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>Minimum</string>
           </property>
          </item>
         </widget>
        </item>
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <widget class="QLabel" name="statusTextLabel">
          <property name="text">
           <string>Objective Table:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTableWidget" name="tableWidget"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="buttonLayout1">
        <item>
         <widget class="QPushButton" name="btnAddParam">
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; }</string>
          </property>
          <property name="text">
           <string>Add Parameter</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnUpdateParam">
          <property name="styleSheet">
           <string>QPushButton { background-color: #2196F3; color: white; }</string>
          </property>
          <property name="text">
           <string>Update</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnDeleteParam">
          <property name="styleSheet">
           <string>QPushButton { background-color: #f44336; color: white; }</string>
          </property>
          <property name="text">
           <string>Delete</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="buttonSpacer1">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="fileSettingsGroup">
     <property name="title">
      <string>Constraints</string>
     </property>
     <layout class="QVBoxLayout" name="verticalLayout_3">
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <widget class="QLabel" name="formatLabel">
          <property name="text">
           <string>Response:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QComboBox" name="comboResponseCon"/>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QCheckBox" name="autoSaveCheck">
        <property name="text">
         <string>Use </string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <item row="0" column="1">
         <widget class="QLineEdit" name="filePatternEdit">
          <property name="text">
           <string/>
          </property>
          <property name="placeholderText">
           <string/>
          </property>
         </widget>
        </item>
        <item row="1" column="0">
         <widget class="QLabel" name="filePatternLabel_2">
          <property name="text">
           <string>Lower Bound:</string>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="filePatternEdit_2">
          <property name="text">
           <string/>
          </property>
          <property name="placeholderText">
           <string/>
          </property>
         </widget>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="filePatternLabel">
          <property name="text">
           <string>Upper Bound:</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QVBoxLayout" name="verticalLayout_2">
        <item>
         <widget class="QLabel" name="statusTextLabel_2">
          <property name="text">
           <string>Constraints Table:</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QTableWidget" name="tableWidget_2"/>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="buttonLayout1_2">
        <item>
         <widget class="QPushButton" name="btnAddParam_2">
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; }</string>
          </property>
          <property name="text">
           <string>Add Parameter</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnUpdateParam_2">
          <property name="styleSheet">
           <string>QPushButton { background-color: #2196F3; color: white; }</string>
          </property>
          <property name="text">
           <string>Update</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnDeleteParam_2">
          <property name="styleSheet">
           <string>QPushButton { background-color: #f44336; color: white; }</string>
          </property>
          <property name="text">
           <string>Delete</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="buttonSpacer1_2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
