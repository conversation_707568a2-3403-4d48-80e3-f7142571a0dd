<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>InputWidget</class>
 <widget class="QWidget" name="InputWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Variables Configuration</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QGroupBox" name="parameterInputGroup">
     <property name="title">
      <string>Parameter Input</string>
     </property>
     <layout class="QFormLayout" name="inputFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="inputFileLabel">
        <property name="text">
         <string>Parameter Name:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QComboBox" name="ParamName">
        <property name="editable">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="formatLabel">
        <property name="text">
         <string>Initial Value:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QLineEdit" name="editParamInit">
        <property name="placeholderText">
         <string>Enter initial value</string>
        </property>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="fileSizeTextLabel">
        <property name="text">
         <string>Upper Bound:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QLineEdit" name="editParamUpper">
        <property name="placeholderText">
         <string>Enter upper bound</string>
        </property>
       </widget>
      </item>
      <item row="3" column="0">
       <widget class="QLabel" name="statusTextLabel">
        <property name="text">
         <string>Lower Bound:</string>
        </property>
       </widget>
      </item>
      <item row="3" column="1">
       <widget class="QLineEdit" name="editParamLower">
        <property name="placeholderText">
         <string>Enter lower bound</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="tableGroup">
     <property name="title">
      <string>Parameters Overview</string>
     </property>
     <layout class="QVBoxLayout" name="tableLayout">
      <item>
       <widget class="QTableWidget" name="tableWidget">
        <property name="maximumSize">
         <size>
          <width>16777215</width>
          <height>200</height>
         </size>
        </property>
        <property name="alternatingRowColors">
         <bool>true</bool>
        </property>
        <property name="selectionBehavior">
         <enum>QAbstractItemView::SelectRows</enum>
        </property>
        <property name="sortingEnabled">
         <bool>true</bool>
        </property>
        <property name="cornerButtonEnabled">
         <bool>false</bool>
        </property>
        <attribute name="horizontalHeaderDefaultSectionSize">
         <number>80</number>
        </attribute>
        <attribute name="horizontalHeaderStretchLastSection">
         <bool>true</bool>
        </attribute>
        <attribute name="verticalHeaderVisible">
         <bool>false</bool>
        </attribute>
        <column>
         <property name="text">
          <string>Parameter Name</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Initial Value</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Upper Bound</string>
         </property>
        </column>
        <column>
         <property name="text">
          <string>Lower Bound</string>
         </property>
        </column>
       </widget>
      </item>
      <item>
       <layout class="QHBoxLayout" name="tableStatsLayout">
        <item>
         <widget class="QLabel" name="paramCountLabel">
          <property name="text">
           <string>Parameters: 0</string>
          </property>
          <property name="styleSheet">
           <string>color: #666;</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="tableStatsSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QLabel" name="statusLabel">
          <property name="text">
           <string>Ready</string>
          </property>
          <property name="styleSheet">
           <string>color: #666; font-style: italic;</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="controlGroup">
     <property name="title">
      <string>Parameter Control</string>
     </property>
     <layout class="QVBoxLayout" name="controlLayout">
      <item>
       <layout class="QHBoxLayout" name="buttonLayout1">
        <item>
         <widget class="QPushButton" name="btnAddParam">
          <property name="text">
           <string>Add Parameter</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #4CAF50; color: white; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnUpdateParam">
          <property name="text">
           <string>Update</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #2196F3; color: white; }</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnDeleteParam">
          <property name="text">
           <string>Delete</string>
          </property>
          <property name="styleSheet">
           <string>QPushButton { background-color: #f44336; color: white; }</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="buttonSpacer1">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="buttonLayout2">
        <item>
         <widget class="QPushButton" name="btnLoadSample">
          <property name="text">
           <string>Load Sample</string>
          </property>
          <property name="toolTip">
           <string>Load sample parameters for testing</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnClearAll">
          <property name="text">
           <string>Clear All</string>
          </property>
          <property name="toolTip">
           <string>Clear all parameters</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnValidate">
          <property name="text">
           <string>Validate</string>
          </property>
          <property name="toolTip">
           <string>Validate all parameters</string>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QPushButton" name="btnExport">
          <property name="text">
           <string>Export</string>
          </property>
          <property name="toolTip">
           <string>Export parameters to file</string>
          </property>
         </widget>
        </item>
        <item>
         <spacer name="buttonSpacer2">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
