#ifndef INPUTWIDGET_H
#define INPUTWIDGET_H

#include <QWidget>
#include <QList>
#include <QString>
#include <QFileDialog>
#include <QMessageBox>
#include <QFile>
#include <QTextStream>

QT_BEGIN_NAMESPACE
class QLineEdit;
class QComboBox;
class QTableWidget;
class QPushButton;
class QLabel;
class QGroupBox;
class QGridLayout;
class QVBoxLayout;
class QSpacerItem;
QT_END_NAMESPACE

namespace Ui {
class InputWidget;
}

struct ParamInfo {
    QString paramName;
    QString paramInit;
    QString paramUpper;
    QString paramLower;
    
    ParamInfo() = default;
    ParamInfo(const QString& name, const QString& init, const QString& upper, const QString& lower)
        : paramName(name), paramInit(init), paramUpper(upper), paramLower(lower) {}
};

class InputWidget : public QWidget
{
    Q_OBJECT

public:
    explicit InputWidget(QWidget *parent = nullptr);
    ~InputWidget();

    // Parameter management methods
    QList<ParamInfo> getParamsList() const;
    void setParamsList(const QList<ParamInfo> &paramsList);
    void addParam(const ParamInfo &param);
    void removeParam(int index);
    void updateParam(int index, const ParamInfo &param);
    void clearParams();
    
    // Utility methods
    int getParameterCount() const;
    void loadSampleData();
    void clearAllData();

signals:
    void parametersChanged();
    void parameterSelected(int index);
    void dataValidated(bool valid);

private slots:
    // UI control slots
    void onParamNameChanged();
    void onParamInitChanged();
    void onParamUpperChanged();
    void onParamLowerChanged();
    void onAddParamClicked();
    void onDeleteParamClicked();
    void onUpdateParamClicked();
    void onTableSelectionChanged();

public slots:
    void validateParameters();
    void exportParameters();

private:
    // Setup methods
    void setupConnections();
    void setupTableWidget();
    void initializeUI();
    
    // UI update methods
    void updateParamCombo();
    void updateParamFields();
    void updateTableFromParams();
    void updateCurrentParamDisplay();
    
    // Data manipulation methods
    void loadParamFromTable(int row);
    void clearParamFields();
    bool isValidParamData(const ParamInfo &param, QString &errorMsg) const;
    QString generateUniqueParamName() const;

    // Member variables
    Ui::InputWidget *ui;
    QList<ParamInfo> m_paramsList;
    int m_currentParamIndex;
    bool m_updatingUI;
};

#endif // INPUTWIDGET_H 
