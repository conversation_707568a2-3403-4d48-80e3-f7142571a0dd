@echo off
echo 正在构建OptimizeApplication项目...

REM 检查是否存在Qt环境
where qmake >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo 错误: 找不到qmake，请确保Qt环境已正确配置
    echo 请将Qt的bin目录添加到PATH环境变量中
    pause
    exit /b 1
)

REM 清理之前的构建文件
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist *.o del *.o
if exist moc_*.cpp del moc_*.cpp
if exist ui_*.h del ui_*.h

REM 运行qmake生成Makefile
echo 正在运行qmake...
qmake OptimizeApplication.pro
if %ERRORLEVEL% NEQ 0 (
    echo 错误: qmake执行失败
    pause
    exit /b 1
)

REM 检查编译器
where nmake >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo 使用MSVC编译器...
    nmake
) else (
    where mingw32-make >nul 2>nul
    if %ERRORLEVEL% EQU 0 (
        echo 使用MinGW编译器...
        mingw32-make
    ) else (
        where make >nul 2>nul
        if %ERRORLEVEL% EQU 0 (
            echo 使用make编译器...
            make
        ) else (
            echo 错误: 找不到合适的编译器 (nmake, mingw32-make, 或 make)
            pause
            exit /b 1
        )
    )
)

if %ERRORLEVEL% EQU 0 (
    echo 构建成功！
    echo 可执行文件位于: bin\OptimizeApplication.exe 或 debug\OptimizeApplication.exe
) else (
    echo 构建失败！
)

pause 