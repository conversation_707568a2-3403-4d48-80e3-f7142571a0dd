#ifndef PIPEWIDGET_H
#define PIPEWIDGET_H

#include <QWidget>
#include "../data/InputData.h"

QT_BEGIN_NAMESPACE
class QVBoxLayout;
class QHBoxLayout;
class QFormLayout;
class QLabel;
class QLineEdit;
class QSpinBox;
class QDoubleSpinBox;
class QGroupBox;
class QTableWidget;
class QPushButton;
QT_END_NAMESPACE

namespace Ui {
class PipeWidget;
}

class PipeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit PipeWidget(QWidget *parent = nullptr);
    explicit PipeWidget(const PipeComponent& pipe, QWidget *parent = nullptr);
    ~PipeWidget();

    void setPipeComponent(const PipeComponent& pipe);
    PipeComponent getPipeComponent() const;
    void updateDisplay();
    void clear();
    
    // RELAP5相关功能
    bool validateData();
    QString generateRELAP5Cards() const;
    void loadFromRELAP5Cards(const QStringList& cards);

signals:
    void dataChanged();
    void componentModified(const QString& componentId);
    void validationError(const QString& message);

private slots:
    void onBasicDataChanged();
    void onVolumeDataChanged();
    void onInitialConditionChanged();
    void addVolume();
    void removeVolume();
    void onControlFlagsChanged();
    void onJunctionFlagsChanged();
    void copyVolumeData();
    void pasteVolumeData();
    void exportToCSV();
    void importFromCSV();

private:
    void setupUI();
    void setupConnections();
    void updateVolumeTable();
    void updateInitialConditionTable();
    void updateControlFlags();
    void updateJunctionFlags();
    void validateAndHighlightErrors();
    void setupContextMenus();
    
    // 数据验证
    bool validateVolumeData(int index, QString& errorMsg);
    bool validateInitialConditions(int index, QString& errorMsg);
    
    // 数据操作
    void fillVolumeDataWithDefaults();
    void fillInitialConditionsWithDefaults();

    // UI
    Ui::PipeWidget *ui;
    
    // 数据
    PipeComponent m_pipeComponent;
    
    // 状态
    bool m_isUpdating;
    QStringList m_clipboardData;
};

#endif // PIPEWIDGET_H 