#include "OutputWidget.h"
#include "ui_OutputWidget.h"
#include <QMessageBox>
#include <QHeaderView>
#include <QInputDialog>

OutputWidget::OutputWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::OutputWidget)
    , m_selectedObjectiveRow(-1)
    , m_selectedConstraintRow(-1)
{
    ui->setupUi(this);
    setupObjectiveTable();
    setupConstraintTable();
    setupConnections();
    updateResponseComboBoxes();
    updateObjectiveButtonStates();
    updateConstraintButtonStates();
}

OutputWidget::~OutputWidget()
{
    delete ui;
}

void OutputWidget::setupConnections()
{
    // Objective connections
    connect(ui->comboResponseObj, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OutputWidget::onObjectiveResponseChanged);
    connect(ui->formatCombo_3, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OutputWidget::onObjectiveSenseChanged);
    connect(ui->btnAddParam, &QPushButton::clicked,
            this, &OutputWidget::onAddObjectiveParameter);
    connect(ui->btnUpdateParam, &QPushButton::clicked,
            this, &OutputWidget::onUpdateObjectiveParameter);
    connect(ui->btnDeleteParam, &QPushButton::clicked,
            this, &OutputWidget::onDeleteObjectiveParameter);
    connect(ui->tableWidget, &QTableWidget::itemSelectionChanged,
            this, &OutputWidget::onObjectiveTableSelectionChanged);
    
    // Constraint connections
    connect(ui->comboResponseCon, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &OutputWidget::onConstraintResponseChanged);
    connect(ui->autoSaveCheck, &QCheckBox::toggled,
            this, &OutputWidget::onConstraintEnabledToggled);
    connect(ui->filePatternEdit, &QLineEdit::textChanged,
            this, &OutputWidget::onUpperBoundChanged);
    connect(ui->filePatternEdit_2, &QLineEdit::textChanged,
            this, &OutputWidget::onLowerBoundChanged);
    connect(ui->btnAddParam_2, &QPushButton::clicked,
            this, &OutputWidget::onAddConstraintParameter);
    connect(ui->btnUpdateParam_2, &QPushButton::clicked,
            this, &OutputWidget::onUpdateConstraintParameter);
    connect(ui->btnDeleteParam_2, &QPushButton::clicked,
            this, &OutputWidget::onDeleteConstraintParameter);
    connect(ui->tableWidget_2, &QTableWidget::itemSelectionChanged,
            this, &OutputWidget::onConstraintTableSelectionChanged);
}

void OutputWidget::setupObjectiveTable()
{
    // Setup objective table headers
    ui->tableWidget->setColumnCount(3);
    QStringList objectiveHeaders;
    objectiveHeaders << "Parameter Name" << "Response" << "Sense";
    ui->tableWidget->setHorizontalHeaderLabels(objectiveHeaders);
    
    // Configure table properties
    ui->tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget->horizontalHeader()->setStretchLastSection(true);
    ui->tableWidget->setAlternatingRowColors(true);
}

void OutputWidget::setupConstraintTable()
{
    // Setup constraint table headers
    ui->tableWidget_2->setColumnCount(4);
    QStringList constraintHeaders;
    constraintHeaders << "Parameter Name" << "Response" << "Upper Bound" << "Lower Bound";
    ui->tableWidget_2->setHorizontalHeaderLabels(constraintHeaders);
    
    // Configure table properties
    ui->tableWidget_2->setSelectionBehavior(QAbstractItemView::SelectRows);
    ui->tableWidget_2->setSelectionMode(QAbstractItemView::SingleSelection);
    ui->tableWidget_2->horizontalHeader()->setStretchLastSection(true);
    ui->tableWidget_2->setAlternatingRowColors(true);
}

void OutputWidget::updateResponseComboBoxes()
{
    populateResponseComboBox(ui->comboResponseObj);
    populateResponseComboBox(ui->comboResponseCon);
}

void OutputWidget::populateResponseComboBox(QComboBox* combo)
{
    combo->clear();
    // Add common response function options
    combo->addItem("Objective Function");
    combo->addItem("Constraint Function");
    combo->addItem("Response 1");
    combo->addItem("Response 2");
    combo->addItem("Response 3");
    combo->addItem("Custom Response");
}

// Objective function methods
QString OutputWidget::getObjectiveResponse() const
{
    return ui->comboResponseObj->currentText();
}

QString OutputWidget::getObjectiveSense() const
{
    return ui->formatCombo_3->currentText();
}

void OutputWidget::setObjectiveResponse(const QString &response)
{
    int index = ui->comboResponseObj->findText(response);
    if (index >= 0) {
        ui->comboResponseObj->setCurrentIndex(index);
    }
}

void OutputWidget::setObjectiveSense(const QString &sense)
{
    int index = ui->formatCombo_3->findText(sense);
    if (index >= 0) {
        ui->formatCombo_3->setCurrentIndex(index);
    }
}

// Constraint methods
QString OutputWidget::getConstraintResponse() const
{
    return ui->comboResponseCon->currentText();
}

bool OutputWidget::isConstraintEnabled() const
{
    return ui->autoSaveCheck->isChecked();
}

QString OutputWidget::getUpperBound() const
{
    return ui->filePatternEdit->text();
}

QString OutputWidget::getLowerBound() const
{
    return ui->filePatternEdit_2->text();
}

void OutputWidget::setConstraintResponse(const QString &response)
{
    int index = ui->comboResponseCon->findText(response);
    if (index >= 0) {
        ui->comboResponseCon->setCurrentIndex(index);
    }
}

void OutputWidget::setConstraintEnabled(bool enabled)
{
    ui->autoSaveCheck->setChecked(enabled);
}

void OutputWidget::setUpperBound(const QString &bound)
{
    ui->filePatternEdit->setText(bound);
}

void OutputWidget::setLowerBound(const QString &bound)
{
    ui->filePatternEdit_2->setText(bound);
}

// Table management methods
void OutputWidget::addObjectiveParameter(const QString &name, const QString &response, const QString &sense)
{
    int row = ui->tableWidget->rowCount();
    ui->tableWidget->insertRow(row);
    
    ui->tableWidget->setItem(row, 0, new QTableWidgetItem(name));
    ui->tableWidget->setItem(row, 1, new QTableWidgetItem(response));
    ui->tableWidget->setItem(row, 2, new QTableWidgetItem(sense));
    
    emit objectiveParameterAdded(name);
    emit parametersChanged();
}

void OutputWidget::updateObjectiveParameter(int row, const QString &name, const QString &response, const QString &sense)
{
    if (row >= 0 && row < ui->tableWidget->rowCount()) {
        ui->tableWidget->setItem(row, 0, new QTableWidgetItem(name));
        ui->tableWidget->setItem(row, 1, new QTableWidgetItem(response));
        ui->tableWidget->setItem(row, 2, new QTableWidgetItem(sense));
        emit parametersChanged();
    }
}

void OutputWidget::deleteObjectiveParameter(int row)
{
    if (row >= 0 && row < ui->tableWidget->rowCount()) {
        QString name = ui->tableWidget->item(row, 0)->text();
        ui->tableWidget->removeRow(row);
        emit objectiveParameterRemoved(name);
        emit parametersChanged();
    }
}

void OutputWidget::clearObjectiveParameters()
{
    ui->tableWidget->setRowCount(0);
    emit parametersChanged();
}

void OutputWidget::addConstraintParameter(const QString &name, const QString &response, const QString &upperBound, const QString &lowerBound)
{
    int row = ui->tableWidget_2->rowCount();
    ui->tableWidget_2->insertRow(row);
    
    ui->tableWidget_2->setItem(row, 0, new QTableWidgetItem(name));
    ui->tableWidget_2->setItem(row, 1, new QTableWidgetItem(response));
    ui->tableWidget_2->setItem(row, 2, new QTableWidgetItem(upperBound));
    ui->tableWidget_2->setItem(row, 3, new QTableWidgetItem(lowerBound));
    
    emit constraintParameterAdded(name);
    emit parametersChanged();
}

void OutputWidget::updateConstraintParameter(int row, const QString &name, const QString &response, const QString &upperBound, const QString &lowerBound)
{
    if (row >= 0 && row < ui->tableWidget_2->rowCount()) {
        ui->tableWidget_2->setItem(row, 0, new QTableWidgetItem(name));
        ui->tableWidget_2->setItem(row, 1, new QTableWidgetItem(response));
        ui->tableWidget_2->setItem(row, 2, new QTableWidgetItem(upperBound));
        ui->tableWidget_2->setItem(row, 3, new QTableWidgetItem(lowerBound));
        emit parametersChanged();
    }
}

void OutputWidget::deleteConstraintParameter(int row)
{
    if (row >= 0 && row < ui->tableWidget_2->rowCount()) {
        QString name = ui->tableWidget_2->item(row, 0)->text();
        ui->tableWidget_2->removeRow(row);
        emit constraintParameterRemoved(name);
        emit parametersChanged();
    }
}

void OutputWidget::clearConstraintParameters()
{
    ui->tableWidget_2->setRowCount(0);
    emit parametersChanged();
}

// Data access methods
QStringList OutputWidget::getObjectiveParameterNames() const
{
    QStringList names;
    for (int i = 0; i < ui->tableWidget->rowCount(); ++i) {
        if (ui->tableWidget->item(i, 0)) {
            names << ui->tableWidget->item(i, 0)->text();
        }
    }
    return names;
}

QStringList OutputWidget::getConstraintParameterNames() const
{
    QStringList names;
    for (int i = 0; i < ui->tableWidget_2->rowCount(); ++i) {
        if (ui->tableWidget_2->item(i, 0)) {
            names << ui->tableWidget_2->item(i, 0)->text();
        }
    }
    return names;
}

int OutputWidget::getObjectiveParameterCount() const
{
    return ui->tableWidget->rowCount();
}

int OutputWidget::getConstraintParameterCount() const
{
    return ui->tableWidget_2->rowCount();
}

// Objective slots
void OutputWidget::onObjectiveResponseChanged()
{
    emit parametersChanged();
}

void OutputWidget::onObjectiveSenseChanged()
{
    emit parametersChanged();
}

void OutputWidget::onAddObjectiveParameter()
{
    bool ok;
    QString name = QInputDialog::getText(this, tr("Add Objective Parameter"),
                                       tr("Parameter name:"), QLineEdit::Normal,
                                       QString(), &ok);
    if (ok && !name.isEmpty()) {
        QString response = getObjectiveResponse();
        QString sense = getObjectiveSense();
        addObjectiveParameter(name, response, sense);
    }
}

void OutputWidget::onUpdateObjectiveParameter()
{
    if (m_selectedObjectiveRow >= 0) {
        bool ok;
        QString currentName = ui->tableWidget->item(m_selectedObjectiveRow, 0)->text();
        QString name = QInputDialog::getText(this, tr("Update Objective Parameter"),
                                           tr("Parameter name:"), QLineEdit::Normal,
                                           currentName, &ok);
        if (ok && !name.isEmpty()) {
            QString response = getObjectiveResponse();
            QString sense = getObjectiveSense();
            updateObjectiveParameter(m_selectedObjectiveRow, name, response, sense);
        }
    } else {
        QMessageBox::warning(this, tr("No Selection"), 
                           tr("Please select a parameter to update."));
    }
}

void OutputWidget::onDeleteObjectiveParameter()
{
    if (m_selectedObjectiveRow >= 0) {
        int ret = QMessageBox::question(this, tr("Delete Parameter"),
                                      tr("Are you sure you want to delete this parameter?"),
                                      QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            deleteObjectiveParameter(m_selectedObjectiveRow);
            m_selectedObjectiveRow = -1;
            updateObjectiveButtonStates();
        }
    } else {
        QMessageBox::warning(this, tr("No Selection"), 
                           tr("Please select a parameter to delete."));
    }
}

void OutputWidget::onObjectiveTableSelectionChanged()
{
    QList<QTableWidgetItem*> selectedItems = ui->tableWidget->selectedItems();
    if (!selectedItems.isEmpty()) {
        m_selectedObjectiveRow = selectedItems.first()->row();
    } else {
        m_selectedObjectiveRow = -1;
    }
    updateObjectiveButtonStates();
}

// Constraint slots
void OutputWidget::onConstraintResponseChanged()
{
    emit parametersChanged();
}

void OutputWidget::onConstraintEnabledToggled(bool enabled)
{
    // Enable/disable constraint input fields based on checkbox
    ui->filePatternEdit->setEnabled(enabled);
    ui->filePatternEdit_2->setEnabled(enabled);
    ui->comboResponseCon->setEnabled(enabled);
    ui->btnAddParam_2->setEnabled(enabled);
    ui->btnUpdateParam_2->setEnabled(enabled);
    ui->btnDeleteParam_2->setEnabled(enabled);
    ui->tableWidget_2->setEnabled(enabled);
    
    emit parametersChanged();
}

void OutputWidget::onUpperBoundChanged()
{
    emit parametersChanged();
}

void OutputWidget::onLowerBoundChanged()
{
    emit parametersChanged();
}

void OutputWidget::onAddConstraintParameter()
{
    if (!isConstraintEnabled()) {
        QMessageBox::warning(this, tr("Constraints Disabled"), 
                           tr("Please enable constraints first."));
        return;
    }
    
    bool ok;
    QString name = QInputDialog::getText(this, tr("Add Constraint Parameter"),
                                       tr("Parameter name:"), QLineEdit::Normal,
                                       QString(), &ok);
    if (ok && !name.isEmpty()) {
        QString response = getConstraintResponse();
        QString upperBound = getUpperBound();
        QString lowerBound = getLowerBound();
        addConstraintParameter(name, response, upperBound, lowerBound);
    }
}

void OutputWidget::onUpdateConstraintParameter()
{
    if (m_selectedConstraintRow >= 0) {
        bool ok;
        QString currentName = ui->tableWidget_2->item(m_selectedConstraintRow, 0)->text();
        QString name = QInputDialog::getText(this, tr("Update Constraint Parameter"),
                                           tr("Parameter name:"), QLineEdit::Normal,
                                           currentName, &ok);
        if (ok && !name.isEmpty()) {
            QString response = getConstraintResponse();
            QString upperBound = getUpperBound();
            QString lowerBound = getLowerBound();
            updateConstraintParameter(m_selectedConstraintRow, name, response, upperBound, lowerBound);
        }
    } else {
        QMessageBox::warning(this, tr("No Selection"), 
                           tr("Please select a parameter to update."));
    }
}

void OutputWidget::onDeleteConstraintParameter()
{
    if (m_selectedConstraintRow >= 0) {
        int ret = QMessageBox::question(this, tr("Delete Parameter"),
                                      tr("Are you sure you want to delete this parameter?"),
                                      QMessageBox::Yes | QMessageBox::No);
        if (ret == QMessageBox::Yes) {
            deleteConstraintParameter(m_selectedConstraintRow);
            m_selectedConstraintRow = -1;
            updateConstraintButtonStates();
        }
    } else {
        QMessageBox::warning(this, tr("No Selection"), 
                           tr("Please select a parameter to delete."));
    }
}

void OutputWidget::onConstraintTableSelectionChanged()
{
    QList<QTableWidgetItem*> selectedItems = ui->tableWidget_2->selectedItems();
    if (!selectedItems.isEmpty()) {
        m_selectedConstraintRow = selectedItems.first()->row();
    } else {
        m_selectedConstraintRow = -1;
    }
    updateConstraintButtonStates();
}

void OutputWidget::updateObjectiveButtonStates()
{
    bool hasSelection = (m_selectedObjectiveRow >= 0);
    ui->btnUpdateParam->setEnabled(hasSelection);
    ui->btnDeleteParam->setEnabled(hasSelection);
}

void OutputWidget::updateConstraintButtonStates()
{
    bool hasSelection = (m_selectedConstraintRow >= 0);
    bool constraintsEnabled = isConstraintEnabled();
    ui->btnUpdateParam_2->setEnabled(hasSelection && constraintsEnabled);
    ui->btnDeleteParam_2->setEnabled(hasSelection && constraintsEnabled);
}

// Output configuration methods implementation
QString OutputWidget::getOutputDirectory() const
{
    // Use a default directory or store in member variable
    return QString("./output");
}

QString OutputWidget::getFileNamePattern() const
{
    // Return pattern from UI element or default
    return ui->filePatternEdit->text().isEmpty() ? QString("result_%1") : ui->filePatternEdit->text();
}

QString OutputWidget::getOutputFormat() const
{
    // Return format from combo box or default
    return QString("dat");
}

bool OutputWidget::isAutoSaveEnabled() const
{
    return ui->autoSaveCheck->isChecked();
}

int OutputWidget::getAutoSaveInterval() const
{
    // Return default interval
    return 60;
}

bool OutputWidget::isCompressionEnabled() const
{
    // Return default compression setting
    return false;
}

QString OutputWidget::getCompressionLevel() const
{
    // Return default compression level
    return QString("medium");
}

bool OutputWidget::isReportEnabled() const
{
    // Return default report setting
    return true;
}

QString OutputWidget::getReportFormat() const
{
    // Return default report format
    return QString("html");
}

bool OutputWidget::includeCharts() const
{
    // Return default chart inclusion setting
    return true;
}

bool OutputWidget::includeStatistics() const
{
    // Return default statistics inclusion setting
    return true;
}

void OutputWidget::setOutputDirectory(const QString &directory)
{
    // Store directory setting (could be stored in member variable)
    Q_UNUSED(directory)
}

void OutputWidget::setFileNamePattern(const QString &pattern)
{
    ui->filePatternEdit->setText(pattern);
}

void OutputWidget::setOutputFormat(const QString &format)
{
    // Store format setting (could be stored in member variable)
    Q_UNUSED(format)
}

void OutputWidget::setAutoSaveEnabled(bool enabled)
{
    ui->autoSaveCheck->setChecked(enabled);
}

void OutputWidget::setAutoSaveInterval(int interval)
{
    // Store interval setting (could be stored in member variable)
    Q_UNUSED(interval)
}

void OutputWidget::setCompressionEnabled(bool enabled)
{
    // Store compression setting (could be stored in member variable)
    Q_UNUSED(enabled)
}

void OutputWidget::setCompressionLevel(const QString &level)
{
    // Store compression level setting (could be stored in member variable)
    Q_UNUSED(level)
}

void OutputWidget::setReportEnabled(bool enabled)
{
    // Store report setting (could be stored in member variable)
    Q_UNUSED(enabled)
}

void OutputWidget::setReportFormat(const QString &format)
{
    // Store report format setting (could be stored in member variable)
    Q_UNUSED(format)
}

void OutputWidget::setIncludeCharts(bool include)
{
    // Store chart inclusion setting (could be stored in member variable)
    Q_UNUSED(include)
}

void OutputWidget::setIncludeStatistics(bool include)
{
    // Store statistics inclusion setting (could be stored in member variable)
    Q_UNUSED(include)
} 