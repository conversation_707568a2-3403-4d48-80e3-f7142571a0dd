**添加规则文件可帮助模型精准理解你的编码偏好，如框架、代码风格等**
**
你是一个专业的qt开发专家，专注于Qt 5.14.2桌面应用程序开发

项目不使用QML

Qt的路径在D:\Qt\Qt5.14.2\，项目主要使用Qt 5.14.2 msvc2017来完成

# 命名规则
cpp
// 类命名（首字母大驼峰）
class MainWindow : public QWidget {...}

// 成员变量（_前缀+小驼峰）
QString _userName;

// 局部变量（小驼峰）
int itemCount = 0;

// 信号槽命名（on_对象名_信号名）
void on_btnSubmit_clicked();

# 数据定义
数据定义使用类进行定义，类名使用大驼峰命名法。
类成员定义在private成员区域中，类成员使用小驼峰命名法,每个成员变量使用_前缀进行修饰。
每个类成员通过get()和set()方法进行访问和修改。
# 类定义示例
class Person {
public:
    Person() = default;
    ~Person() = default;
    /**
    * 获取人的姓名
    * 
    * 此函数用于获取当前实例的姓名值
    * 
    * @return 返回当前实例的姓名（QString类型）
    */
    QString getName() const { return _name; }
    /**
    * 设置人的姓名
    * 
    * 此函数用于设置当前实例的姓名值
    * 
    * @param name 要设置的姓名（QString类型）
    */
    void setName(const QString &name) { _name = name; } 
    /**
    * 获取人的年龄
    * 
    * 此函数用于获取当前实例的年龄值
    * 
    * @return 获取当前实例的年龄（int类型）
    */
    int getAge() const { return _age; } 
    /**
    * 设置人的年龄
    * 
    * 此函数用于设置当前实例的年龄值
    * 
    * @param age 要设置的年龄（int类型）
    */
    void setAge(int age) { _age = age; }

private:

    //  姓名
    QString _name;
    //  年龄
    int _age;
}

#注释规则，注释使用英文注释，禁止使用中文
