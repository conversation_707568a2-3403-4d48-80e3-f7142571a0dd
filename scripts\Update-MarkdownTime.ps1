param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath,
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("update", "append", "create")]
    [string]$Action = "update",
    
    [Parameter(Mandatory=$false)]
    [ValidateSet("chinese", "iso", "us")]
    [string]$TimeFormat = "chinese"
)

# Get formatted current time
function Get-FormattedDateTime {
    param([string]$Format)
    
    $now = Get-Date
    switch ($Format) {
        "chinese" { return $now.ToString("yyyy年MM月dd日 HH:mm:ss") }
        "iso" { return $now.ToString("yyyy-MM-dd HH:mm:ss") }
        "us" { return $now.ToString("MM/dd/yyyy HH:mm:ss") }
        default { return $now.ToString("yyyy年MM月dd日 HH:mm:ss") }
    }
}

$timestamp = Get-FormattedDateTime -Format $TimeFormat

# Handle file not exist
if (-not (Test-Path $FilePath)) {
    if ($Action -eq "create") {
        $title = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)
        $content = @"
# $title

**创建时间：** $timestamp
**最后更新：** $timestamp

---

"@
        Set-Content -Path $FilePath -Value $content -Encoding UTF8
        Write-Host "Created new markdown file: $FilePath" -ForegroundColor Green
        Write-Host "Timestamp: $timestamp" -ForegroundColor Yellow
    } else {
        Write-Host "Error: File not found $FilePath" -ForegroundColor Red
        exit 1
    }
} else {
    # Handle existing file
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    
    if ($Action -eq "update") {
        Write-Host "Updating timestamp in file: $FilePath" -ForegroundColor Cyan
        
        # Find and replace existing timestamp
        $patterns = @(
            '\*\*最后更新：\*\*\s+[^\r\n]+',
            '\*\*更新时间：\*\*\s+[^\r\n]+',
            '\*\*文档更新时间：\*\*\s+[^\r\n]+'
        )
        
        $updated = $false
        foreach ($pattern in $patterns) {
            if ($content -match $pattern) {
                $content = $content -replace $pattern, "**最后更新：** $timestamp"
                $updated = $true
                Write-Host "Found and updated existing timestamp" -ForegroundColor Green
                break
            }
        }
        
        # If no existing timestamp found, add at the end
        if (-not $updated) {
            $content += "`r`n`r`n**最后更新：** $timestamp"
            Write-Host "Added new timestamp at the end" -ForegroundColor Green
        }
        
    } elseif ($Action -eq "append") {
        Write-Host "Appending timestamp to file: $FilePath" -ForegroundColor Cyan
        $content += "`r`n`r`n**最后更新：** $timestamp"
    }
    
    # Save file
    Set-Content -Path $FilePath -Value $content -Encoding UTF8
    Write-Host "Updated timestamp: $timestamp" -ForegroundColor Yellow
}

Write-Host "Operation completed." -ForegroundColor Green 