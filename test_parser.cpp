#include <QCoreApplication>
#include <QDebug>
#include "src/data/InputData.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    
    InputData inputData;
    QString testFile = "test_relap5_standard.i";
    
    qDebug() << "Testing RELAP5 standard .i file parsing...";
    qDebug() << "Loading file:" << testFile;
    
    if (inputData.loadFromIFile(testFile)) {
        qDebug() << "File loaded successfully!";
        qDebug() << "Problem title:" << inputData.problemTitle;
        qDebug() << "File name:" << inputData.fileName;
        
        // Test pipe components
        qDebug() << "\n=== Pipe Components ===";
        qDebug() << "Number of pipes:" << inputData.pipes.size();
        
        for (const auto& pipe : inputData.pipes) {
            qDebug() << "Pipe ID:" << pipe.componentId;
            qDebug() << "Pipe Name:" << pipe.componentName;
            qDebug() << "Pipe Type:" << pipe.componentType;
            qDebug() << "Number of volumes:" << pipe.numberOfVolumes;
            
            if (!pipe.volumes.isEmpty()) {
                qDebug() << "First volume data:";
                qDebug() << "  Volume:" << pipe.volumes[0];
                qDebug() << "  Length:" << pipe.lengths[0];
                qDebug() << "  Elevation:" << pipe.elevations[0];
                qDebug() << "  Roughness:" << pipe.roughness[0];
                qDebug() << "  Hydraulic Diameter:" << pipe.hydraulicDiameters[0];
                qDebug() << "  Angle:" << pipe.angles[0];
            }
            
            if (!pipe.initialConditions.isEmpty()) {
                qDebug() << "First initial condition:";
                qDebug() << "  Thermo State:" << pipe.initialConditions[0].thermodynamicState;
                qDebug() << "  Pressure:" << pipe.initialConditions[0].pressure;
                qDebug() << "  Temperature:" << pipe.initialConditions[0].temperature;
                qDebug() << "  Quality:" << pipe.initialConditions[0].quality;
                qDebug() << "  Velocity:" << pipe.initialConditions[0].velocity;
                qDebug() << "  Boron Density:" << pipe.initialConditions[0].boronDensity;
            }
        }
        
        // Test junction components
        qDebug() << "\n=== Junction Components ===";
        qDebug() << "Number of junctions:" << inputData.junctions.size();
        
        for (const auto& junction : inputData.junctions) {
            qDebug() << "Junction ID:" << junction.componentId;
            qDebug() << "Junction Name:" << junction.componentName;
            qDebug() << "Junction Type:" << junction.componentType;
            qDebug() << "From Component:" << junction.fromComponent;
            qDebug() << "To Component:" << junction.toComponent;
            qDebug() << "Area:" << junction.area;
            qDebug() << "Forward Loss:" << junction.forwardLoss;
            qDebug() << "Reverse Loss:" << junction.reverseLoss;
            qDebug() << "Flags:" << junction.flags;
            qDebug() << "Velocity Flag:" << junction.velocityFlag;
            qDebug() << "Velocity:" << junction.velocity;
            qDebug() << "Interface Velocity:" << junction.interfaceVelocity;
            qDebug() << "Quality:" << junction.quality;
        }
        
        // Test volume components
        qDebug() << "\n=== Volume Components ===";
        qDebug() << "Number of volumes:" << inputData.volumes.size();
        
        for (const auto& volume : inputData.volumes) {
            qDebug() << "Volume ID:" << volume.componentId;
            qDebug() << "Volume Name:" << volume.componentName;
            qDebug() << "Volume Type:" << volume.componentType;
            qDebug() << "Volume:" << volume.volume;
            qDebug() << "Length:" << volume.length;
            qDebug() << "Elevation:" << volume.elevation;
            qDebug() << "Angle:" << volume.angle;
            qDebug() << "Roughness:" << volume.roughness;
            qDebug() << "Hydraulic Diameter:" << volume.hydraulicDiameter;
            qDebug() << "Thermo State:" << volume.thermodynamicState;
            qDebug() << "Pressure:" << volume.pressure;
            qDebug() << "Temperature:" << volume.temperature;
            qDebug() << "Quality:" << volume.quality;
        }
        
    } else {
        qDebug() << "Failed to load file!";
        return 1;
    }
    
    return 0;
} 