# Model Module

This document contains all model related documentation organized by hierarchy.

---

## model

# model

Specifies how variables are mapped into a set of responses

**Topics**

block

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_model](model-id_model.html) | Give the model block an identifying name, in case of multiple model blocks  
Required (Choose One) | Model Type | [single](model-single.html) | A model with one of each block: variable, interface, and response  
[surrogate](model-surrogate.html) | An empirical model that is created from data or the results of a submodel  
[nested](model-nested.html) | A model whose responses are computed through the use of a sub-iterator  
[active_subspace](model-active_subspace.html) | Active (variable) subspace model  
[adapted_basis](model-adapted_basis.html) | Basis adaptation model  
[random_field](model-random_field.html) | Experimental capability to generate a random field representation. from data, from simulation runs, or from a covariance matrix. The representation may then be sampled for use as a random field input to another simulation. THIS IS AN EXPERIMENTAL CAPABILITY.  
Optional | [variables_pointer](model-variables_pointer.html) | Specify which variables block will be included with this model block  
Optional | [responses_pointer](model-responses_pointer.html) | Specify which reponses block will be used by this model block  
Optional | [hierarchical_tagging](model-hierarchical_tagging.html) | Enables hierarchical evaluation tagging  
  
**Description**

A model is comprised of a mapping from variables, through an interface, to responses.

_Model Group 1_ The type of model can be:

  * `single` (default)

  * `surrogate`

  * `nested`

  * `subspace`

  * `random_field`

The input file must specify one of these types. If the type is not specified, Dakota will assume a single model.

_Block Pointers and ID_

Each of these model types supports `variables_pointer` and `responses_pointer` strings for identifying the variables and responses specifications used in constructing the model by cross-referencing with `id_variables` and `id_responses` strings from particular variables and responses keyword specifications.

These pointers are valid for each model type since each model contains a set of variables that is mapped into a set of responses – only the specifics of the mapping differ.

Additional pointers are used for each model type for constructing the components of the variable to response mapping. As an environment specification identifies a top-level method and a method specification identifies a model, a model specification identifies variables, responses, and (for some types) interface specifications. This top-down flow specifies all of the object interrelationships.

**Examples**

The first example shows a minimal specification for a `single` model, which is the default model when no models are explicitly specified by the user.

    model
      single

The next example displays a surrogate model specification which selects a quadratic polynomial from among the global approximation methods. It uses a pointer to a design of experiments method for generating the data needed for building the global approximation, reuses any old data available for the current approximation region, and employs the first-order multiplicative approach to correcting the approximation each time correction is requested.

    model,
      id_model = 'M1'
      variables_pointer = 'V1'
      responses_pointer = 'R1'
      surrogate
        global
          polynomial quadratic
          dace_method_pointer = 'DACE'
          reuse_samples region
          correction multiplicative first_order

This example demonstrates the use of identifiers and pointers. It provides the optional model independent specifications for model identifier, variables pointer, and responses pointer as well as model dependent specifications for global surrogates (see `[global](../../usingdakota/reference/model-surrogate-global.html)`).

Finally, an advanced nested model example would be

    model
      id_model = 'M1'
      variables_pointer = 'V1'
      responses_pointer = 'R1'
      nested
        optional_interface_pointer = 'OI1'
          optional_interface_responses_pointer = 'OIR1'
         sub_method_pointer = 'SM1'
           primary_variable_mapping  = '' '' 'X'   'Y'
           secondary_variable_mapping = '' '' 'mean' 'mean'
           primary_response_mapping  = 1. 0. 0. 0. 0. 0. 0. 0. 0.
           secondary_response_mapping = 0. 0. 0. 1. 3. 0. 0. 0. 0.
                                        0. 0. 0. 0. 0. 0. 1. 3. 0.

This example illustrates controls for model identifier, variables pointer, and responses pointer and for specifying details of the nested mapping.


---

### model → active_subspace

# active_subspace

Active (variable) subspace model

**Specification**

  * _Alias:_ subspace

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [truth_model_pointer](model-active_subspace-truth_model_pointer.html) | Pointer to specify a full-space model, from which to construct a lower dimensional surrogate  
Optional | [initial_samples](model-active_subspace-initial_samples.html) | Initial number of samples for sampling-based methods  
Optional | [sample_type](model-active_subspace-sample_type.html) | Selection of sampling strategy  
Optional | [truncation_method](model-active_subspace-truncation_method.html) | Metric that estimates active subspace size  
Optional | [dimension](model-active_subspace-dimension.html) | Explicitly specify the desired subspace size  
Optional | [bootstrap_samples](model-active_subspace-bootstrap_samples.html) | Number of bootstrap replicates used in truncation metrics  
Optional | [build_surrogate](model-active_subspace-build_surrogate.html) | Construct moving least squares surrogate over active subspace  
Optional | [normalization](model-active_subspace-normalization.html) | Normalize gradient samples  
  
**Description**

A model that transforms the original model (given by `[truth_model_pointer](../../usingdakota/reference/model-active_subspace-truth_model_pointer.html)`) to one with a reduced set of variables. This reduced model is identified by iteratively sampling the gradient of the original model and performing a singular value decomposition of the gradient matrix.

_Expected Output_

A subspace model will perform an initial sampling design to identify an active subspace using one of the truncation methods.

_Usage Tips_

If the desired subspace size is not identified, consider using the explicit `[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` truncation option or one of the other truncation methods.

**Examples**

Perform an initial 100 gradient samples and use the `[bing_li](../../usingdakota/reference/model-active_subspace-truncation_method-bing_li.html)` truncation method to identify an active subspace. The truncation method uses 150 bootstrap samples to compute the Bing Li truncation metric.

    model
      subspace
        id_model = 'SUBSPACE'
        truth_model_pointer = 'FULLSPACE'
        initial_samples  100
        truncation_method bing_li
        bootstrap_samples 150

**Theory**

The idea behind active subspaces is to find directions in the input variable space in which the quantity of interest is nearly constant. After rotation of the input variables, this method can allow significant dimension reduction. Below is a brief summary of the process.

  1. Compute the gradient of the quantity of interest, \\(q = f(\mathbf{x})\\) ,

at several locations sampled from the full input space,

\\[\nabla_{\mathbf{x}} f_i = \nabla f(\mathbf{x}_i).\\]

  2. Compute the eigendecomposition of the matrix \\(\hat{\mathbf{C}}\\) ,

\\[\hat{\mathbf{C}} = \frac{1}{M}\sum_{i=1}^{M}\nabla_{\mathbf{x}} f_i\nabla_{\mathbf{x}} f_i^T = \hat{\mathbf{W}}\hat{\mathbf{\Lambda}}\hat{\mathbf{W}}^T,\\]

where \\(\hat{\mathbf{W}}\\) has eigenvectors as columns,

\\(\hat{\mathbf{\Lambda}} = \text{diag}(\hat{\lambda}_1,\:\ldots\:,\hat{\lambda}_N)\\) contains eigenvalues, and \\(N\\) is the total number of parameters.

  3. Using a `[truncation_method](../../usingdakota/reference/model-active_subspace-truncation_method.html)` or specifying a

`[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` to estimate the active subspace size, split the eigenvectors into active and inactive directions,

\\[\hat{\mathbf{W}} = \left[\hat{\mathbf{W}}_1\quad\hat{\mathbf{W}}_2\right].\\]

These eigenvectors are used to rotate the input variables.

  4. Next the input variables, \\(\mathbf{x}\\) , are expanded in terms of active and

inactive variables,

\\[\mathbf{x} = \hat{\mathbf{W}}_1\mathbf{y} + \hat{\mathbf{W}}_2\mathbf{z}.\\]

  5. A surrogate is then built as a function of the active variables,

\\[g(\mathbf{y}) \approx f(\mathbf{x})\\]

For additional information, see:

  1. Constantine, Paul G. “Active Subspaces: Emerging Ideas for Dimension Reduction in Parameter Studies”. Vol. 2. SIAM, 2015.

  2. Constantine, Paul G., Eric Dow, and Qiqi Wang. “Active subspace methods in theory and practice: Applications to kriging surfaces.” SIAM Journal on Scientific Computing 36.4 (2014): A1500-A1524.

  3. Constantine, Paul, and David Gleich. “Computing Active Subspaces.” arXiv preprint arXiv:1408.0545 (2014).


---

#### model → active_subspace → bootstrap_samples

# bootstrap_samples

Number of bootstrap replicates used in truncation metrics

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

The number of bootstrap replicates used to estimate the active subspace size.

_Default Behavior_

Use 100 replicates.


---

#### model → active_subspace → build_surrogate

# build_surrogate

Construct moving least squares surrogate over active subspace

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [refinement_samples](model-active_subspace-build_surrogate-refinement_samples.html) | Number of supplementary surrogate build samples  
  
**Description**

Once the active subspace variables have been identified, replace the fullspace truth model with a moving least squares surrogate model over the reduced variables. The surrogate is constructed using the fullspace function value samples collected during subspace identification, possibly supplemented with additional `[refinement_samples](../../usingdakota/reference/model-active_subspace-build_surrogate-refinement_samples.html)` in the fullspace.

_Default Behavior_

No surrogate is constructed by default.

**Examples**

    model
      active_subspace
        id_model = 'SUBSPACE'
        truth_model_pointer = 'FULLSPACE'
        initial_samples  100
       build_surrogate
         refinement_samples 10


---

##### model → active_subspace → build_surrogate → refinement_samples

# refinement_samples

Number of supplementary surrogate build samples

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

  * _Default:_ 0

**Description**

Augment the function value data collected during subspace identification with the specified number of `refinement_samples` to build the surrogate model.


---

#### model → active_subspace → dimension

# dimension

Explicitly specify the desired subspace size

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This control explicitly indicates the number of basis vectors to retain. The subspace model will include exactly `dimension` variables.

_Default Behavior_

Active for adapted basis. Not active for active subspace (the number of basis vectors will be chosen by one of the truncation methods).

_Usage Tips_

This control can be helpful when a a a priori studies give insight to the appropriate subspace size.


---

#### model → active_subspace → initial_samples

# initial_samples

Initial number of samples for sampling-based methods

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ model-dependent

**Description**

The `initial_samples` keyword is used to define the number of initial samples (i.e., randomly chosen sets of variable values) at which to execute a model. The initial samples may later be augmented in an iterative process.

_Default Behavior_

By default, Dakota will use the minimum number of samples required by the chosen method.

_Usage Tips_

To obtain linear sensitivities or to construct a linear response surface, at least dim+1 samples should be used, where “dim” is the number of variables. For sensitivities to quadratic terms or quadratic response surfaces, at least (dim+1)(dim+2)/2 samples are needed. For uncertainty quantification, we recommend at least 10*dim samples. For `variance_based_decomp`, we recommend hundreds to thousands of samples. Note that for `variance_based_decomp`, the number of simulations performed will be N*(dim+2).

**Examples**

    method
      sampling
        sample_type random
        initial_samples = 20
        refinement_samples = 5


---

#### model → active_subspace → normalization

# normalization

Normalize gradient samples

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Normalize By… | [mean_value](model-active_subspace-normalization-mean_value.html) | Normalize by sample mean of function values  
[mean_gradient](model-active_subspace-normalization-mean_gradient.html) | Normalize by sample mean of gradient norms  
[local_gradient](model-active_subspace-normalization-local_gradient.html) | Normalize each gradient sample by its norm  
  
**Description**

Normalize the matrix of sampled gradients before identifying subspace.

_Default Behavior_

The default is `[local_gradient](../../usingdakota/reference/model-active_subspace-normalization-local_gradient.html)`


---

##### model → active_subspace → normalization → local_gradient

# local_gradient

Normalize each gradient sample by its norm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

For each reponse function \\(f_i\\) , normalize each gradient sample \\(\nabla^j f_i\\) by its two-norm \\(\|\nabla^j f_i\|\\)


---

##### model → active_subspace → normalization → mean_gradient

# mean_gradient

Normalize by sample mean of gradient norms

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

For each reponse function \\(f_i\\) , normalize its gradient data by the mean \\(m_i\\) of the gradient two-norms taken across samples \\(j=1,...,J\\) :

\\[m_i = \frac{1}{J} \sum_{j=1}^{J}{\| \nabla^j f_i \|_2}\\]


---

##### model → active_subspace → normalization → mean_value

# mean_value

Normalize by sample mean of function values

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

For each reponse function \\(f_i\\) , normalize its gradient data by the mean \\(m_i\\) of the function values taken across samples \\(j=1,...,J\\) :

\\[m_i = \frac{1}{J} \sum_{j=1}^{J}{f_i^j}\\]


---

#### model → active_subspace → sample_type

# sample_type

Selection of sampling strategy

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ random

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Sample Type | [lhs](model-active_subspace-sample_type-lhs.html) | Uses Latin Hypercube Sampling (LHS) to sample variables  
[random](model-active_subspace-sample_type-random.html) | Uses purely random Monte Carlo sampling to sample variables  
  
**Description**

The `sample_type` keyword allows the user to select between two types of sampling: Monte Carlo (pure random) and Latin hypercube (stratified) sampling.

The incremental keywords are deprecated; instead use `samples` together with `refinement_samples`.

_Default Behavior_

If the `sample_type` keyword is present, it must be accompanied by `lhs` or `random`. In most contexts, `lhs` is the default (exception: multilevel_sampling uses Monte Carlo by default).

**Examples**

    method
      sampling
        sample_type lhs
        samples = 20
        seed = 83921


---

##### model → active_subspace → sample_type → lhs

# lhs

Uses Latin Hypercube Sampling (LHS) to sample variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `lhs` keyword invokes Latin Hypercube Sampling as the means of drawing samples of uncertain variables according to their probability distributions. This is a stratified, space-filling approach that selects variable values from a set of equi-probable bins.

_Default Behavior_

Latin Hypercube Sampling is the default sampling mode in most contexts (exception: multilevel_sampling). To explicitly specify LHS in the Dakota input file, the `lhs` keyword must appear in conjunction with the `sample_type` keyword.

_Usage Tips_

Latin Hypercube Sampling is very robust and can be applied to any problem. It is fairly effective at estimating the mean of model responses and linear correlations with a reasonably small number of samples relative to the number of variables.

**Examples**

    method
      sampling
        sample_type lhs
        samples = 20


---

##### model → active_subspace → sample_type → random

# random

Uses purely random Monte Carlo sampling to sample variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `random` keyword invokes Monte Carlo sampling as the means of drawing samples of uncertain variables according to their probability distributions.

_Default Behavior_

In most contexts, Monte Carlo sampling is not the default sampling mode (exception: multilevel_sampling). To change this behavior, the `random` keyword must be specified in conjuction with the `sample_type` keyword.

_Usage Tips_

Monte Carlo sampling is more computationally expensive than Latin Hypercube Sampling as it requires a larger number of samples to accurately estimate statistics.

**Examples**

    method
      sampling
        sample_type random
        samples = 200


---

#### model → active_subspace → truncation_method

# truncation_method

Metric that estimates active subspace size

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ constantine

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [bing_li](model-active_subspace-truncation_method-bing_li.html) | Use the Bing Li “ladle” diagnostic to truncate subspace  
Optional | [constantine](model-active_subspace-truncation_method-constantine.html) | Use the Constantine diagnostic to truncate subspace  
Optional | [energy](model-active_subspace-truncation_method-energy.html) | Truncate the subspace based on eigenvalue energy  
Optional | [cross_validation](model-active_subspace-truncation_method-cross_validation.html) | Truncate the subspace to minimize surrogate cross-validation error  
  
**Description**

Metric that controls how many basis vectors are retained in the active subspace.

_Default Behavior_

The default is to use the `[constantine](../../usingdakota/reference/model-active_subspace-truncation_method-constantine.html)` diagnostic.

_Usage Tips_

If the automated subspace identification methods do not yield desirable results, consider using the explicit `[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` truncation option.


---

##### model → active_subspace → truncation_method → bing_li

# bing_li

Use the Bing Li “ladle” diagnostic to truncate subspace

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Uses a trade-off criterion to determine where to truncate the active subspace. The criterion is a function of the eigenvalues and eigenvectors of the active subspace gradient matrix. This function compares the decrease in eigenvalue amplitude with the increase in eigenvector variability under bootstrap sampling of the gradient matrix. The active subspace size is taken to be the index of the first minimum of this quantity.

_Usage Tips_

If this automated diagnostic does not yield desirable results, consider using the explicit `[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` truncation option or one of the other truncation methods.

**Theory**

Below is a brief outline of the Bing Li method of active subspace identification. The first two steps are common to all active subspace truncation methods.

  1. Compute the gradient of the quantity of interest, \\(q = f(\mathbf{x})\\) ,

at several locations sampled from the input space,

\\[\nabla_{\mathbf{x}} f_i = \nabla f(\mathbf{x}_i).\\]

  2. Compute the eigendecomposition of the matrix \\(\hat{\mathbf{C}}\\) ,

\\[\hat{\mathbf{C}} = \frac{1}{M}\sum_{i=1}^{M}\nabla_{\mathbf{x}} f_i\nabla_{\mathbf{x}} f_i^T = \hat{\mathbf{W}}\hat{\mathbf{\Lambda}}\hat{\mathbf{W}}^T,\\]

where \\(\hat{\mathbf{W}}\\) has eigenvectors as columns,

\\(\hat{\mathbf{\Lambda}} = \text{diag}(\hat{\lambda}_1,\:\ldots\:,\hat{\lambda}_N)\\) contains eigenvalues, and \\(N\\) is the total number of parameters.

  3. Normalize the eigenvalues,

\\[\lambda_i = \frac{\hat{\lambda}_i}{\sum_j^N \hat{\lambda}_j}.\\]

  4. Use bootstrap sampling of the gradients found in step 1 to compute replicate

eigendecompositions,

\\[\hat{\mathbf{C}}_j^* = \hat{\mathbf{W}}_j^*\hat{\mathbf{\Lambda}}_j^*\left(\hat{\mathbf{W}}_j^*\right)^T.\\]

  5. Compute variability of eigenvectors,

\\[f_i^0 = \frac{1}{M_{boot}}\sum_j^{M_{boot}}\left\lbrace 1 - \left\vert\text{det}\left(\hat{\mathbf{W}}_i^T\hat{\mathbf{W}}_{j,i}^*\right)\right\vert\right\rbrace ,\\]

where \\(\hat{\mathbf{W}}_i\\) and \\(\hat{\mathbf{W}}_{j,i}^*\\) both

contain only the first \\(i\\) eigenvectors and \\(M_{boot}\\) is the number of bootstrap samples. The value of the variability at the first index, \\(f_1^0\\) , is defined as zero.

  6. Normalize the eigenvector variability,

\\[f_i = \frac{f_i^0}{\sum_j^N f_j^0}.\\]

  7. The criterion, \\(g_i\\) , is defined as,

\\[g_i = \lambda_i + f_i.\\]

  8. The index of first minimum of \\(g_i\\) is then the estimated active

subspace rank.

For additional information, see Luo, Wei, and Bing Li. “Combining eigenvalues and variation of eigenvectors for order determination.” SIAM, 2015.


---

##### model → active_subspace → truncation_method → constantine

# constantine

Use the Constantine diagnostic to truncate subspace

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Uses a criterion based on the variability of the subspace estimate. Eigenvectors are computed for bootstrap samples of the gradient matrix. The subspace size associated with the minimum distance between bootstrap eigenvectors and the nominal eigenvectors is the estimated active subspace size.

_Usage Tips_

If this automated diagnostic does not yield desirable results, consider using the explicit `[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` truncation option or one of the other truncation methods.

**Theory**

Below is a brief outline of the Constantine method of active subspace identification. The first two steps are common to all active subspace truncation methods.

  1. Compute the gradient of the quantity of interest, \\(q = f(\mathbf{x})\\) ,

at several locations sampled from the input space,

\\[\nabla_{\mathbf{x}} f_i = \nabla f(\mathbf{x}_i).\\]

  2. Compute the eigendecomposition of the matrix \\(\hat{\mathbf{C}}\\) ,

\\[\hat{\mathbf{C}} = \frac{1}{M}\sum_{i=1}^{M}\nabla_{\mathbf{x}} f_i\nabla_{\mathbf{x}} f_i^T = \hat{\mathbf{W}}\hat{\mathbf{\Lambda}}\hat{\mathbf{W}}^T,\\]

where \\(\hat{\mathbf{W}}\\) has eigenvectors as columns,

\\(\hat{\mathbf{\Lambda}} = \text{diag}(\hat{\lambda}_1,\:\ldots\:,\hat{\lambda}_N)\\) contains eigenvalues, and \\(N\\) is the total number of parameters.

  3. Use bootstrap sampling of the gradients found in step 1 to compute replicate

eigendecompositions,

\\[\hat{\mathbf{C}}_j^* = \hat{\mathbf{W}}_j^*\hat{\mathbf{\Lambda}}_j^*\left(\hat{\mathbf{W}}_j^*\right)^T.\\]

  4. Compute the average distance between nominal and bootstrap subspaces,

\\[e^*_n = \frac{1}{M_{boot}}\sum_j^{M_{boot}} \text{dist}(\text{ran}(\hat{\mathbf{W}}_n), \text{ran}(\hat{\mathbf{W}}_{j,n}^*)) = \frac{1}{M_{boot}}\sum_j^{M_{boot}} \left\| \hat{\mathbf{W}}_n\hat{\mathbf{W}}_n^T - \hat{\mathbf{W}}_{j,n}^*\left(\hat{\mathbf{W}}_{j,n}^*\right)^T\right\|,\\]

where \\(M_{boot}\\) is the number of bootstrap samples,

\\(\hat{\mathbf{W}}_n\\) and \\(\hat{\mathbf{W}}_{j,n}^*\\) both contain only the first \\(n\\) eigenvectors, and \\(n < N\\) .

  5. The estimated subspace rank, \\(r\\) , is then,

\\[r = \operatorname*{arg\,min}_n \, e^*_n.\\]

For additional information, see Constantine, Paul G. “Active Subspaces: Emerging Ideas for Dimension Reduction in Parameter Studies”. Vol. 2. SIAM, 2015.


---

##### model → active_subspace → truncation_method → cross_validation

# cross_validation

Truncate the subspace to minimize surrogate cross-validation error

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | CV Selection Criterion | [minimum](model-active_subspace-truncation_method-cross_validation-minimum.html) | Select subspace to minimize cross-validation error  
[relative](model-active_subspace-truncation_method-cross_validation-relative.html) | Choose subspace with cross-validation error less than tolerance  
[decrease](model-active_subspace-truncation_method-cross_validation-decrease.html) | Choose subspace where cross-validation error stabilizes  
Optional | [relative_tolerance](model-active_subspace-truncation_method-cross_validation-relative_tolerance.html) | Tolerance for cross-validation error value  
Optional | [decrease_tolerance](model-active_subspace-truncation_method-cross_validation-decrease_tolerance.html) | Tolerance for cross-validation error stabilization  
Optional | [max_rank](model-active_subspace-truncation_method-cross_validation-max_rank.html) | Maximum subspace dimension to consider  
Optional | [exhaustive](model-active_subspace-truncation_method-cross_validation-exhaustive.html) | Assess all admissible subspace dimensions  
  
**Description**

Select the subspace dimension that minimizes the 10-fold cross-validation error when constructing a moving least squares surrogate model.


---

###### model → active_subspace → truncation_method → cross_validation → decrease

# decrease

Choose subspace where cross-validation error stabilizes

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Select smallest subspace where change in cross-validation error to next larger subspace falls below specified tolerance.


---

###### model → active_subspace → truncation_method → cross_validation → decrease_tolerance

# decrease_tolerance

Tolerance for cross-validation error stabilization

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.0e-6

**Description**

Select smallest subspace where change in cross-validation error falls below specified tolerance.


---

###### model → active_subspace → truncation_method → cross_validation → exhaustive

# exhaustive

Assess all admissible subspace dimensions

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ on

**Description**

Assess cross-validation error for all admissible subspace dimensions, even if a particular dimension meets specified cross-validation tolerance(s).


---

###### model → active_subspace → truncation_method → cross_validation → max_rank

# max_rank

Maximum subspace dimension to consider

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ number fullspace vars

**Description**

In cross-validation, only consider subspace dimensions from 1 to `max_rank`.


---

###### model → active_subspace → truncation_method → cross_validation → minimum

# minimum

Select subspace to minimize cross-validation error

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Select the subspace dimension that mimimizes the cross-validation error across sizes considered.


---

###### model → active_subspace → truncation_method → cross_validation → relative

# relative

Choose subspace with cross-validation error less than tolerance

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Select smallest subspace with cross-validation error less than specified tolerance.


---

###### model → active_subspace → truncation_method → cross_validation → relative_tolerance

# relative_tolerance

Tolerance for cross-validation error value

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.0e-6

**Description**

Select smallest subspace where change in cross-validation error falls below specified tolerance.


---

##### model → active_subspace → truncation_method → energy

# energy

Truncate the subspace based on eigenvalue energy

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [truncation_tolerance](model-active_subspace-truncation_method-energy-truncation_tolerance.html) | Specify the maximum percentage (as a decimal) of the eigenvalue energy not captured by the active subspace representation.  
  
**Description**

Uses a criterion based on the derivative matrix eigenvalue energy.

_Usage Tips_

This subspace truncation method may work best when working with non-normally distributed uncertain variables. If this automated diagnostic does not yield desirable results, consider using the explicit `[dimension](../../usingdakota/reference/model-active_subspace-dimension.html)` truncation option or one of the other truncation methods.

**Theory**

Using the eigenvalue energy truncation metric, the subspace size is determined using the following equation:

\\[n = \inf \left\lbrace d \in \mathbf{Z} \quad\middle|\quad 1 \le d \le N \quad \wedge\quad 1 - \frac{\sum_{i = 1}^{d} \lambda_i}{\sum_{i = 1}^{N} \lambda_i} \,<\, \epsilon \right\rbrace\\]

where \\(\epsilon\\) is the `[truncation_tolerance](../../usingdakota/reference/model-active_subspace-truncation_method-energy-truncation_tolerance.html)`, \\(n\\) is the estimated subspace size, \\(N\\) is the size of the full space, and \\(\lambda_i\\) are the eigenvalues of the derivative matrix.


---

###### model → active_subspace → truncation_method → energy → truncation_tolerance

# truncation_tolerance

Specify the maximum percentage (as a decimal) of the eigenvalue energy not captured by the active subspace representation.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**


---

#### model → active_subspace → truth_model_pointer

# truth_model_pointer

Pointer to specify a full-space model, from which to construct a lower dimensional surrogate

**Topics**

block_pointer

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Description**

This must point to a model block, identified by `[id_model](../../usingdakota/reference/model-id_model.html)`. That model will be run to generate gradient data, from which an active subspace model will be identified and built.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.


---

### model → adapted_basis

# adapted_basis

Basis adaptation model

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [truth_model_pointer](model-adapted_basis-truth_model_pointer.html) | Pointer to specify a “truth” model, from which to construct a surrogate  
Optional | [truncation_tolerance](model-adapted_basis-truncation_tolerance.html) | Convergence tolerance used to identify the (reduced) dimension of the rotation matrix  
  
**Description**

A model that transforms the original model (given by `[truth_model_pointer](../../usingdakota/reference/model-adapted_basis-truth_model_pointer.html)`) to one with a rotated set of variables. The current implementation does not support the reduction of the dimension for the new model and only transform the variable set in a rotated one in which the variables are arranged in a decreasing importance order.

An initial PCE representation is built with a sparse grid method and the PCE coefficients are then used to build a rotation matrix. This matrix is also reported as a result of the code execution. The new model can subsequently be used for an UQ workflow.

_Expected Output_ A basis adaptation model will perform an initial sparse grid design to identify the rotation matrix.

**Examples**

Perform an initial sparse grid design (level 1) to evaluate the PCE expansion and evaluate the rotation matrix. Afterwards, 100 samples are generated for the model in the rotated space to obtain its statistics.

    method,
      sampling
        model_pointer = 'SUBSPACE'
        samples = 100 seed = 1234567
    
    model
      id_model = 'SUBSPACE'
      adapted_basis
        truth_model_pointer = 'FULLSPACE'
        sparse_grid_level = 1

**Theory**

The idea behind the Basis Adaptation method is to generate a PCE representation and rotate it such that the new set of rotated variables are organized in a decreasing importance ordering. Subsequently this rotation matrix can be truncated according to some criterion to only include the significant directions.

The first step of the Basis Adaptation is to compute a PCE expansion (we assume here \\(\xi\\) to have a standard multivariate Gaussian distribution)

\\[Q(\xi) = \sum_{\alpha\in\mathcal{J}_{d,p}}Q_{\alpha}\psi_{\alpha}(\xi),\\]

where \\(\alpha = (\alpha_1,...,\alpha_d) \in \mathcal{J}_{d,p}:=(\mathbb{N}_0)^d\\) with \\(|\alpha| = \sum_{i=1}^{d} \alpha_i<= d\\) is multi-index of dimension \\(d\\) and order up to \\(p\\) .

Afterwards, a multivariate Gaussian distribution \\(\eta\\) is sought such that

\\[\eta = A \xi,\\]

where \\(A\\) is an isometry such that \\(A A^\mathrm{T} = I\\) .

The basis adaptation model is obtained by expressing the original model with respect to the rotated set of variables \\(\eta\\) as

\\[{Q}^{A}(\eta) = \sum_{\beta\in\mathcal{J}_{d,p}}Q_{\beta}^{A}\psi_{\beta}(\eta).\\]

Since the basis for both the original and the adapted basis model span the same space, we know that \\({Q}^{A}(\eta(\xi)) = Q(\xi)\\) and therefore a relationships between the PCE coefficients exists

\\[Q_{\alpha} = \sum_{\beta\in\mathcal{J}_{d,p}}Q_{\beta}^{A}\langle\psi_{\beta}^{A},\psi_{\alpha}\rangle, \ \alpha\in \mathcal{J}_{d,p}.\\]

The linear adaptation strategy is used at this time in Dakota to obtain the rotation matrix \\(A\\) . The steps to obtain the matrix \\(A\\) are the following:

  1. The first variable \\(\eta_1\\) is obtained by directly combining the first order terms, that is

\\[\eta_1 = \sum_{ \alpha \in\mathcal{J}_{d,1}} Q_{ \alpha}\psi_{\alpha}(\xi) = \sum_{i=1}^{d}Q_{e_i} \xi_i,\\]

where \\(e_i\\) is the \\(d-\\) dimensional multi-index with 1 at the \\(i\\) th location and zero elsewhere.

  2. The remaining rows of the \\(A\\) matrix ( \\(A \in \mathbb{R}^{d \times d}\\) ) are obtained by using the largest coefficient, row after row, placed in the column that it occupies in the first row. All other terms are set to zero.

  3. As last step, in order to obtain an isometry, the Gram-Schmidt procedure is followed on \\(A.\\)

See the theory manual for more details.


---

#### model → adapted_basis → truncation_tolerance

# truncation_tolerance

Convergence tolerance used to identify the (reduced) dimension of the rotation matrix

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 0.8

**Description**

This convergence tolerance controls the dimension of the rotation matrix after the truncation. The metric controlled by this convergence tolerance is a quantity varying between [0,1]. If a more aggressive dimension truncation is desired, the default value (0.8) can be reduced to a lower value.

**Examples**

The following block

    model
      id_model = 'SUBSPACE'
      adapted_basis
        truth_model_pointer = 'FULLSPACE'
        sparse_grid_level = 1
        truncation_tolerance = 0.9

increases the default value (0.8) for the `truncation_tolerance` to 0.9 to obtain a rotation matrix and, consequently, a reduced model that more closely approximates the full dimensional model.


---

#### model → adapted_basis → truth_model_pointer

# truth_model_pointer

Pointer to specify a “truth” model, from which to construct a surrogate

**Topics**

block_pointer

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Expansion Basis Control | [sparse_grid_level](model-adapted_basis-truth_model_pointer-sparse_grid_level.html) | Level to use in sparse grid integration or interpolation  
[expansion_order](model-adapted_basis-truth_model_pointer-expansion_order.html) | The (initial) order of a polynomial expansion  
Optional | [dimension](model-adapted_basis-truth_model_pointer-dimension.html) | Prescribe the number of active dimensions  
Optional | [rotation_method](model-adapted_basis-truth_model_pointer-rotation_method.html) | Method used to build the rotation matrix  
  
**Description**

This must point to a model block, identified by `[id_model](../../usingdakota/reference/model-id_model.html)`. That model will be run to generate training data, from which a surrogate model will be constructed.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.


---

##### model → adapted_basis → truth_model_pointer → dimension

# dimension

Prescribe the number of active dimensions

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

If a prescribed dimension for the rotation matrix is desired, this can be specified with the keywork `dimension`. In this case, the algorithm does not use the `truncation_method` or the `truncation_tolerance` to determine the truncation of the rotation matrix, but it just selects the first \\(d\\) dimensions, where \\(d\\) is assigned by `dimension`.

**Examples**

The following block

    model
      id_model = 'SUBSPACE'
      adapted_basis
        truth_model_pointer = 'FULLSPACE'
        sparse_grid_level = 1
        dimension = 3

prescribes the selection of the first three most important directions to build the rotation matrix and the reduced model representation.


---

##### model → adapted_basis → truth_model_pointer → expansion_order

# expansion_order

The (initial) order of a polynomial expansion

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [collocation_ratio](model-adapted_basis-truth_model_pointer-expansion_order-collocation_ratio.html) | Set the number of points used to build a PCE via regression to be proportional to the number of terms in the expansion.  
  
**Description**

When the expansion_order for a a polynomial chaos expansion is specified, the coefficients may be computed by integration based on random samples or by regression using either random or sub-sampled tensor product quadrature points.

Multidimensional integration by Latin hypercube sampling

(specified with `expansion_samples`). In this case, the expansion order _p_ cannot be inferred from the numerical integration specification and it is necessary to provide an `expansion_order` to specify _p_ for a total-order expansion.

Linear regression (specified with either `collocation_points` or

`collocation_ratio`). A total-order expansion is used and must be specified using `expansion_order` as described in the previous option. To avoid requiring the user to calculate _N_ from _n_ and _p_), the `collocation_ratio` allows for specification of a constant factor applied to _N_ (e.g., `collocation_ratio` = `2`. produces samples = _2N_). In addition, the default linear relationship with _N_ can be overridden using a real-valued exponent specified using `ratio_order`. In this case, the number of samples becomes \\(cN^o\\) where \\(c\\) is the `collocation_ratio` and \\(o\\) is the `ratio_order`. The `use_derivatives` flag informs the regression approach to include derivative matching equations (limited to gradients at present) in the least squares solutions, enabling the use of fewer collocation points for a given expansion order and dimension (number of points required becomes \\(\frac{cN^o}{n+1}\\) ). When admissible, a constrained least squares approach is employed in which response values are first reproduced exactly and error in reproducing response derivatives is minimized. Two collocation grid options are supported: the default is Latin hypercube sampling (“point collocation”), and an alternate approach of “probabilistic collocation” is also available through inclusion of the `tensor_grid` keyword. In this alternate case, the collocation grid is defined using a subset of tensor-product quadrature points: the order of the tensor-product grid is selected as one more than the expansion order in each dimension (to avoid sampling at roots of the basis polynomials) and then the tensor multi-index is uniformly sampled to generate a non-repeated subset of tensor quadrature points.

If `collocation_points` or `collocation_ratio` is specified, the PCE coefficients will be determined by regression. If no regression specification is provided, appropriate defaults are defined. Specifically SVD-based least-squares will be used for solving over-determined systems and under-determined systems will be solved using LASSO. For the situation when the number of function values is smaller than the number of terms in a PCE, but the total number of samples including gradient values is greater than the number of terms, the resulting over-determined system will be solved using equality constrained least squares. Technical information on the various methods listed below can be found in the Linear regression section of the Theory Manual. Some of the regression methods (OMP, LASSO, and LARS) are able to produce a set of possible PCE coefficient vectors (see the Linear regression section in the Theory Manual). If cross validation is inactive, then only one solution, consistent with the `noise_tolerance`, will be returned. If cross validation is active, Dakota will choose between possible coefficient vectors found internally by the regression method across the set of expansion orders (1,…, `expansion_order`) and the set of specified noise tolerances and return the one with the lowest cross validation error indicator.


---

###### model → adapted_basis → truth_model_pointer → expansion_order → collocation_ratio

# collocation_ratio

Set the number of points used to build a PCE via regression to be proportional to the number of terms in the expansion.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

Set the number of points used to build a PCE via regression to be proportional to the number of terms in the expansion. To avoid requiring the user to calculate N from n and p, the collocation_ratio allows for specification of a constant factor applied to N (e.g., collocation_ratio = 2. produces samples = 2N). In addition, the default linear relationship with N can be overridden using a real-valued exponent specified using ratio_order. In this case, the number of samples becomes \\(cN^o\\) where \\(c\\) is the collocation_ratio and \\(o\\) is the ratio_order. The use_derivatives flag informs the regression approach to include derivative matching equations (limited to gradients at present) in the least squares solutions, enabling the use of fewer collocation points for a given expansion order and dimension (number of points required becomes \\(\frac{cN^o}{n+1}\\) ).


---

##### model → adapted_basis → truth_model_pointer → rotation_method

# rotation_method

Method used to build the rotation matrix

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ norm

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Rotation Method | [unranked](model-adapted_basis-truth_model_pointer-rotation_method-unranked.html) | Use the unranked method to obtain the rotation matrix  
[ranked](model-adapted_basis-truth_model_pointer-rotation_method-ranked.html) | Use the ranked method to obtain the rotation matrix  
  
**Description**

The rotation matrix for the Adapted Basis method is built starting from a matrix which includes the linear PCE coefficients. The entries of the first row are the linear PCE coefficients. For all the other rows, each row has only one nonzero entry. Depending on the way used to construct all the rows, from the second to last one, there are two implemented methods, `[unranked](../../usingdakota/reference/model-adapted_basis-truth_model_pointer-rotation_method-unranked.html)` and `[ranked](../../usingdakota/reference/model-adapted_basis-truth_model_pointer-rotation_method-ranked.html)`. A followed Gram_schmidt process is applied on the resulting matrix to make it an isometry.

_Default Behavior_

The default is to use the `[ranked](../../usingdakota/reference/model-adapted_basis-truth_model_pointer-rotation_method-ranked.html)`.

_Usage Tips_

**Examples**

The following method block

    model
      id_model = 'SUBSPACE'
      adapted_basis
        truth_model_pointer = 'FULLSPACE'
        sparse_grid_level = 1
        rotation_method = unranked

changes the default method to the `[unranked](../../usingdakota/reference/model-adapted_basis-truth_model_pointer-rotation_method-unranked.html)` method.


---

###### model → adapted_basis → truth_model_pointer → rotation_method → ranked

# ranked

Use the ranked method to obtain the rotation matrix

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In this case the construction of the \\(i\\) th row, with \\(i \geq 2\\) , of the rotation matrix is performed by putting the \\((i-1)\\) th largest linear PCE coefficient in the column that it occupies in the first row.

**Theory**

The first row of the rotation captures the complete Gaussian components and is suggested as the leading direction. By the construction in this method, the other rotated variables are the variables in the original space that have the greatest sensitivity in descending order. The particular construction makes sure that the importance of the adapted variables is in descending order, which permits the possibility of reduced representation.


---

###### model → adapted_basis → truth_model_pointer → rotation_method → unranked

# unranked

Use the unranked method to obtain the rotation matrix

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In this case the construction of the second to last rows of the rotation matrix is performed by using the value 1 in the diagonal terms and zeros elsewhere.


---

##### model → adapted_basis → truth_model_pointer → sparse_grid_level

# sparse_grid_level

Level to use in sparse grid integration or interpolation

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Multi-dimensional integration by the Smolyak sparse grid method (specified with sparse_grid_level and, optionally, dimension_preference). The underlying one-dimensional integration rules are the same as for the tensor-product quadrature case; however, the default rule selection is nested for sparse grids (Genz-Keister for normals/transformed normals and Gauss-Patterson for uniforms/transformed uniforms). This default can be overridden with an explicit non_nested specification (resulting in Gauss-Hermite for normals/transformed normals and Gauss-Legendre for uniforms/transformed uniforms). As for tensor quadrature, the dimension_preference specification enables the use of anisotropic sparse grids (refer to the PCE description in the User’s Manual for the anisotropic index set constraint definition). Similar to anisotropic tensor grids, the dimension with greatest preference will have resolution at the full sparse_grid_level and all other dimension resolutions will be reduced in proportion to their reduced preference. For PCE with either isotropic or anisotropic sparse grids, a summation of tensor-product expansions is used, where each anisotropic tensor-product quadrature rule underlying the sparse grid construction results in its own anisotropic tensor-product expansion as described in case 1. These anisotropic tensor-product expansions are summed into a sparse PCE using the standard Smolyak summation (again, refer to the User’s Manual for additional details). As for quadrature_order, the sparse_grid_level specification admits an array input for enabling specification of multiple grid resolutions used by certain advanced solution methodologies.

A corresponding sequence specification is documented at, e.g., `[sparse_grid_level_sequence](../../usingdakota/reference/method-multifidelity_polynomial_chaos-sparse_grid_level_sequence.html)` and `[sparse_grid_level_sequence](../../usingdakota/reference/method-multifidelity_stoch_collocation-sparse_grid_level_sequence.html)`


---

### model → hierarchical_tagging

# hierarchical_tagging

Enables hierarchical evaluation tagging

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no hierarchical tagging

**Description**

The hierarchical tagging option is useful for studies involving multiple models with a nested or hierarchical relationship. For example a nested model has a sub-method, which itself likely operates on a sub-model, or a hierarchical approximation involves coordination of low and high fidelity models. Specifying `hierarchical_tagging` will yield function evaluation identifiers (“tags”) composed of the evaluation IDs of the models involved, e.g., outermodel.innermodel.interfaceid = 4.9.2. This communicates the outer contexts to the analysis driver when performing a function evaluation.

**Examples**

test/dakota_uq_timeseries_ivp_optinterf.in test/dakota_uq_timeseries_sop_optinterf.in


---

### model → id_model

# id_model

Give the model block an identifying name, in case of multiple model blocks

**Topics**

block_identifier

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ method use of last model parsed

**Description**

The model identifier string is supplied with `id_model` and is used to provide a unique identifier string for use within method specifications (refer to the keyword `model_pointer` in any of the methods in the `[method](../../usingdakota/reference/method.html)` block, for example: `[model_pointer](../../usingdakota/reference/method-list_parameter_study-model_pointer.html)`)

This is used to determine which model the method will run.


---

### model → nested

# nested

A model whose responses are computed through the use of a sub-iterator

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [optional_interface_pointer](model-nested-optional_interface_pointer.html) | Pointer to interface that provides non-nested responses  
Required | [sub_method_pointer](model-nested-sub_method_pointer.html) | The `sub_method_pointer` specifies the method block for the sub-iterator  
  
**Description**

Instead of appealing directly to a primary interface, a nested model maps variables to responses by executing a secondary iterator, or a “sub-iterator”. In other words, a function evaluation of the primary study consists of a solution of an entire secondary study - potentially many secondary function evaluations.

The sub-iterator in turn operates on a sub-model. The sub-iterator responses may be combined with non-nested contributions from an optional interface specification.

A * `sub_method_pointer*` must be provided in order to specify the method block describing the sub-iterator. The remainder of the model is specified under that keyword.

A b `optional_interface_pointer` points to the interface specification and `optional_interface_responses_pointer` points to a responses specification describing the data to be returned by this interface. This interface is used to provide non-nested data, which is then combined with data from the nested iterator using the `primary_response_mapping`. (See the discussions for the `sub_method_pointer` and `optional_interface_pointer` keywords.)


---

#### model → nested → optional_interface_pointer

# optional_interface_pointer

Pointer to interface that provides non-nested responses

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no optional interface

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [optional_interface_responses_pointer](model-nested-optional_interface_pointer-optional_interface_responses_pointer.html) | Pointer to responses block that defines non-nested responses  
  
**Description**

`optional_interface_pointer` is used to specify an optional inferface (using that interface block’s `[id_interface](../../usingdakota/reference/interface-id_interface.html)` label) to provide non-nested responses to the nested model.

To generate the complete set of primary responses (response functions, objective functions, and calibration terms) expected by the nested model, primary responses from the optional interface may be combined (by addition) with responses derived from sub-iterator results, or they may be presented unchanged to the nested model. The way that Dakota treats primary responses from these two sources depends on the number of rows of and entries in the `primary_response_mapping` matrix.

To understand how, it is helpful to consider the following expression. Nested model primary responses \\(r\\) are computed from the column vector of optional interface responses \\(f\\) , the `primary_response_mapping` matrix \\(W\\) , and the column vector of results from the sub-iterator \\(s\\) .

\\[ \begin{align}\begin{aligned} \left\\{r\right\\} =\\\\\begin{split} \left\\{ \begin{array}{c} f \\\ \hline 0 \end{array} \right\\}\end{split}\\\\+\\\\\begin{split}\left\\{ \begin{array}{c} \left[ W \right] \left\\{s\right\\} \\\ \hline 0 \end{array} \right\\}\end{split}\end{aligned}\end{align} \\]

The number of rows in \\(f\\) is equal to the number of primary responses returned by the optional interface, and in \\(r\\) , the number of primary responses expected by the nested model. In this expression, \\(0\\) represents “padding” that may be needed by either the optional interface vector or the sub-iterator vector to make them the same dimension as \\(r\\) . One or the other of these quantities may be padded, but not both. In terms of the Dakota input, this means that the number of nested model primary responses must equal the larger of these two quantities: the number of optional interface responses or the number of rows in the `primary_response_mapping`.

The nested model’s secondary responses (nonlinear constraints) are the secondary responses from the optional interface augmented by the secondary responses constructed from sub-iterator results. Unlike primary responses from these two sources, which may be combined by adding, it is not possible presently to combine secondary responses. For this reason, the number of nested model’s secondary responses must equal the number of secondary responses provided by the optional interface, plus the number provided by the sub-iterator (i.e. the number of rows in the `secondary_response_mapping`). Secondary responses from the optional interface are inserted first into the nested model’s nonlinear constraints; those from the sub-iterator come after. If the nested model has secondary responses and an optional interface is used, it is necessary to specify a separate response block for the optional interface using the `optional_interface_response_pointer`. Targets and upper and lower bounds for the nonlinear constraints are taken from the nested model’s response block.

**Examples**

The first example illustrates the use of an optional interface and primary response mapping. No secondary responses are present. Assume that the inner method returns two results (suppose its the mean and standard deviation of a single response), so that the dimensions of `primary_response_mapping` will be interpreted by Dakota as 1x2, just as they are written here.

The nested model ( `nested`) expects two primary responses ( `outer_resp` block), and the optional interface returns two ( `opt_resp` block).

    model
      id_model 'nested'
      nested
        optional_interface_pointer 'opt_intf'
          optional_interface_response_pointer 'opt_resp'
        sub_method_pointer 'inner_method'
          primary_response_mapping   1.0 2.0
    
        responses_pointer 'outer_resp'
        variables_pointer 'outer_vars'
    
    responses
      id_responses 'opt_resp'
      response_functions 2
        descriptors 'opt1' 'opt2'
      no_gradients
      no_hessians
    
    responses
      id_responses 'outer_resp'
      response_functions 2
        descriptors 'out1' 'out2'
      no_gradients
      no_hessians

The first nested model response, \\(out1\\) will be computed \\(out1 = opt1 + 1.0*mean + 2.0*stddev\\) , and the second response \\(out2\\) will be equal to simply \\(opt2\\) . In the matrix form introduced above, including the implicit padding:

\\[ \begin{align}\begin{aligned}\begin{split} \left\\{ \begin{array}{c} out1 \\\ out2 \end{array} \right\\} = \left\\{ \begin{array}{c} opt1 \\\ opt2 \end{array} \right\\} +\end{split}\\\\\begin{split} \left\\{ \begin{array}{c} 1.0 dot ``mean`` + 2.0 dot ``stddev`` \\\ 0 \end{array} \right\\}\end{split}\end{aligned}\end{align} \\]

The `optional_interface_response_pointer` was not strictly needed. Because the number of responses returned by the optional interface and expected by the nested model is the same (and no secondary responses are present), the nested model’s response specification alone would have sufficed.

If it were desired to combine \\(opt2\\) with the sub-iterator’s results and set \\(out1\\) to \\(opt1\\) with no combination, then the `primary_response_mapping` matrix could be changed to:

    primary_response_mapping   0.0 0.0
                               1.0 2.0

<hr> Secondary responses will be demonstrated in the second example.

    model
      id_model 'nested'
      nested
        optional_interface_pointer 'opt_intf'
          optional_interface_response_pointer 'opt_resp'
        sub_method_pointer 'inner_method'
          primary_response_mapping   1.0 2.0
          secondary_response_mapping 1.0 0.0
        responses_pointer 'outer_resp'
        variables_pointer 'outer_vars'
    
    responses
      id_responses 'opt_resp'
      objective_functions 2
        descriptors 'opt1' 'opt2'
      nonlinear_inequality_constraints 1
        descriptors 'optc'
      no_gradients
      no_hessians
    
    responses
      id_responses 'outer_resp'
      objective_functions 2
        descriptors 'out1' 'out2'
      nonlinear_inequality_constraints 2
        descriptors 'outc1' 'outc2'
      no_gradients
      no_hessians

The nested model expects a total of two constraints. Because the `secondary_response_mapping` has one row, one of these constraints will be supplied from the sub-iterator. The other is provided by the optional interface. Secondary responses from the optional iterator come first, and so `optc` is placed in `outc1`. `outc2` takes the value computed from the sub-iterator results.


---

##### model → nested → optional_interface_pointer → optional_interface_responses_pointer

# optional_interface_responses_pointer

Pointer to responses block that defines non-nested responses

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ reuse of top-level responses specification

**Description**

`optional_interface_responses_pointer` points to the reponses block (specifically, its `[id_responses](../../usingdakota/reference/responses-id_responses.html)` label) that defines the non-nested response to return to the nested model. The `primary_response_mapping` keywords control how these non-nested respones are combined with responses from the nested sub-iterator. See the entry for `optional_interface_pointer` for a full description. If `optional_interface_responses_pointer` is not provided, the top-level. responses specification is reused.


---

#### model → nested → sub_method_pointer

# sub_method_pointer

The `sub_method_pointer` specifies the method block for the sub-iterator

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [iterator_servers](model-nested-sub_method_pointer-iterator_servers.html) | Specify the number of iterator servers when Dakota is run in parallel  
Optional | [iterator_scheduling](model-nested-sub_method_pointer-iterator_scheduling.html) | Specify the scheduling of concurrent iterators when Dakota is run in parallel  
Optional | [processors_per_iterator](model-nested-sub_method_pointer-processors_per_iterator.html) | Specify the number of processors per iterator server when Dakota is run in parallel  
Optional | [primary_variable_mapping](model-nested-sub_method_pointer-primary_variable_mapping.html) | Primary mappning of top-level variables to sub-model variables  
Optional | [secondary_variable_mapping](model-nested-sub_method_pointer-secondary_variable_mapping.html) | Secondary mappning of top-level variables to sub-model variables  
Optional | [primary_response_mapping](model-nested-sub_method_pointer-primary_response_mapping.html) | Mapping of sub-method results to top-level primary responses  
Optional | [secondary_response_mapping](model-nested-sub_method_pointer-secondary_response_mapping.html) | Mapping of sub-method results to top-level secondary responses  
Optional | [identity_response_mapping](model-nested-sub_method_pointer-identity_response_mapping.html) | Identity mapping of sub-method results to top-level responses  
  
**Description**

The `sub_method_pointer` specifies the method block for the sub-iterator.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.

A nested model performs an evaluation (variables-to-responses mapping) by running a complete sub-iterator (i.e. method) to completion. The sub-iterator, which is specified using the `sub_method_pointer`, has its own model. This model, referred to here as the sub-model, possesses its own variables and responses. Prior to every execution of the sub-iterator, information about the nested model’s variables is injected into the sub-model’s variables. After the sub-iterator has completed, its results are passed back up to the nested model as responses.

Communication of variables information from a nested model to its sub-iterator, and in the opposite direction of sub-iterator results to nested model responses, is called mapping. (This mapping must not be confused with the variables-to-responses mapping that constitutes an evaluation.) Dakota allows considerable power and flexibility in how nested model mappings are performed. They are specified using four keywords: for the variables, `primary_variable_mapping` and `secondary_variable_mapping`; and for the responses, `primary_response_mapping` and `secondary_response_mapping`. They are described below.

_Variable Mappings_

In the variable mapping case, primary and secondary variable mapping specifications are used to map from the top-level variables into the sub-model variables. These mappings support three possibilities in any combination: (1) insertion of an active top-level variable value into an identified sub-model distribution parameter for an identified active sub-model variable, (2) insertion of an active top-level variable value into an identified active sub-model variable value, and (3) addition of an active top-level variable value as an inactive sub-model variable, augmenting the active sub-model variables.

For the variable mappings, the primary and secondary specifications are lists of strings that are used to target specific sub-model variables and their sub-parameters, respectively. The primary strings are matched to sub-model variable descriptors such as `'cdv_1'` (either user-supplied or default labels). The secondary strings are matched to random variable distribution parameters such as `cdv_1'`’mean’ or `mean'`’num_trials’ or design/state variable sub-parameters such as `num_trials'`’lower_bound’ or `lower_bound'`’upper_bound’.

An important limitation is that real-valued top-level variables must map to real-valued sub-model variables or real-valued sub-parameters, and integer-valued top-level variables must map to either integer-valued sub-model variables or integer-valued sub-parameters. However, as long as these real versus integer constraints are satisfied, mappings are free to cross variable types (design, aleatory uncertain, epistemic uncertain, state) and domain types (continuous, discrete).

Both `primary_variable_mapping` and `secondary_variable_mapping` specifications are optional, which is designed to support the following three possibilities:

  1. If both primary and secondary variable mappings are specified, then an active top-level variable value will be inserted into the identified sub-parameter (the secondary mapping) for the identified sub-model variable (the primary mapping).

  2. If a primary mapping is specified but a secondary mapping is not, then an active top-level variable value will be inserted into the identified sub-model variable value (the primary mapping).

  3. If a primary mapping is not specified (corresponding secondary mappings, if specified, are ignored), then an active top-level variable value will be inserted into a corresponding sub-model variable, based on matching of variable types (e.g., top-level and sub-model variable specifications both allocate a set of ‘continuous_design’ variables which are active at the top level). Multiple sub-model variable types may be updated in this manner, provided that they are all active in the top-level variables. Since there is a direct variable correspondence for these default insertions, sub-model bounds and descriptors are also updated from the top-level bounds and labels in order to eliminate the need for redundant input file specifications. Thus, it is typical for the sub-model variables specification to only contain the minimal required information, such as the number of variables of each type, for these insertion targets. The sub-model must allocate enough space for each of the types that will accept default insertions, and the leading set of matching sub-model variables are updated (i.e., the sub-model may allocate more than needed and the trailing set will be unmodified).

These different variable mapping possibilities may be used in any combination by employing empty strings (‘’) for particular omitted mappings (the number of strings in user-supplied primary and secondary variable mapping specifications must equal the total number of active top-level variables, including both continuous and discrete types). The ordering of the active variables is the same as shown in `dakota`.input.summary on subpage input_spec_summary and as presented in the `[variables](../../usingdakota/reference/variables.html)` section of this manual.

Inactive nested model variables are treated differently from those in the active view. If inactive variables are present at the outer level, then the default type 3 mapping is used for these variables; that is, outer loop inactive variables are inserted into inner loop variables (active or inactive) based on matching of variable types, top-level bounds and labels are also propagated, the inner loop must allocate sufficient space to receive the outer loop values, and the leading subset within this inner loop allocation is updated. This capability is important for allowing nesting beyond two levels, since an active variable at the outer-most loop may become inactive at the next lower level, but still needs to be further propagated down to lower levels in the recursion.

_Response Mappings_

For the response mappings, the primary and secondary specifications determine how results from the completed sub-iterator are mapped into nested model responses. The response mapping defines a matrix which scales and combines the results from the inner loop into outer loop responses. Each row of the mapping corresponds to one outer loop response, and each column of the mapping corresponds to a result from the inner loop. The results returned from the sub-model are best thought of as forming a column vector; the nested model responses are then the dot-product of the mapping matrix and results vector. The number and type of results that are available and must be accounted for in the response mapping depends on the sub-iterator type:

optimization: the final objective function(s) and nonlinear constraints

nonlinear least squares: the final least squares terms and nonlinear constraints

aleatory uncertainty quantification (UQ): for each response function, a mean statistic, a standard deviation statistic, and all probability/reliability/generalized reliability/response level results for any user-specified `response_levels`, `probability_levels`, `reliability_levels`, and/or `gen_reliability_levels`, in that order.

epistemic and mixed aleatory/epistemic UQ using interval estimation methods: lower and upper interval bounds for each response function.

epistemic and mixed aleatory/epistemic UQ using evidence methods: for each response function, lower and upper interval bounds (belief and plausibility) for all probability/reliability/generalized reliability/response level results computed from any user-specified `response_levels`, `probability_levels`, `reliability_levels`, and/or `gen_reliability_levels`, in that order. parameter studies and design of experiments: for optimization and least squares response data sets, the best solution found (lowest constraint violation if infeasible, lowest composite objective function if feasible). For generic response data sets, a best solution metric is not defined, so the sub-iterator response vector is empty in this case.

The `primary_response_mapping` matrix maps sub-iterator results into top-level objective functions, least squares terms, or generic response functions, depending on the declared top-level response set. The `secondary_response_mapping` matrix maps sub-iterator results into top-level nonlinear inequality and equality constraints. Alternately, if all the responses of the sub-iterator are to be mapped one-to-one to the top-level nested model responses `[identity_response_mapping](../../usingdakota/reference/model-nested-sub_method_pointer-identity_response_mapping.html)` may be specified instead.

_Summary_

The nested model constructs admit a wide variety of multi-iterator, multi-model solution approaches. For example, optimization within optimization (for hierarchical multidisciplinary optimization), uncertainty quantification within uncertainty quantification (for second-order probability), uncertainty quantification within optimization (for optimization under uncertainty), and optimization within uncertainty quantification (for uncertainty of optima) are all supported, with and without surrogate model indirection. Several examples of nested model usage are provided in the Users Manual, most notably mixed epistemic-aleatory UQ, optimization under uncertainty (OUU), and surrogate-based UQ.

**Examples**

Two examples are provided to illustrate nested models. The first is a relatively simple case. Although it is somewhat contrived, it demonstrates several features of nested models. A step-by-step explanation is provided below.

    environment
      method_pointer 'opt'
    
    method
      id_method 'opt'
      asynch_pattern_search
      model_pointer 'outer_model'
      output verbose
    
    model
      id_model 'outer_model'
      variables_pointer 'outer_vars'
      responses_pointer 'outer_resps'
      nested
        sub_method_pointer 'UQ_method'
          primary_variable_mapping 'x1' 'x2'
          secondary_variable_mapping 'mean' 'mean'
          primary_response_mapping  0 0 0.3 0 0 0.7
    
    variables
      id_variables 'outer_vars'
        continuous_design 2
        descriptors 'x1_mean' 'x2_mean'
        lower_bounds -2.0 -2.0
        upper_bounds  2.0  2.0
    
    responses
      id_responses 'outer_resps'
        objective_functions 1
        descriptors 'sum_p
        no_gradients
        no_hessians
    
    method
      id_method 'UQ_method'
      sampling samples 30
      seed 1234
      model_pointer 'UQ_model'
      probability_levels 0.9 0.9
    
    model
      id_model 'UQ_model'
      single
      variables_pointer 'inner_vars'
      responses_pointer 'inner_resps'
      interface_pointer 'inf'
    
    variables
      id_variables 'inner_vars'
      normal_uncertain 2
      descriptors 'x1' 'x2'
      means 0.0 0.0
      std_deviations 1.0 1.0
    
    responses
      id_responses 'inner_resps'
      response_functions 2
      descriptors 'f1' 'f2'
      no_gradients
      no_hessians
    
    interface
      id_interface 'inf'
      direct
        analysis_drivers 'text_book'

The example input is an ‘optimization under uncertainty’ or OUU. In an OUU, some statistic of the simulation response is optimized. In this case, a weighted sum of the 90th percentiles of two simulation responses from the `text_book` driver is being minimized. The uncertainty in these responses is driven by uncertainty in the input variables, which are normally distributed. The means of the uncertain variables are the design variables in the optimization.

In Dakota, this is accomplished by nesting an uncertainty quantification method ( `[sampling](../../usingdakota/reference/method-sampling.html)`), which computes the 90th percentiles, within an optimizer ( `method-asynch_pattern_search`), which iteratively adjusts the variable means to minimize the objective. For every evaluation requested by the optimizer, the design variables ( `x1_mean` and `x2_mean` in the example) are inserted into the means of the uncertain variables ( `x1` and `x2`), then the `,sampling` study is run to completion, and finally the resulting statistics for the responses `f1` and `f2` are mapped into the responses `sum_p`.

It may be helpful to sketch out the relationships between blocks as indicated by their various pointers in order to understand the nested structure of the study. The top-level or outer method, `'opt'`, has a model named `opt',`’outer_model’. This model refers to variables and responses blocks ( `'outer_vars'` and `outer_vars'`’outer_resps’) that are used by the optimizer. The `'outer_model'` block also refers to a sub-method, `outer_model'`’UQ_method’ which defines the UQ study and has its own model, which in turn possess variables, an interface, and responses.

Next, note the primary and secondary variable mappings in `'outer_model'`, which specify that the values of the active design variables `x1_mean` and `x2_mean` are to be inserted into the means of the uncertain variables `x1` and `x2`.

The response mapping in `'outer_model'` is a matrix with a single row and six columns. The single row corresponds to the number of responses in the outer method. As described above, for each of our two sub-model responses, the `sampling` method returns a mean, a standard deviation, and a single probability level in that order to the nested model. There are a total of six results and accordingly six columns in the mapping matrix. The coefficients 0.3 and 0.7 in the matrix result in a weighted sum of the 90th percentiles:

\\[sum\\_p = 0.3*(90th\: percentile \:of \:f1) + 0.7*(90th\: percentile\: of\: f2)\\]

<hr> The next example is a fragment of an input file, showing only the nested model itself. It illustrates more complex variable and response mappings.

    primary_variable_mapping  = '' '' 'X'   'Y'
    secondary_variable_mapping = '' '' 'mean' 'mean'
    primary_response_mapping  = 1. 0. 0. 0. 0. 0. 0. 0. 0.
    secondary_response_mapping = 0. 0. 0. 1. 3. 0. 0. 0. 0.
                                 0. 0. 0. 0. 0. 0. 1. 3. 0.

The variable mappings correspond to 4 top-level variables, the first two of which employ the default mappings from active top-level variables to sub-model variables of the same type (option 3 above) and the latter two of which are inserted into the mean distribution parameters of sub-model variables `'X'` and `X'`’Y’ (option 1 above).

The response mappings define a 3 by 9 matrix corresponding to 9 inner loop results and 3 outer loop response functions (one primary response function and 2 secondary functions, such as one objective and two constraints). Each row of the response mapping is a vector which is multiplied (i.e, with a dot-product) against the 9 sub-iterator results to determine the outer loop function.

Consider again a UQ example with 3 response functions, each providing a mean, a standard deviation, and one level mapping (if no level mappings are specified, the responses would only have a mean and standard deviation). The primary response mapping can be seen to extract the first result from the inner loop, which would correspond to the mean of the first response function. This mapped sub-iterator response becomes a single objective function, least squares term, or generic response function at the outer level, as dictated by the top-level response specification. The secondary response mapping maps the fourth sub-iterator result plus 3 times the fifth sub-iterator result (mean plus 3 standard deviations) into one top-level nonlinear constraint and the seventh sub-iterator result plus 3 times the eighth sub-iterator result (mean plus 3 standard deviations) into another top-level nonlinear constraint, where these top-level nonlinear constraints may be inequality or equality, as dictated by the top-level response specification.

Note that a common case is for each sub-iterator result to be mapped to a unique outer loop response (for example, in the nested UQ case where one wants to determine an interval on each inner loop statistic). In these simple cases, the response mapping would define an identity matrix and `[identity_response_mapping](../../usingdakota/reference/model-nested-sub_method_pointer-identity_response_mapping.html)` may instead be specified.


---

##### model → nested → sub_method_pointer → identity_response_mapping

# identity_response_mapping

Identity mapping of sub-method results to top-level responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no sub-iterator contribution to nested model functions

**Description**

Specifies that the list of sub-method results (statistics, best parameters, etc.) should map one-to-one to the list of top-level nested model responses.

_Usage Tips_

To use an identity mapping, the number of top-level nested model responses (primary + secondary responses) must equal the number of sub-method results. If receiving an error about this, set `output` `verbose` and re-run the Dakota study to see a list of sub-method results responses.

The identity map may not be used in conjunction with `[optional_interface_pointer](../../usingdakota/reference/model-nested-optional_interface_pointer.html)`, nor with an explicit `primary_response_mapping` or `secondary_response_mapping`.

**Examples**

    model
      id_model 'nested'
        nested
        sub_method_pointer 'aleat'
        identity_response_mapping


---

##### model → nested → sub_method_pointer → iterator_scheduling

# iterator_scheduling

Specify the scheduling of concurrent iterators when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Server Mode | [master](model-nested-sub_method_pointer-iterator_scheduling-master.html) | Specify a dedicated master partition for parallel iterator scheduling  
[peer](model-nested-sub_method_pointer-iterator_scheduling-peer.html) | Specify a peer partition for parallel iterator scheduling  
  
**Description**

An important feature for component-based iterators is that execution of sub-iterator runs may be performed concurrently. The optional `iterator_scheduling` specification supports user override of the automatic parallel configuration for the number of iterator servers. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired number of partitions at the iterator parallelism level. Currently, `hybrid`, `multi_start`, and `pareto_set` component-based iterators support concurrency in their sub-iterators. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.


---

###### model → nested → sub_method_pointer → iterator_scheduling → master

# master

Specify a dedicated master partition for parallel iterator scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a dedicated master partition. In a dedicated master partition, one processor (the “master”) dynamically schedules work on the iterator servers. This reduces the number of processors available to create servers by 1.


---

###### model → nested → sub_method_pointer → iterator_scheduling → peer

# peer

Specify a peer partition for parallel iterator scheduling

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option overrides the Dakota parallel automatic configuration, forcing the use of a peer partition. In a peer partition, all processors are available to be assigned to iterator servers. Note that unlike the case of `evaluation_scheduling`, it is not possible to specify `static` or `dynamic`.


---

##### model → nested → sub_method_pointer → iterator_servers

# iterator_servers

Specify the number of iterator servers when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

An important feature for component-based iterators is that execution of sub-iterator runs may be performed concurrently. The optional `iterator_servers` specification supports user override of the automatic parallel configuration for the number of iterator servers. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired number of partitions at the iterator parallelism level. Currently, `hybrid`, `multi_start`, and `pareto_set` component-based iterators support concurrency in their sub-iterators. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.


---

##### model → nested → sub_method_pointer → primary_response_mapping

# primary_response_mapping

Mapping of sub-method results to top-level primary responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ no sub-iterator contribution to primary functions

**Description**

Matrix that specifies how sub-method results (statistics, best parameters, etc.) map to top-level nested model primary responses (objectives, calibration terms, or response functions). Its usage is explained in detail on the parent keyword ( `sub_method_pointer`) page.


---

##### model → nested → sub_method_pointer → primary_variable_mapping

# primary_variable_mapping

Primary mappning of top-level variables to sub-model variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ default variable insertions based on variable type

**Description**

The `primary_variable_mapping`, `secondary_variable_mapping`, `primary_response_mapping`, and `secondary_response_mapping` keywords control how top-level variables and responses are mapped to variables and responses in the sub-model. Their usage is explained on the parent keyword ( `sub_method_pointer`) page.


---

##### model → nested → sub_method_pointer → processors_per_iterator

# processors_per_iterator

Specify the number of processors per iterator server when Dakota is run in parallel

**Topics**

concurrency_and_parallelism

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

An important feature for component-based iterators is that execution of sub-iterator runs may be performed concurrently. The optional `processors_per_iterator` specification supports user override of the automatic parallel configuration for the number of processors in each iterator server. That is, if the automatic configuration is undesirable for some reason, the user can enforce a desired server size at the iterator parallelism level. Currently, `hybrid`, `multi_start`, and `pareto_set` component-based iterators support concurrency in their sub-iterators. Refer to ParallelLibrary and the [Parallel Computing section](../advanced/parallelcomputing.html#parallel) for additional information.


---

##### model → nested → sub_method_pointer → secondary_response_mapping

# secondary_response_mapping

Mapping of sub-method results to top-level secondary responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ no sub-iterator contribution to secondary functions

**Description**

Matrix that specifies how sub-method results (statistics, best parameters, etc.) map to top-level nested model secondary responses (nonlinear inequality or equality constraints). Its usage is explained in detail on the parent keyword ( `sub_method_pointer`) page.


---

##### model → nested → sub_method_pointer → secondary_variable_mapping

# secondary_variable_mapping

Secondary mappning of top-level variables to sub-model variables

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ primary mappings into sub-model variables are value-based

**Description**

The `primary_variable_mapping`, `secondary_variable_mapping`, `primary_response_mapping`, and `secondary_response_mapping` keywords control how top-level variables and responses are mapped to variables and responses in the sub-model. Their usage is explained on the parent keyword ( `sub_method_pointer`) page.


---

### model → random_field

# random_field

Experimental capability to generate a random field representation. from data, from simulation runs, or from a covariance matrix. The representation may then be sampled for use as a random field input to another simulation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [build_source](model-random_field-build_source.html) | Specify how the random field will be built: from a data file, from simulation runs, or from a covariance matrix. THIS IS AN EXPERIMENTAL CAPABILITY.  
Optional | [expansion_form](model-random_field-expansion_form.html) | Specify the form of the expansion to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.  
Optional | [expansion_bases](model-random_field-expansion_bases.html) | Specify the number of basis functions to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.  
Optional | [truncation_tolerance](model-random_field-truncation_tolerance.html) | Specify a percent of the response variance that should be captured with the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.  
Required | [propagation_model_pointer](model-random_field-propagation_model_pointer.html) | Pointer to the model that will accept realizations of the random field and use them for subsequent analysis. Typcially, this model will take the random field as inputs, e.g. a random field defining a pressure boundary or temperature boundary condition over a structure. THIS IS AN EXPERIMENTAL CAPABILITY.  
  
**Description**

Capability to generate a random field representation from data, from simulation runs, or from a covariance matrix. The random field may then be sampled for use as a random field input to another simulation. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ There are three main sections of the `random_field` model. The first section tells Dakota what data to use to build the random field. This is specified with `build_source`. The source of data to build the random field may be a file with data (where the N rows of data correspond to N samples of the random field and the M columns correspond to field values), or it may be a simulation that generates field data, or it may be specified given a mesh and a covariance matrix governing how the field varies over the mesh. In the case of using a simulation to generate field data, the simulation is defined with `dace_method_pointer`. In the case of using a mesh and a covariance, the form of the covariance is defined with `analytic_covariance`.

The next section of the random fiel model specifies the form of the expansion, `expansion_form`. This can be either a Karhunen-Loeve expansion or a Principal components analysis. These are very similar: both involve the eigenvalues of the covariance matrix of the field data. The only difference is in the treatment of the estimation of the coefficients of the eigenvector basis functions. In the PCA case, we have developed an approach which makes the coefficients explicit functions of the uncertain variables used to generate the random field. The specification of the random field can also include the number of bases to retain or a truncation tolerance, which defines the percent variance that the expansion should capture.

The final section of the random field model allows the user to specify a pointer to a model over which the random field will be propagated, meaning the model which will be driven with the random field input. This part of the specification is optional: one can build a random field but not use it in a downstream model.

**Examples**

As stated above, this is an emerging capability. The syntax currently looks like the following:

    random_field
       build_source
         rf_data_file | dace_method_pointer | analytic_covariance
       expansion_form
         karhunen_loeve | principal_components
         expansion_bases
         truncation_tolerance
       propagation_model_pointer


---

#### model → random_field → build_source

# build_source

Specify how the random field will be built: from a data file, from simulation runs, or from a covariance matrix. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Build Data Source | [rf_data_file](model-random_field-build_source-rf_data_file.html) | Specify that the random field will be built from a file of data. THIS IS AN EXPERIMENTAL CAPABILITY.  
[dace_method_pointer](model-random_field-build_source-dace_method_pointer.html) | Pointer to a DACE method for purposes of generating an ensemble of field responses to be used in estimating a random field model. THIS IS AN EXPERIMENTAL CAPABILITY.  
[analytic_covariance](model-random_field-build_source-analytic_covariance.html) | Use an analytic covariance function for the purposes of generating a random field model. THIS IS AN EXPERIMENTAL CAPABILITY.  
  
**Description**

As part of the capability to generate a random field representation, the user needs to specify the data used to generate the random field representation. This data may reside in a data file, it may be generated by running a set of simulations and generating field responses, or it may be generated by a covariance matrix defined over a mesh. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

##### model → random_field → build_source → analytic_covariance

# analytic_covariance

Use an analytic covariance function for the purposes of generating a random field model. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Covariance Kernel Form | [squared_exponential](model-random_field-build_source-analytic_covariance-squared_exponential.html) | Specify a squared exponential covariance in the case where the random field is built from an analytic covariance functio. THIS IS AN EXPERIMENTAL CAPABILITY.  
[exponential](model-random_field-build_source-analytic_covariance-exponential.html) | Specify an exponential covariance in the case where the random field is built from an analytic covariance functio. THIS IS AN EXPERIMENTAL CAPABILITY.  
  
**Description**

As part of the capability to generate a random field representation, the user needs to specify the data used to generate the random field representation. If `analytic_covariance` is specified, an analytic covariance function will be used to generate instantiations of a random field over a mesh. The form of the covariance function must be specified (e.g. `exponential` or `squared_exponential`. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

###### model → random_field → build_source → analytic_covariance → exponential

# exponential

Specify an exponential covariance in the case where the random field is built from an analytic covariance functio. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

###### model → random_field → build_source → analytic_covariance → squared_exponential

# squared_exponential

Specify a squared exponential covariance in the case where the random field is built from an analytic covariance functio. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

##### model → random_field → build_source → dace_method_pointer

# dace_method_pointer

Pointer to a DACE method for purposes of generating an ensemble of field responses to be used in estimating a random field model. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no design of experiments data

**Description**

As part of the capability to generate a random field representation, the user needs to specify the data used to generate the random field representation. One way to do this is to run a set of simulations and generating field responses. Dakota will then take the full set of field responses (e.g. multiple samples, where each sample has a field response) and construct a random field model representing the uncertainty in the ensemble. The `dace_method_pointer` is a pointer to a Design and Analysis of Computer Experiments (DACE) method, typically which is a sampling method. In this case, the sampling method should be on a simulation which can generate field responses (e.g. `field_responses`, `field_objectives`, or `field_calibration` terms.)

THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

##### model → random_field → build_source → rf_data_file

# rf_data_file

Specify that the random field will be built from a file of data. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ none

**Description**

As part of the capability to generate a random field representation, the user needs to specify the data used to generate the random field representation. In the case of `rf_data_file`, the data should reside in that file. The rows of this file represent separate samples, and the columns represent the field data. For example, if you have 100 samples of a random field of length 500, the data file will be of dimension 100 x 500. Currently, this option is not operational. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ Currently, the `build_source` that is fully working is the `dace_method_pointer`. The others are not fully operational.


---

#### model → random_field → expansion_bases

# expansion_bases

Specify the number of basis functions to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This control allows the user to specify the number of basis functions to be used in the random field representation (e.g. either a Karhunen-Loeve or Principal Components expansion). Typically, a small number of basis functions (3-5) will be sufficient to represent a significant amount of the variance in the response field. However, this depends on the particulars of the problem. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ If the user specifies neither `trunction_tolerance` nor `expansion_bases`, then the number of expansion bases that captures 95% of the variance will be used.


---

#### model → random_field → expansion_form

# expansion_form

Specify the form of the expansion to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Reduced Basis Type | [karhunen_loeve](model-random_field-expansion_form-karhunen_loeve.html) | Specify Karhunen-Loeve as the expansion form to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.  
[principal_components](model-random_field-expansion_form-principal_components.html) | Specify Principal Components as the form of the expansion to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.  
  
**Description**

This control allows the user to specify the form of the random field representation (e.g. either a Karhunen-Loeve or Principal Components expansion). THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ The default expansion form is Karhunen-Loeve.


---

##### model → random_field → expansion_form → karhunen_loeve

# karhunen_loeve

Specify Karhunen-Loeve as the expansion form to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ The default expansion form is Karhunen-Loeve.


---

##### model → random_field → expansion_form → principal_components

# principal_components

Specify Principal Components as the form of the expansion to be used in the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Principal Components and Karhunen-Loeve are very similar functional forms of the expansion. They both use basis functions which are eigenvectors of the covariance matrix of the random field data. However, in principal components, we parameterize the coefficients of the expansion to be Gaussian process models which are functions of the uncertain parameters used to generate the initial ensemble of data representing the random field. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ The default expansion form is Karhunen-Loeve.


---

#### model → random_field → propagation_model_pointer

# propagation_model_pointer

Pointer to the model that will accept realizations of the random field and use them for subsequent analysis. Typcially, this model will take the random field as inputs, e.g. a random field defining a pressure boundary or temperature boundary condition over a structure. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Description**

After a random field representation has been generated, Dakota will generate samples of that random field based on the representation. These sample realizations can then be used to drive another set of simulation analyses. The `propagation_model_pointer` is the model on which the random field realizations will be propagated. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_


---

#### model → random_field → truncation_tolerance

# truncation_tolerance

Specify a percent of the response variance that should be captured with the random field representation. THIS IS AN EXPERIMENTAL CAPABILITY.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

This control allows the user to specify the percent variance to be capture in the random field representation (e.g. either a Karhunen-Loeve or Principal Components expansion). Typically, the user would specify something like 0.9 or 0.95, where 0.9 means that 90% of the variance of the response field will be captured by the random field. THIS IS AN EXPERIMENTAL CAPABILITY UNDER ACTIVE DEVELOPMENT.

_Default Behavior_ If the user specifies neither `trunction_tolerance` nor `expansion_bases`, then the truncation tolerance is set to 0.95 and the number of expansion bases that captures 95% of the variance will be used.


---

### model → responses_pointer

# responses_pointer

Specify which reponses block will be used by this model block

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ model use of last responses parsed

**Description**

The `responses_pointer` is used to specify which responses block will be used by the model, by cross-referencing with `[id_responses](../../usingdakota/reference/responses-id_responses.html)` keyword in the `[responses](../../usingdakota/reference/responses.html)` block.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.

_Default Behavior_

When a responses pointer is not specified, the model will use the last responses block parsed from the input file.


---

### model → single

# single

A model with one of each block: variable, interface, and response

**Specification**

  * _Alias:_ simulation

  * _Arguments:_ None

  * _Default:_ N/A ( single if no model specification)

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [interface_pointer](model-single-interface_pointer.html) | Interface block pointer for the single model type  
Optional | [solution_level_control](model-single-solution_level_control.html) | Cost estimates associated with a set of solution control values.  
Optional (Choose One) | Cost model for 1 or more resolution levels | [solution_level_cost](model-single-solution_level_cost.html) | Cost estimates associated with a set of solution control values.  
[cost_recovery_metadata](model-single-cost_recovery_metadata.html) | Identify metadata by label for capturing online cost estimates  
  
**Description**

The single model is the simplest model type. It uses a single `[interface](../../usingdakota/reference/interface.html)` instance to map `[variables](../../usingdakota/reference/variables.html)` into `[responses](../../usingdakota/reference/responses.html)`. There is no recursion in this case.

The optional `[interface_pointer](../../usingdakota/reference/model-single-interface_pointer.html)` specification identifies the interface block by cross-referencing with the `id_interface` string input from a particular interface keyword specification. This is only necessary when the input file has multiple interface blocks, and you wish to explicitly point to the desired block. The same logic follows for responses and variables blocks and pointers.

**Examples**

The example shows a minimal specification for a single model, which is the default model when no models are specified by the user.

    model
      single

This example does not provide any pointer strings and therefore relies on the default behavior of constructing the model with the last variables, interface, and responses specifications parsed.


---

#### model → single → cost_recovery_metadata

# cost_recovery_metadata

Identify metadata by label for capturing online cost estimates

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Description**

Metadata can be used for a variety of response annotation purposes, including the online recovery of simulation cost (using facilities either implemented in a user’s analysis driver or codified in an embedded test driver). This information can then be used by certain algorithms that allocate resources based on relative cost, e.g. multifidelity UQ methods.

Cost estimates may be provided a priori, e.g. using `solution_level_cost`, in cases where they can be assumed fixed for each model form and/or resolution. In cases such as model tuning over a set of hyper-parameters, costs will generally be a function of these hyper-parameters and need to be captured online during initialization of the algorithm (e.g. during evaluation of the pilot sample).

_Default Behavior_ No recovery of cost estimates from the response metadata.

_Usage Tips_ It is common to employ total CPU time, aggregated across all computing cores involved in a (parallel) simulation instance. Wall time, even when the number of cores is fixed, can be subject to additional variability due to system load.

**Examples**

    model,
     id_model = 'TUNABLE_LF'
     simulation
       cost_recovery_metadata = 'core_time'
    
    responses,
     response_functions = 1
     descriptors = 'Q'
     no_gradients
     no_hessians
     metadata 'core_time'


---

#### model → single → interface_pointer

# interface_pointer

Interface block pointer for the single model type

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ model use of last interface parsed

**Description**

In the `single` model case, a single interface is used to map the variables into responses. The optional `interface_pointer` specification identifies this interface by cross-referencing with the `id_interface` string input from a particular interface keyword specification.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.

When an interface pointer is not specified, the model will use the last interface block parsed from the input file.


---

#### model → single → solution_level_control

# solution_level_control

Cost estimates associated with a set of solution control values.

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ use of single default solution level

**Description**

Simulation-based models may have an associated `solution_level_control`, which identifies a hierarchy of solution states, such as a set of mesh discretizations from coarse to fine, a set of solver tolerances from loose to tight, etc. The string specified as the `solution_level_control` identifies a discrete variable label that parameterizes the hierarchy of solution states.

_Note:_ If the discrete variable identified is a discrete set variable, then it is important to note that the variable’s set values will be ordered (lexicographically in the case of string variables), and the ordering of values provided in `solution_level_cost` should correspond to this set ordering. A common error is to provide a listing of set values that is out of order and then providing a set of costs corresponding to this misordered list – in this case, the solution level costs will be associated with the re-ordered set values.

**Examples**

In this example, integer solution control values and solution costs are naturally well ordered.

    model,
     simulation
       solution_level_control = 'mesh_size'
       solution_level_cost = 1. 8. 64. 512. 4096.
    
    variables,
     uniform_uncertain = 9
       lower_bounds      =  9*-1.
       upper_bounds      =  9* 1.
     discrete_state_set
       integer = 1
             set_values = 4 8 16 32 64
             descriptors = 'mesh_size'

In this example, string solution control values are lexicographically ordered, and care must be taken to align the solution cost estimates.

    model,
     simulation
       solution_level_control = 'mesh_size'
       solution_level_cost = 1. 64. 8. # match set ordering
    
    variables,
     uniform_uncertain = 9
       lower_bounds      =  9*-1.
       upper_bounds      =  9* 1.
     discrete_state_set
       string = 1
             set_values = 'COARSE' 'FINE' 'MEDIUM' # lexicographical ordering
             descriptors = 'mesh_size'


---

#### model → single → solution_level_cost

# solution_level_cost

Cost estimates associated with a set of solution control values.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

**Description**

Simulation-based models may have an associated `solution_level_control`, which identifies a hierarchy of solution states, such as a set of mesh discretizations from coarse to fine, a set of solver tolerances from loose to tight, etc. In algorithms that manage such a hierarchy and perform optimal resource allocation among the solution states (e.g., multilevel Monte Carlo), it is important to estimate a set of costs associated with each state. These cost estimates can be relative, such as in the example below (lowest cost normalized to 1.)

_Note:_ a scalar solution cost can be specified without an associated solution level control. This is useful when employing a hierarchy of model forms (each model has a scalar solution cost and no solution level control) instead of a hierarchy of discretization levels (one model has a vector-valued solution cost associated with multiple solution levels).

**Examples**

    model,
     simulation
       solution_level_control = 'mesh_size'
       solution_level_cost = 1. 8. 64. 512. 4096.
    
    variables,
     uniform_uncertain = 9
       lower_bounds      =  9*-1.
       upper_bounds      =  9* 1.
     discrete_state_set
       integer = 1
             set_values = 4 8 16 32 64
             descriptors = 'mesh_size'


---

### model → surrogate

# surrogate

An empirical model that is created from data or the results of a submodel

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_surrogates](model-surrogate-id_surrogates.html) | Identifies the subset of the response functions by number that are to be approximated (the default is all functions).  
Required (Choose One) | Surrogate Category | [global](model-surrogate-global.html) | Select a surrogate model with global support  
[multipoint](model-surrogate-multipoint.html) | Construct a surrogate from multiple existing training points  
[local](model-surrogate-local.html) | Build a locally accurate surrogate from data at a single point  
[ensemble](model-surrogate-ensemble.html) | Ensemble surrogates employ a collection of lower-fidelity models to approximate a truth reference model at reduced cost.  
  
**Description**

Surrogate models are inexpensive approximate models that are intended to capture the salient features of an expensive high-fidelity model. They can be used to explore the variations in response quantities over regions of the parameter space, or they can serve as inexpensive stand-ins for optimization or uncertainty quantification studies (see, for example, the surrogate-based optimization methods, `[surrogate_based_global](../../usingdakota/reference/method-surrogate_based_global.html)` and `[surrogate_based_local](../../usingdakota/reference/method-surrogate_based_local.html)`).

Surrogate models supported in Dakota are categorized as Data Fitting or Ensemble, as shown below. Each of these surrogate types provides an approximate representation of a “truth” model which is used to perform the parameter to response mappings. This approximation is built and updated using results from the truth model, called the “training data”.

  * Data fits: _Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._ Data fitting methods involve construction of an approximation or surrogate model using data (response values, gradients, and Hessians) generated from the original truth model. Data fit methods can be further categorized as local, multipoint, and global approximation techniques, based on the number of points used in generating the data fit.

    1. Local: built from response data from a single point in parameter space

>     * Taylor series expansion: `[taylor_series](../../usingdakota/reference/model-surrogate-local-taylor_series.html)` Training data consists of a single point, plus gradient and Hessian information.

    2. Multipoint: built from two or more points in parameter space, often involving the current and previous iterates of a minimization algorithm.

>     * TANA-3: `[tana](../../usingdakota/reference/model-surrogate-multipoint-tana.html)` Training Data comes from a few previously evaluated points

    3. Global full space response surface methods:

>     * Polynomial regression: `[polynomial](../../usingdakota/reference/model-surrogate-global-polynomial.html)`
> 
>     * Gaussian process (Kriging): `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)`
> 
>     * Artifical neutral network: `[neural_network](../../usingdakota/reference/model-surrogate-global-neural_network.html)`
> 
>     * MARS: `[mars](../../usingdakota/reference/model-surrogate-global-mars.html)`
> 
>     * Radial Basis Functions: `[radial_basis](../../usingdakota/reference/model-surrogate-global-radial_basis.html)`
> 
>     * Orthogonal polynomials (only supported in PCE/SC for now): `[polynomial_chaos](../../usingdakota/reference/method-polynomial_chaos.html)` and `[stoch_collocation](../../usingdakota/reference/method-stoch_collocation.html)` Training data is generated using either a design of experiments method applied to the truth model ( specified by `[dace_method_pointer](../../usingdakota/reference/model-surrogate-global-dace_method_pointer.html)`), or from saved data (specified by `[reuse_points](../../usingdakota/reference/model-surrogate-global-reuse_points.html)` ) in a restart database, or an import file.

  * Multifidelity/ensemble: Multifidelity modeling involves the use of a low-fidelity physics-based model as a surrogate for the original high-fidelity model. The low-fidelity model typically involves a coarser mesh, looser convergence tolerances, reduced element order, or omitted physics. See `[ensemble](../../usingdakota/reference/model-surrogate-ensemble.html)`.

The global and ensemble surrogates have a correction feature in order to improve the local accuracy of the surrogate models. The correction factors force the surrogate models to match the true function values and possibly true function derivatives at the center point of each trust region.

Details can be found on global `[correction](../../usingdakota/reference/model-surrogate-global-correction.html)` or ensemble `[correction](../../usingdakota/reference/model-surrogate-ensemble-ordered_model_fidelities-correction.html)`.

**Theory**

Surrogate models are used extensively in the surrogate-based optimization and least squares methods, in which the goals are to reduce expense by minimizing the number of truth function evaluations and to smooth out noisy data with a global data fit. However, the use of surrogate models is not restricted to optimization techniques; uncertainty quantification and optimization under uncertainty methods are other primary users.

_Data Fit Surrogate Models_

A surrogate of the {em data fit} type is a non-physics-based approximation typically involving interpolation or regression of a set of data generated from the original model. Data fit surrogates can be further characterized by the number of data points used in the fit, where a local approximation (e.g., first or second-order Taylor series) uses data from a single point, a multipoint approximation (e.g., two-point exponential approximations (TPEA) or two-point adaptive nonlinearity approximations (TANA)) uses a small number of data points often drawn from the previous iterates of a particular algorithm, and a global approximation (e.g., polynomial response surfaces, kriging/gaussian_process, neural networks, radial basis functions, splines) uses a set of data points distributed over the domain of interest, often generated using a design of computer experiments.

Dakota contains several types of surface fitting methods that can be used with optimization and uncertainty quantification methods and strategies such as surrogate-based optimization and optimization under uncertainty. These are: polynomial models (linear, quadratic, and cubic), first-order Taylor series expansion, kriging spatial interpolation, artificial neural networks, multivariate adaptive regression splines, radial basis functions, and moving least squares. With the exception of Taylor series methods, all of the above methods listed in the previous sentence are accessed in Dakota through the Surfpack library. All of these surface fitting methods can be applied to problems having an arbitrary number of design parameters. However, surface fitting methods usually are practical only for problems where there are a small number of parameters (e.g., a maximum of somewhere in the range of 30-50 design parameters). The mathematical models created by surface fitting methods have a variety of names in the engineering community. These include surrogate models, meta-models, approximation models, and response surfaces. For this manual, the terms surface fit model and surrogate model are used.

The data fitting methods in Dakota include software developed by Sandia researchers and by various researchers in the academic community.

_Multifidelity Surrogate Models_

A second type of surrogate is the {em model ensemble} type (also called multifidelity, variable fidelity, variable complexity, etc.). In this case, one or more models that are still physics-based but are of lower fidelity (e.g., coarser discretization, reduced element order, looser convergence tolerances, omitted physics) are used as surrogates in place of the high-fidelity model. For example, an inviscid, incompressible Euler CFD model on a coarse discretization could be used as a low-fidelity surrogate for a high-fidelity large-eddy simulation model on a fine discretization.

_Surrogate Model Selection_

This section offers some guidance on choosing from among the available surrogate model types.

>   * For Surrogate Based Local Optimization, using the `[surrogate_based_local](../../usingdakota/reference/method-surrogate_based_local.html)` method with a trust region: using the keywords: `[surrogate](../../usingdakota/reference/model-surrogate.html)` `[local](../../usingdakota/reference/model-surrogate-local.html)` `[taylor_series](../../usingdakota/reference/model-surrogate-local-taylor_series.html)` or `[surrogate](../../usingdakota/reference/model-surrogate.html)` `[multipoint](../../usingdakota/reference/model-surrogate-multipoint.html)` `[tana](../../usingdakota/reference/model-surrogate-multipoint-tana.html)` will probably work best. If for some reason you wish or need to use a global surrogate (not recommended) then the best of these options is likely to be either: `[surrogate](../../usingdakota/reference/model-surrogate.html)` `[global](../../usingdakota/reference/model-surrogate-global.html)` `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` or `[surrogate](../../usingdakota/reference/model-surrogate.html)` `[global](../../usingdakota/reference/model-surrogate-global.html)` `[moving_least_squares](../../usingdakota/reference/model-surrogate-global-moving_least_squares.html)`.
> 
>   * For Efficient Global Optimization (EGO), the `[efficient_global](../../usingdakota/reference/method-efficient_global.html)` method: the default surrogate is: `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` which is likely to find a more optimal value and/or require fewer true function evaluations than the alternative, `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[dakota](../../usingdakota/reference/model-surrogate-global-gaussian_process-dakota.html)`. However, the `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` will likely take more time to build than the `dakota` version. Note that currently the `[use_derivatives](../../usingdakota/reference/method-efficient_global-use_derivatives.html)` keyword is not recommended for use with EGO based methods.
> 
>   * For EGO based global interval estimation, the `[global_interval_est](../../usingdakota/reference/method-global_interval_est.html)` `[ego](../../usingdakota/reference/method-global_interval_est-ego.html)` method: the default `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` will likely work better than the alternative `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[dakota](../../usingdakota/reference/model-surrogate-global-gaussian_process-dakota.html)`.
> 
>   * For Efficient Global Reliability Analysis (EGRA), the `[global_reliability](../../usingdakota/reference/method-global_reliability.html)` method: the `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` and `dakota` versions of the gaussian process tend to give similar answers with the `dakota` version tending to use fewer true function evaluations. Since this is based on EGO, it is likely that the default `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` is more accurate, although this has not been rigorously demonstrated.
> 
>   * For EGO based Dempster-Shafer Theory of Evidence, i.e. the `[global_evidence](../../usingdakota/reference/method-global_evidence.html)` `[ego](../../usingdakota/reference/method-global_evidence-ego.html)` method, the default `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` often use significantly fewer true function evaluations than the alternative `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[dakota](../../usingdakota/reference/model-surrogate-global-gaussian_process-dakota.html)`.
> 
>   * When using a global surrogate to extrapolate, any of the surrogates:
> 
>     * `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)`
> 
>     * `[polynomial](../../usingdakota/reference/model-surrogate-global-polynomial.html)` `[quadratic](../../usingdakota/reference/model-surrogate-global-polynomial-quadratic.html)`
> 
>     * `[polynomial](../../usingdakota/reference/model-surrogate-global-polynomial.html)` `[cubic](../../usingdakota/reference/model-surrogate-global-polynomial-cubic.html)` are recommended.
> 
>   * When there is over roughly two or three thousand data points and you wish to interpolate (or approximately interpolate) then a Taylor series, Radial Basis Function Network, or Moving Least Squares fit is recommended. The only reason that the `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` is not recommended is that it can take a considerable amount of time to construct when the number of data points is very large. Use of the third party MARS package included in Dakota is generally discouraged.
> 
>   * In other situations that call for a global surrogate, the `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` is generally recommended. The `use_derivatives` keyword will only be useful if accurate and inexpensive derivatives are available. Finite difference derivatives are disqualified on both counts. However, derivatives generated by analytical, automatic differentiation, or continuous adjoint techniques can be appropriate. Currently, first order derivatives, i.e. gradients, are the highest order derivatives that can be used to construct the `[gaussian_process](../../usingdakota/reference/model-surrogate-global-gaussian_process.html)` `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` model; Hessians will not be used even if they are available.
> 
>


---

#### model → surrogate → ensemble

# ensemble

Ensemble surrogates employ a collection of lower-fidelity models to approximate a truth reference model at reduced cost.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Ensemble model specification | [ordered_model_fidelities](model-surrogate-ensemble-ordered_model_fidelities.html) | Specification of an hierarchy of model fidelities, ordered from low to high.  
[truth_model_pointer](model-surrogate-ensemble-truth_model_pointer.html) | Pointer to specify a “truth” model, from which to construct a surrogate  
  
**Description**

Multifidelity modeling involves the use of lower-fidelity physics-based models as surrogates for a high-fidelity “truth” model. These low-fidelity models can involve variations in model form, resolution, or both. Model form variations for reduced fidelity may involve omitted physics or solution of approximated or averaged physics. Resolution variations typically involve coarsened space-time discretizations (e.g., h- derefinement), reduced element order (e.g., p- derefinement), or loosened convergence tolerances. Unlike local, global, and multipoint surrogates, these approximations are not data-driven (no high-fidelity data required for construction) and are stand-alone simulation models in their own right. As an example from computational fluid dynamics (CFD), both model form and resolution are varied when an inviscid, incompressible Euler model on a coarse discretization is used as a low-fidelity surrogate for a high-fidelity large eddy simulation (LES) model on a fine discretization.

There are two specification options for identifying the ensemble of models. First, the `ordered_model_fidelities` specification points to a sequence of model specifications of varying fidelity, ordered from lowest to highest fidelity. The highest fidelity model in this list provides the “truth” model, and each of the lower fidelity alternatives provides different levels of approximation at different levels of cost. This specification defines the sequence of all model forms, where each model specification identified in this ordered listing can additionally identify a set of resolution controls. Either or both of these sequences may be specified, and this is referred to as a “multilevel hierarchy” in the case of a hierarchy of resolutions (one entry in `ordered_model_fidelities` that includes active resolution control), a “multifidelity hierarchy” in the case of a hierarchy of model forms (multiple entries in `ordered_model_fidelities` without active resolution control), or a “multilevel-multifidelity hierarchy” in the case of two-dimensional hierarchy including both model forms and resolutions (multiple entries in `ordered_model_fidelities` with one or more including active resolution control). Note that the multilevel-multifidelity case can be “ragged” in the sense that not all models need to provide the same number of (or any) resolution controls.

Second, an unordered or “non-hierarchical” ensemble of models may be specified using `truth_model_pointer` in combination with `approximation_models`, where the latter defines the set of unordered approximations to the high-fidelity reference model. Note that the distinction between ordered and unordered approximations is of little consequence within the ensemble surrogate model implementation; rather it becomes important when aligning with the requirements of multifidelity algorithms that either assume ordered hierarchies or provide the flexibility to leverage general approximation ensembles.

The `correction` specification identifies what type ( `additive`, `multiplicative`, `combined`) and order ( `zeroth_order`, `first_order`, `second_order`) of correction technique will be applied to the low fidelity results in order to match high fidelity results (value and potentially gradient and Hessian) at one or more points. As described below, this is essential in the optimization context.

_Use cases_

In multifidelity surrogate-based optimization (SBO), the search algorithm relies primarily on the lower fidelity models, which are corrected for consistency with higher fidelity models. The higher fidelity models are used primarily for verifying candidate steps based on solution of low fidelity approximate subproblems and for updating low fidelity corrections. In the hierarchical SBO case (as compared to SBO with data fits), the `correction` specification is required, since the omission of a correction technique would effectively eliminate the purpose of the high fidelity model (to use a low fidelity model without corrections, then a `single` model can be used rather than a `hierarchical` model). Refer to `[global](../../usingdakota/reference/model-surrogate-global.html)` for additional information on available correction approaches.

In multifidelity uncertainty quantification (UQ), response differences are tracked for purposes of decomposing variance across model/resolution levels or for constructing separate discrepancy emulators. In this context, correction specifications are still valid for defining discrepancy emulation details but they are optional with the most common cases used as defaults.

**Examples**

Theory:


---

##### model → surrogate → ensemble → ordered_model_fidelities

# ordered_model_fidelities

Specification of an hierarchy of model fidelities, ordered from low to high.

**Specification**

  * _Alias:_ model_fidelity_sequence

  * _Arguments:_ STRINGLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [correction](model-surrogate-ensemble-ordered_model_fidelities-correction.html) | Correction approaches for surrogate models  
  
**Description**

An ensemble surrogate model can manage an ordered set of model fidelities, each of which may in turn involve multiple discretization levels (in the case of a simulation model) or additional model recursions.

The ordering is assumed to be from lowest fidelity to highest fidelity, as dictated by an accuracy versus cost trade-off. Corresponding sequence specifications within methods (e.g., `quadrature_order_sequence`, `sparse_grid_level_sequence`, `expansion_order_sequence`, etc. within stochastic expansion methods) should be synchronized with this model order.

_Additional Discussion_

Internal to the hierarchical usage of an ensemble surrogate model, only one low fidelity model instance and one high fidelity model instance are active at any given time, although various optimization and UQ algorithms can be used to traverse deep multilevel and multifidelity hierarchies by activating different model combinations and different response modes within the hierarchical model infrastructure.

**Examples**

    model,
     id_model = 'HIERARCH'
     surrogate ensemble
       ordered_model_fidelities = 'LF' 'MF 'HF'
       correction additive zeroth_order
    
    model,
     id_model = 'LF'
     simulation
       interface_pointer = 'LF_DRIVER'
    
    model,
     id_model = 'MF'
     simulation
       interface_pointer = 'MF_DRIVER'
    
    model,
     id_model = 'HF'
     simulation
       interface_pointer = 'HF_DRIVER'


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction

# correction

Correction approaches for surrogate models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no surrogate correction

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Correction Order | [zeroth_order](model-surrogate-ensemble-ordered_model_fidelities-correction-zeroth_order.html) | Specify that truth values must be matched.  
[first_order](model-surrogate-ensemble-ordered_model_fidelities-correction-first_order.html) | Specify that truth values and gradients must be matched.  
[second_order](model-surrogate-ensemble-ordered_model_fidelities-correction-second_order.html) | Specify that truth values, gradients and Hessians must be matched.  
Required (Choose One) | Correction Type | [additive](model-surrogate-ensemble-ordered_model_fidelities-correction-additive.html) | Additive correction factor for local surrogate accuracy  
[multiplicative](model-surrogate-ensemble-ordered_model_fidelities-correction-multiplicative.html) | Multiplicative correction factor for local surrogate accuracy.  
[combined](model-surrogate-ensemble-ordered_model_fidelities-correction-combined.html) | Multipoint correction for a hierarchical surrogate  
  
**Description**

Some of the surrogate model types support the use of correction factors that improve the local accuracy of the surrogate models.

The `correction` specification specifies that the approximation will be corrected to match truth data, either matching truth values in the case of `zeroth_order` matching, matching truth values and gradients in the case of `first_order` matching, or matching truth values, gradients, and Hessians in the case of `second_order` matching. For `additive` and `multiplicative` corrections, the correction is local in that the truth data is matched at a single point, typically the center of the approximation region. The `additive` correction adds a scalar offset ( `zeroth_order`), a linear function ( `,first_order`), or a quadratic function ( `second_order`) to the approximation to match the truth data at the point, and the `multiplicative` correction multiplies the approximation by a scalar ( `zeroth_order`), a linear function ( `first_order`), or a quadratic function ( `second_order`) to match the truth data at the point. The `additive` `first_order` case is due to [[LN00](../../misc/bibliography.html#id187 "R. M. Lewis and S. N. Nash. A multigrid approach to the optimization of systems governed by differential equations. In Proceedings of the 8th AIAA/USAF/NASA/ISSMO Symposium on Multidisciplinary Analysis and Optimization, number AIAA-2000-4890. Long Beach, CA, Sep 2000.")] and the `multiplicative` `first_order` case is commonly known as beta correction [[Haf91](../../misc/bibliography.html#id137 "R. T. Haftka. Combining global and local approximations. AIAA Journal, 29\(9\):1523–1525, 1991.")]. For the `combined` correction, the use of both additive and multiplicative corrections allows the satisfaction of an additional matching condition, typically the truth function values at the previous correction point (e.g., the center of the previous trust region). The `combined` correction is then a multipoint correction, as opposed to the local `additive` and `multiplicative` corrections. Each of these correction capabilities is described in detail in [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")].

The correction factors force the surrogate models to match the true function values and possibly true function derivatives at the center point of each trust region. Currently, Dakota supports either zeroth-, first-, or second-order accurate correction methods, each of which can be applied using either an additive, multiplicative, or combined correction function. For each of these correction approaches, the correction is applied to the surrogate model and the corrected model is then interfaced with whatever algorithm is being employed. The default behavior is that no correction factor is applied.

The simplest correction approaches are those that enforce consistency in function values between the surrogate and original models at a single point in parameter space through use of a simple scalar offset or scaling applied to the surrogate model. First-order corrections such as the first-order multiplicative correction (also known as beta correction [[CHGK93](../../misc/bibliography.html#id36 "K. J. Chang, R. T. Haftka, G. L. Giles, and P.-J. Kao. Sensitivity-based scaling for approximating structural response. J. Aircraft, 30:283–288, 1993.")]) and the first-order additive correction [[LN00](../../misc/bibliography.html#id187 "R. M. Lewis and S. N. Nash. A multigrid approach to the optimization of systems governed by differential equations. In Proceedings of the 8th AIAA/USAF/NASA/ISSMO Symposium on Multidisciplinary Analysis and Optimization, number AIAA-2000-4890. Long Beach, CA, Sep 2000.")] also enforce consistency in the gradients and provide a much more substantial correction capability that is sufficient for ensuring provable convergence in SBO algorithms. SBO convergence rates can be further accelerated through the use of second-order corrections which also enforce consistency in the Hessians [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")], where the second-order information may involve analytic, finite-difference, or quasi-Newton Hessians.

Correcting surrogate models with additive corrections involves

f{equation} hat{f_{hi_{alpha}}}({bf x}) = f_{lo}({bf x}) + alpha({bf x}) f} where multifidelity notation has been adopted for clarity. For multiplicative approaches, corrections take the form

f{equation} hat{f_{hi_{beta}}}({bf x}) = f_{lo}({bf x}) beta({bf x}) f} where, for local corrections, \\(\alpha({\bf x})\\) and \\(\beta({\bf x})\\) are first or second-order Taylor series approximations to the exact correction functions:

f{eqnarray} alpha({bf x}) & = & A({bf x_c}) + nabla A({bf x_c})^T ({bf x} - {bf x_c}) + frac{1}{2} ({bf x} - {bf x_c})^T nabla^2 A({bf x_c}) ({bf x} - {bf x_c}) \ beta({bf x}) & = & B({bf x_c}) + nabla B({bf x_c})^T ({bf x} - {bf x_c}) + frac{1}{2} ({bf x} - {bf x_c})^T nabla^2 B({bf x_c}) ({bf x} - {bf x_c}) f} where the exact correction functions are

f{eqnarray} A({bf x}) & = & f_{hi}({bf x}) - f_{lo}({bf x}) \ B({bf x}) & = & frac{f_{hi}({bf x})}{f_{lo}({bf x})} f} Refer to [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")] for additional details on the derivations.

A combination of additive and multiplicative corrections can provide for additional flexibility in minimizing the impact of the correction away from the trust region center. In other words, both additive and multiplicative corrections can satisfy local consistency, but through the combination, global accuracy can be addressed as well. This involves a convex combination of the additive and multiplicative corrections:

\\[\hat{f_{hi_{\gamma}}}({\bf x}) = \gamma \hat{f_{hi_{\alpha}}}({\bf x}) + (1 - \gamma) \hat{f_{hi_{\beta}}}({\bf x})\\]

where \\(\gamma\\) is calculated to satisfy an additional matching condition, such as matching values at the previous design iterate.

It should be noted that in both first order correction methods, the function \\(\hat{f}(x)\\) matches the function value and gradients of \\(f_{t}(x)\\) at \\(x=x_{c}\\) . This property is necessary in proving that the first order-corrected SBO algorithms are provably convergent to a local minimum of \\(f_{t}(x)\\) . However, the first order correction methods are significantly more expensive than the zeroth order correction methods, since the first order methods require computing both \\(\nabla f_{t}(x_{c})\\) and \\(\nabla f_{s}(x_{c})\\) . When the SBO strategy is used with either of the zeroth order correction methods, or with no correction method, convergence is not guaranteed to a local minimum of \\(f_{t}(x)\\) . That is, the SBO strategy becomes a heuristic optimization algorithm. From a mathematical point of view this is undesirable, but as a practical matter, the heuristic variants of SBO are often effective in finding local minima.

_Usage guidelines_

  * Both the `additive` zeroth_order and `multiplicative` zeroth_order correction methods are “free” since they use values of \\(f_{t}(x_{c})\\) that are normally computed by the SBO strategy.

  * The use of either the `additive` first_order method or the `multiplicative` first_order method does not necessarily improve the rate of convergence of the SBO algorithm.

  * When using the first order correction methods, the gradient-related response keywords must be modified to allow either analytic or numerical gradients to be computed. This provides the gradient data needed to compute the correction function.

  * For many computationally expensive engineering optimization problems, gradients often are too expensive to obtain or are discontinuous (or may not exist at all). In such cases the heuristic SBO algorithm has been an effective approach at identifying optimal designs [[Giu02](../../misc/bibliography.html#id120 "A. A. Giunta. Use of data sampling, surrogate models, and numerical optimization in engineering design. In Proc. 40th AIAA Aerospace Science Meeting and Exhibit, number AIAA-2002-0538. Reno, NV, January 2002.")].


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → additive

# additive

Additive correction factor for local surrogate accuracy

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use an additive correction factor to improve the local accuracy of a surrogate.


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → combined

# combined

Multipoint correction for a hierarchical surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

For the combined correction, the use of both additive and multiplicative corrections allows the satisfaction of an additional matching condition, typically the truth function values at the previous correction point (e.g., the center of the previous trust region). The combined correction is then a multipoint correction, as opposed to the local additive and multiplicative corrections.


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → first_order

# first_order

Specify that truth values and gradients must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This correction specification specifies that the approximation will be corrected to match truth data. The keyword `first_order` matching ensures that truth values and gradients are matched.


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → multiplicative

# multiplicative

Multiplicative correction factor for local surrogate accuracy.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use a multiplicative correction factor to improve the local accuracy of a surrogate.


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → second_order

# second_order

Specify that truth values, gradients and Hessians must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The correction specification specifies that the approximation will be corrected to match truth data. The keyword `second_order` matching ensures that truth values, gradients and Hessians are matched.


---

###### model → surrogate → ensemble → ordered_model_fidelities → correction → zeroth_order

# zeroth_order

Specify that truth values must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The correction specification specifies that the approximation will be corrected to match truth data. The keyword `zeroth_order` matching ensures that truth values are matched.


---

##### model → surrogate → ensemble → truth_model_pointer

# truth_model_pointer

Pointer to specify a “truth” model, from which to construct a surrogate

**Topics**

block_pointer

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [approximation_models](model-surrogate-ensemble-truth_model_pointer-approximation_models.html) | Specification of an unordered ensemble of low-fidelity approximations  
  
**Description**

This must point to a model block, identified by `[id_model](../../usingdakota/reference/model-id_model.html)`. That model will be run to generate training data, from which a surrogate model will be constructed.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.


---

###### model → surrogate → ensemble → truth_model_pointer → approximation_models

# approximation_models

Specification of an unordered ensemble of low-fidelity approximations

**Specification**

  * _Alias:_ unordered_model_fidelities

  * _Arguments:_ STRINGLIST

**Description**

An unordered or “non-hierarchical” ensemble surrogate manages an unordered set of low-fidelity model approximations, each of which may include hyper-parameter resolution controls (in the case of a simulation model) or additional model recursions.

Any corresponding sequence specifications within methods (e.g., `quadrature_order_sequence`, `sparse_grid_level_sequence`, `expansion_order_sequence`, etc. within stochastic expansion methods) should be synchronized with the order in the model listing.

Internal to the ensemble surrogate model, subsets of the model ensemble may be active for any given evaluation, as dictated by the iterative algorithm in use.

**Examples**

    model,
     id_model = 'NONHIERARCH'
     surrogate ensemble
       unordered_model_fidelities = 'LF1' 'LF2'
       truth_model_pointer = 'HF'
    
    model,
     id_model = 'LF1'
     simulation
       interface_pointer = 'LF1_DRIVER'
              solution_level_cost = 1.
    
    model,
     id_model = 'LF2'
     simulation
       interface_pointer = 'LF2_DRIVER'
              solution_level_cost = 2.4
    
    model,
     id_model = 'HF'
     simulation
       interface_pointer = 'HF_DRIVER'
              solution_level_cost = 256.


---

#### model → surrogate → global

# global

Select a surrogate model with global support

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Global Surrogate Type | [experimental_gaussian_process](model-surrogate-global-experimental_gaussian_process.html) | Use the Gaussian process regression surrogate from the surrogates module  
[gaussian_process](model-surrogate-global-gaussian_process.html) | Gaussian Process surrogate model  
[mars](model-surrogate-global-mars.html) | Multivariate Adaptive Regression Spline (MARS)  
[moving_least_squares](model-surrogate-global-moving_least_squares.html) | Moving Least Squares surrogate models  
[function_train](model-surrogate-global-function_train.html) | Global surrogate model based on functional tensor train decomposition  
[neural_network](model-surrogate-global-neural_network.html) | Artificial neural network model  
[radial_basis](model-surrogate-global-radial_basis.html) | Radial basis function (RBF) model  
[polynomial](model-surrogate-global-polynomial.html) | Polynomial surrogate model  
[experimental_polynomial](model-surrogate-global-experimental_polynomial.html) | Use a deterministic polynomial surrogate  
[experimental_python](model-surrogate-global-experimental_python.html) | Use the experimental python surrogates interface  
Optional | [domain_decomposition](model-surrogate-global-domain_decomposition.html) | Piecewise Domain Decomposition for Global Surrogate Models  
Optional (Choose One) | Number of Build Points | [total_points](model-surrogate-global-total_points.html) | Specified number of training points  
[minimum_points](model-surrogate-global-minimum_points.html) | Construct surrogate with minimum number of points  
[recommended_points](model-surrogate-global-recommended_points.html) | Construct surrogate with recommended number of points  
Optional (Choose One) | Build Data Source | [dace_method_pointer](model-surrogate-global-dace_method_pointer.html) | Specify a method to gather training data  
[truth_model_pointer](model-surrogate-global-truth_model_pointer.html) | A surrogate model pointer that guides a method to whether it should use a surrogate model or compute truth function evaluations  
Optional | [reuse_points](model-surrogate-global-reuse_points.html) | Surrogate model training data reuse control  
Optional | [import_build_points_file](model-surrogate-global-import_build_points_file.html) | File containing points you wish to use to build a surrogate  
Optional | [export_approx_points_file](model-surrogate-global-export_approx_points_file.html) | Output file for surrogate model value evaluations  
Optional | [use_derivatives](model-surrogate-global-use_derivatives.html) | Use derivative data to construct surrogate models  
Optional | [correction](model-surrogate-global-correction.html) | Correction approaches for surrogate models  
Optional | [metrics](model-surrogate-global-metrics.html) | Compute surrogate quality metrics  
Optional | [import_challenge_points_file](model-surrogate-global-import_challenge_points_file.html) | Datafile of points to assess surrogate quality  
  
**Description**

The global surrogate model requires specification of one of the following approximation types:

  1. Polynomial

  2. Gaussian process (Kriging interpolation)

  3. Layered perceptron artificial neural network approximation

  4. MARS

  5. Moving least squares

  6. Radial basis function

  7. Voronoi Piecewise Surrogate (VPS)

All these approximations are implemented in SurfPack [[GSB+06](../../misc/bibliography.html#id121 "A. A. Giunta, L. P. Swiler, S. L Brown, M. S. Eldred, M. D. Richards, and E. C. Cyr. The surfpack software library for surrogate modeling of sparse, irregularly spaced multidimensional data. In Proceedings of the 11th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference, number AIAA-2006-7049. Portsmouth, VA, 2006.")], except for VPS. In addition, a second version of Gaussian process is implemented directly in Dakota.

_Training Data_

Training data can be taken from prior runs, stored in a datafile, or by running a Design of Experiments method. The keywords listed below are used to determine how to collect training data:

  * `dace_method_pointer`

  * `reuse_points`

  * `import_points_file`

  * `use_derivatives` The source of training data is determined by the contents of a provided `import_points_file`, whether `reuse_points` and `use_derivatives` are specified, and the contents of the method block specified by `dace_method_pointer`. `use_derivatives` is a special case, the other keywords are discussed below.

The number of training data points used in building a global approximation is determined by specifying one of three point counts:

  1. `minimum_points`: minimum required or minimum “reasonable” amount of training data. Defaults to d+1 for d input dimensions for most models, e.g., polynomials override to the number of coefficients required to estimate the requested order.

  2. `recommended_points`: recommended number of training data, (this is the default option, if none of the keywords is specified). Defaults to 5*d, except for polynomials where it’s equal to the minimum.

  3. `total_points`: specify the number of training data points. However, if the `total_points` value is less than the default `minimum_points` value, the `minimum_points` value is used.

The sources of training data depend on the number of training points, \\(N_{tp}\\) , the number of points in the import file, \\(N_{if}\\) , and the value of `reuse_points`.

  * If there is no import file, all training data come from the DACE method

  * If there is an import file, all \\(N_{if}\\) points from the file are used, and the remaining \\(N_{tp} - N_{if}\\) points come from the DACE method

  * If there is an import file and `reuse_points` is:

    * `none` \- all \\(N_{tp}\\) points from DACE method

    * `region` \- only the points within a trust region are taken from the import file, and all remaining points are from the DACE method.

    * `all` \- (Default) all \\(N_{if}\\) points from the file are used, and the remaining \\(N_{tp} - N_{if}\\) points come from the DACE method

_Surrogate Correction_

A `correction` model can be added to the constructed surrogate in order to better match the training data. The specified correction method will be applied to the surrogate, and then the corrected surrogate model is used by the method.

Finally, the quality of the surrogate can be tested using the `metrics` and `challenge_points_file` keywords.

**Theory**

Global methods, also referred to as response surface methods, involve many points spread over the parameter ranges of interest. These surface fitting methods work in conjunction with the sampling methods and design of experiments methods.

_Procedures for Surface Fitting_

The surface fitting process consists of three steps:

  1. selection of a set of design points

  2. evaluation of the true response quantities (e.g., from a user-supplied simulation code) at these design points,

  3. using the response data to solve for the unknown coefficients (e.g., polynomial coefficients, neural network weights, kriging correlation factors) in the surface fit model.

In cases where there is more than one response quantity (e.g., an objective function plus one or more constraints), then a separate surface is built for each response quantity. Currently, the surface fit models are built using only 0 \\(^{\mathrm{th}}\\) -order information (function values only), although extensions to using higher-order information (gradients and Hessians) are possible.

Each surface fitting method employs a different numerical method for computing its internal coefficients. For example, the polynomial surface uses a least-squares approach that employs a singular value decomposition to compute the polynomial coefficients, whereas the kriging surface uses Maximum Likelihood Estimation to compute its correlation coefficients. More information on the numerical methods used in the surface fitting codes is provided in the Dakota Developers Manual.


---

##### model → surrogate → global → correction

# correction

Correction approaches for surrogate models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no surrogate correction

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Correction Order | [zeroth_order](model-surrogate-global-correction-zeroth_order.html) | Specify that truth values must be matched.  
[first_order](model-surrogate-global-correction-first_order.html) | Specify that truth values and gradients must be matched.  
[second_order](model-surrogate-global-correction-second_order.html) | Specify that truth values, gradients and Hessians must be matched.  
Required (Choose One) | Correction Type | [additive](model-surrogate-global-correction-additive.html) | Additive correction factor for local surrogate accuracy  
[multiplicative](model-surrogate-global-correction-multiplicative.html) | Multiplicative correction factor for local surrogate accuracy.  
[combined](model-surrogate-global-correction-combined.html) | Multipoint correction for a hierarchical surrogate  
  
**Description**

Some of the surrogate model types support the use of correction factors that improve the local accuracy of the surrogate models.

The `correction` specification specifies that the approximation will be corrected to match truth data, either matching truth values in the case of `zeroth_order` matching, matching truth values and gradients in the case of `first_order` matching, or matching truth values, gradients, and Hessians in the case of `second_order` matching. For `additive` and `multiplicative` corrections, the correction is local in that the truth data is matched at a single point, typically the center of the approximation region. The `additive` correction adds a scalar offset ( `zeroth_order`), a linear function ( `,first_order`), or a quadratic function ( `second_order`) to the approximation to match the truth data at the point, and the `multiplicative` correction multiplies the approximation by a scalar ( `zeroth_order`), a linear function ( `first_order`), or a quadratic function ( `second_order`) to match the truth data at the point. The `additive` `first_order` case is due to [[LN00](../../misc/bibliography.html#id187 "R. M. Lewis and S. N. Nash. A multigrid approach to the optimization of systems governed by differential equations. In Proceedings of the 8th AIAA/USAF/NASA/ISSMO Symposium on Multidisciplinary Analysis and Optimization, number AIAA-2000-4890. Long Beach, CA, Sep 2000.")] and the `multiplicative` `first_order` case is commonly known as beta correction [[Haf91](../../misc/bibliography.html#id137 "R. T. Haftka. Combining global and local approximations. AIAA Journal, 29\(9\):1523–1525, 1991.")]. For the `combined` correction, the use of both additive and multiplicative corrections allows the satisfaction of an additional matching condition, typically the truth function values at the previous correction point (e.g., the center of the previous trust region). The `combined` correction is then a multipoint correction, as opposed to the local `additive` and `multiplicative` corrections. Each of these correction capabilities is described in detail in [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")].

The correction factors force the surrogate models to match the true function values and possibly true function derivatives at the center point of each trust region. Currently, Dakota supports either zeroth-, first-, or second-order accurate correction methods, each of which can be applied using either an additive, multiplicative, or combined correction function. For each of these correction approaches, the correction is applied to the surrogate model and the corrected model is then interfaced with whatever algorithm is being employed. The default behavior is that no correction factor is applied.

The simplest correction approaches are those that enforce consistency in function values between the surrogate and original models at a single point in parameter space through use of a simple scalar offset or scaling applied to the surrogate model. First-order corrections such as the first-order multiplicative correction (also known as beta correction [[CHGK93](../../misc/bibliography.html#id36 "K. J. Chang, R. T. Haftka, G. L. Giles, and P.-J. Kao. Sensitivity-based scaling for approximating structural response. J. Aircraft, 30:283–288, 1993.")]) and the first-order additive correction [[LN00](../../misc/bibliography.html#id187 "R. M. Lewis and S. N. Nash. A multigrid approach to the optimization of systems governed by differential equations. In Proceedings of the 8th AIAA/USAF/NASA/ISSMO Symposium on Multidisciplinary Analysis and Optimization, number AIAA-2000-4890. Long Beach, CA, Sep 2000.")] also enforce consistency in the gradients and provide a much more substantial correction capability that is sufficient for ensuring provable convergence in SBO algorithms. SBO convergence rates can be further accelerated through the use of second-order corrections which also enforce consistency in the Hessians [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")], where the second-order information may involve analytic, finite-difference, or quasi-Newton Hessians.

Correcting surrogate models with additive corrections involves

f{equation} hat{f_{hi_{alpha}}}({bf x}) = f_{lo}({bf x}) + alpha({bf x}) f} where multifidelity notation has been adopted for clarity. For multiplicative approaches, corrections take the form

f{equation} hat{f_{hi_{beta}}}({bf x}) = f_{lo}({bf x}) beta({bf x}) f} where, for local corrections, \\(\alpha({\bf x})\\) and \\(\beta({\bf x})\\) are first or second-order Taylor series approximations to the exact correction functions:

f{eqnarray} alpha({bf x}) & = & A({bf x_c}) + nabla A({bf x_c})^T ({bf x} - {bf x_c}) + frac{1}{2} ({bf x} - {bf x_c})^T nabla^2 A({bf x_c}) ({bf x} - {bf x_c}) \ beta({bf x}) & = & B({bf x_c}) + nabla B({bf x_c})^T ({bf x} - {bf x_c}) + frac{1}{2} ({bf x} - {bf x_c})^T nabla^2 B({bf x_c}) ({bf x} - {bf x_c}) f} where the exact correction functions are

f{eqnarray} A({bf x}) & = & f_{hi}({bf x}) - f_{lo}({bf x}) \ B({bf x}) & = & frac{f_{hi}({bf x})}{f_{lo}({bf x})} f} Refer to [[EGC04](../../misc/bibliography.html#id79 "M. S. Eldred, A. A. Giunta, and S. S. Collis. Second-order corrections for surrogate-based optimization with model hierarchies. In Proceedings of the 10th AIAA/ISSMO Multidisciplinary Analysis and Optimization Conference. Albany, NY,, Aug. 30–Sept. 1, 2004. AIAA Paper 2004-4457.")] for additional details on the derivations.

A combination of additive and multiplicative corrections can provide for additional flexibility in minimizing the impact of the correction away from the trust region center. In other words, both additive and multiplicative corrections can satisfy local consistency, but through the combination, global accuracy can be addressed as well. This involves a convex combination of the additive and multiplicative corrections:

\\[\hat{f_{hi_{\gamma}}}({\bf x}) = \gamma \hat{f_{hi_{\alpha}}}({\bf x}) + (1 - \gamma) \hat{f_{hi_{\beta}}}({\bf x})\\]

where \\(\gamma\\) is calculated to satisfy an additional matching condition, such as matching values at the previous design iterate.

It should be noted that in both first order correction methods, the function \\(\hat{f}(x)\\) matches the function value and gradients of \\(f_{t}(x)\\) at \\(x=x_{c}\\) . This property is necessary in proving that the first order-corrected SBO algorithms are provably convergent to a local minimum of \\(f_{t}(x)\\) . However, the first order correction methods are significantly more expensive than the zeroth order correction methods, since the first order methods require computing both \\(\nabla f_{t}(x_{c})\\) and \\(\nabla f_{s}(x_{c})\\) . When the SBO strategy is used with either of the zeroth order correction methods, or with no correction method, convergence is not guaranteed to a local minimum of \\(f_{t}(x)\\) . That is, the SBO strategy becomes a heuristic optimization algorithm. From a mathematical point of view this is undesirable, but as a practical matter, the heuristic variants of SBO are often effective in finding local minima.

_Usage guidelines_

  * Both the `additive` zeroth_order and `multiplicative` zeroth_order correction methods are “free” since they use values of \\(f_{t}(x_{c})\\) that are normally computed by the SBO strategy.

  * The use of either the `additive` first_order method or the `multiplicative` first_order method does not necessarily improve the rate of convergence of the SBO algorithm.

  * When using the first order correction methods, the gradient-related response keywords must be modified to allow either analytic or numerical gradients to be computed. This provides the gradient data needed to compute the correction function.

  * For many computationally expensive engineering optimization problems, gradients often are too expensive to obtain or are discontinuous (or may not exist at all). In such cases the heuristic SBO algorithm has been an effective approach at identifying optimal designs [[Giu02](../../misc/bibliography.html#id120 "A. A. Giunta. Use of data sampling, surrogate models, and numerical optimization in engineering design. In Proc. 40th AIAA Aerospace Science Meeting and Exhibit, number AIAA-2002-0538. Reno, NV, January 2002.")].


---

###### model → surrogate → global → correction → additive

# additive

Additive correction factor for local surrogate accuracy

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use an additive correction factor to improve the local accuracy of a surrogate.


---

###### model → surrogate → global → correction → combined

# combined

Multipoint correction for a hierarchical surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

For the combined correction, the use of both additive and multiplicative corrections allows the satisfaction of an additional matching condition, typically the truth function values at the previous correction point (e.g., the center of the previous trust region). The combined correction is then a multipoint correction, as opposed to the local additive and multiplicative corrections.


---

###### model → surrogate → global → correction → first_order

# first_order

Specify that truth values and gradients must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This correction specification specifies that the approximation will be corrected to match truth data. The keyword `first_order` matching ensures that truth values and gradients are matched.


---

###### model → surrogate → global → correction → multiplicative

# multiplicative

Multiplicative correction factor for local surrogate accuracy.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use a multiplicative correction factor to improve the local accuracy of a surrogate.


---

###### model → surrogate → global → correction → second_order

# second_order

Specify that truth values, gradients and Hessians must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The correction specification specifies that the approximation will be corrected to match truth data. The keyword `second_order` matching ensures that truth values, gradients and Hessians are matched.


---

###### model → surrogate → global → correction → zeroth_order

# zeroth_order

Specify that truth values must be matched.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The correction specification specifies that the approximation will be corrected to match truth data. The keyword `zeroth_order` matching ensures that truth values are matched.


---

##### model → surrogate → global → dace_method_pointer

# dace_method_pointer

Specify a method to gather training data

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no design of experiments data

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [auto_refinement](model-surrogate-global-dace_method_pointer-auto_refinement.html) | Experimental auto-refinement of surrogate model  
  
**Description**

The number of training points and the sources are specified on `[global](../../usingdakota/reference/model-surrogate-global.html)`, as well as the number of new training points required.

New training points are gathered by running the “truth” model using the method specified by `dace_method_pointer`. The DACE method will only be invoked if it has new samples to perform, and if new samples are required and no DACE iterator has been provided, an error will result.

The `dace_method_pointer` points to design of experiments method block used to generate truth model data.

Permissible methods include: Monte Carlo (random) sampling, Latin hypercube sampling, orthogonal array sampling, central composite design sampling, and Box-Behnken sampling.

Note that the number of samples specified in the method block may be overwritten, if the requested number of samples is less than `[minimum_points](../../usingdakota/reference/model-surrogate-global-minimum_points.html)`.


---

###### model → surrogate → global → dace_method_pointer → auto_refinement

# auto_refinement

Experimental auto-refinement of surrogate model

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no refinement

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [max_iterations](model-surrogate-global-dace_method_pointer-auto_refinement-max_iterations.html) | Number of iterations allowed for optimizers and adaptive UQ methods  
Optional | [max_function_evaluations](model-surrogate-global-dace_method_pointer-auto_refinement-max_function_evaluations.html) | Number of function evaluations allowed for optimizers  
Optional | [convergence_tolerance](model-surrogate-global-dace_method_pointer-auto_refinement-convergence_tolerance.html) | Cross-validation threshold for surrogate convergence  
Optional | [soft_convergence_limit](model-surrogate-global-dace_method_pointer-auto_refinement-soft_convergence_limit.html) | Maximum number of iterations without improvement in cross-validation  
Optional | [cross_validation_metric](model-surrogate-global-dace_method_pointer-auto_refinement-cross_validation_metric.html) | Choice of error metric to satisfy  
  
**Description**

(Experimental option) Automatically refine the surrogate model until desired cross-validation quality is achieved. Refinement is accomplished by iteratively adding more data to the training set until the cross-validation `convergence_tolerance` is achieved, or `max_function_evaluations` or `max_iterations` is exceeded.

The amount of new training data that is incorporated each iteration is specified in the DACE method that is referred to by the model’s `dace_method_pointer`. See `[refinement_samples](../../usingdakota/reference/method-sampling-refinement_samples.html)` for more information.


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → convergence_tolerance

# convergence_tolerance

Cross-validation threshold for surrogate convergence

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.0e-4

**Description**

The surrogate model will be refined until the selected `cross_validation_metric` falls below this convergence tolerance.

_Default Behavior_ The default is 1e-4.


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → cross_validation_metric

# cross_validation_metric

Choice of error metric to satisfy

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ root-mean-squared error

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [folds](model-surrogate-global-dace_method_pointer-auto_refinement-cross_validation_metric-folds.html) | Number of cross validation folds  
  
**Description**

The cross-validation score that the auto-refinement routine seeks to satisfy is based on an error metric. Any of the surrogate diagnostic `[metrics](../../usingdakota/reference/model-surrogate-global-metrics.html)` may be used. These include:

  * root_mean_squared

  * mean_abs

  * rsquared

  * sum_squared

  * mean_squared

  * sum_abs

  * max_abs

_Default Behavior_ The default metric is the the root mean squared error (‘root_mean_squared’).


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → cross_validation_metric → folds

# folds

Number of cross validation folds

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 10

**Description**

Number of folds (partitions) of the training data to use in cross validation (default 10).


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → max_function_evaluations

# max_function_evaluations

Number of function evaluations allowed for optimizers

**Topics**

method_independent_controls

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 1000

**Description**

The maximum number of function evaluations is used as a stopping criterion for optimizers. If it has not reached any other stopping criteria first, the optimizer will stop after it has performed `max_function_evalutions` evaluations. See also `max_iterations`.

Some optimizers (e.g. `ncsu_direct`) may run past this limit in the course of an iteration step that began before `max_function_evaluations` was exceeded.

_Default Behavior_

Default value is 1000.


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → max_iterations

# max_iterations

Number of iterations allowed for optimizers and adaptive UQ methods

**Topics**

method_independent_controls

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 100

**Description**

The maximum number of iterations is used as a stopping criterion for optimizers and some adaptive UQ methods. If it has not reached any other stopping criteria first, the method will stop after it has performed `max_iterations` iterations. In cases where there are multiple nested iterations to be controlled, `max_iterations` generally controls the outer-most context.

_Default Behavior_

Default value is 100.


---

###### model → surrogate → global → dace_method_pointer → auto_refinement → soft_convergence_limit

# soft_convergence_limit

Maximum number of iterations without improvement in cross-validation

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Description**

The purpose of this control is to cease auto-refinement when improvement in the cross-validation score appears to have stalled or occurs too slowly.

A rolling average of improvement in the cross-validation score is computed over `soft_convergence_limit` iterations. Auto-refinement is halted if the average becomes smaller than the `convergence_tolerance`. The average is computed but ignored until the number of iterations has exceeded `soft_convergence_limit`.

The default setting of 0 disables soft convergence.


---

##### model → surrogate → global → domain_decomposition

# domain_decomposition

Piecewise Domain Decomposition for Global Surrogate Models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [cell_type](model-surrogate-global-domain_decomposition-cell_type.html) | Type of the Geometric Cells Used for the Piecewise Decomposition Option of Global Surrogates  
Optional | [support_layers](model-surrogate-global-domain_decomposition-support_layers.html) | Optional Number of Support Layers for the Piecewise Decomposition Option of Global Surrogates  
Optional | [discontinuity_detection](model-surrogate-global-domain_decomposition-discontinuity_detection.html) | Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates  
  
**Description**

Typical regression techniques use all available sample points to build continuous approximations the underlying function.

An alternative option is to use piecewise decomposition to locally approximate the function at some point using a few sample points from its neighborhood only. This option currently supports Polynomial Regression, Gaussian Process (GP) Interpolation, and Radial Basis Functions (RBF) Regression. It requires a decomposition cell type (currently set to be Voronoi cells). Optional parameters are: the number of layers of neighbors used to solve the regression problem (default is one layer), and an optional discontinuity detection capability (identified by a user-input jump or gradient threshold).

The method can also make use of the gradient and Hessian information, if available. The user needs to specify the keyword user_derivatives.


---

###### model → surrogate → global → domain_decomposition → cell_type

# cell_type

Type of the Geometric Cells Used for the Piecewise Decomposition Option of Global Surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Description**

The piecewise decomposition option for global surrogates is used to locally approximate a function at some point using a few sample points from its neighborhood.

This option requires a decomposition cell type that can vary from structured grid boxes, to polygonal Voronoi cells. Currently, this option only supports Voronoi cells.


---

###### model → surrogate → global → domain_decomposition → discontinuity_detection

# discontinuity_detection

Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Threshold Mode | [jump_threshold](model-surrogate-global-domain_decomposition-discontinuity_detection-jump_threshold.html) | Gradient Threshold Parameter of the Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates  
[gradient_threshold](model-surrogate-global-domain_decomposition-discontinuity_detection-gradient_threshold.html) | Gradient Threshold Parameter of the Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates  
  
**Description**

The piecewise decomposition option for global surrogates is used to locally approximate a function at some point using a few sample points from its neighborhood.

The domain decomposition algorithm supports an optional discontinuity detection capability where seeds across a user-input discontinuity threshold are not considered neighbors when building the approximate connectivity Delaunay graph. Alternatively, the domain is split into patches that trap discontinuities between them. This capability is specified by either jump or gradient threshold values in the input spec.


---

###### model → surrogate → global → domain_decomposition → discontinuity_detection → gradient_threshold

# gradient_threshold

Gradient Threshold Parameter of the Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

The piecewise decomposition option for global surrogates is used to locally approximate a function at some point using a few sample points from its neighborhood.

The domain decomposition algorithm supports an optional discontinuity detection capability where seeds across a user-input discontinuity threshold are not considered neighbors when building the approximate connectivity Delaunay graph. Alternatively, the domain is split into patches that trap discontinuities between them. This capability can be specified using a gradient threshold value in the input spec.


---

###### model → surrogate → global → domain_decomposition → discontinuity_detection → jump_threshold

# jump_threshold

Gradient Threshold Parameter of the Optional Discontinuity Detection Capability for the Piecewise Decomposition Option of Global Surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

The piecewise decomposition option for global surrogates is used to locally approximate a function at some point using a few sample points from its neighborhood.

The domain decomposition algorithm supports an optional discontinuity detection capability where seeds across a user-input discontinuity threshold are not considered neighbors when building the approximate connectivity Delaunay graph. Alternatively, the domain is split into patches that trap discontinuities between them. This capability can be specified using a jump threshold value in the input spec.


---

###### model → surrogate → global → domain_decomposition → support_layers

# support_layers

Optional Number of Support Layers for the Piecewise Decomposition Option of Global Surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

The piecewise decomposition option for global surrogates is used to locally approximate a function at some point using a few sample points from its neighborhood.

The neighborhood of a cell is parameterized via a number of (support layers). The default value is set to one layer of neighbors (cells that share direct edges with the cell under study). One more support layer would include the neighbors of that cell’s neighbors, and so on.


---

##### model → surrogate → global → experimental_gaussian_process

# experimental_gaussian_process

Use the Gaussian process regression surrogate from the surrogates module

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [trend](model-surrogate-global-experimental_gaussian_process-trend.html) | This keyword enables the use of deterministic polynomial trend function  
Optional | [num_restarts](model-surrogate-global-experimental_gaussian_process-num_restarts.html) | Number of optimization restarts for L-BFGS-B  
Optional (Choose One) | Nugget | [nugget](model-surrogate-global-experimental_gaussian_process-nugget.html) | Value for the fixed nugget parameter  
[find_nugget](model-surrogate-global-experimental_gaussian_process-find_nugget.html) | Use regression to estimate the nugget.  
Optional | [options_file](model-surrogate-global-experimental_gaussian_process-options_file.html) | Filename for a YAML file that specifies Gaussian process options  
Optional | [export_approx_variance_file](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file.html) | Output file for surrogate model variance evaluations  
Optional | [export_model](model-surrogate-global-experimental_gaussian_process-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-experimental_gaussian_process-import_model.html) | Import surrogate model from archive file  
  
**Description**

This Gaussian process implementation is contained in Dakota’s surrogates module and is considered experimental. It uses gradient-based optimization with restarts to determine hyperparmeters and trend coefficients. Nugget and trend estimation are optional.


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file

# export_approx_variance_file

Output file for surrogate model variance evaluations

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no variance export to a file

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-annotated.html) | Selects annotated tabular file format  
[freeform](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-freeform.html) | Selects freeform file format  
  
**Description**

Specifies a file in which the points (input variable values) at which the surrogate model is evaluated and corresponding response _variance_ values computed by the surrogate model will be written. The response values are the surrogate’s predicted approximation to the truth model responses at those points.

_Usage Tips_

Dakota exports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](model-surrogate-global-experimental_gaussian_process-export_approx_variance_file-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → experimental_gaussian_process → export_approx_variance_file → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → experimental_gaussian_process → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_gaussian_process-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-experimental_gaussian_process-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_gaussian_process → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → experimental_gaussian_process → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-experimental_gaussian_process-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-experimental_gaussian_process-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → experimental_gaussian_process → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_gaussian_process → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_gaussian_process → find_nugget

# find_nugget

Use regression to estimate the nugget.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ None

**Description**

Estimate a nugget term in the Gaussian process Gram matrix.

_Default Behavior_ Off by default. The user may alternatively specify a fixed nugget.


---

###### model → surrogate → global → experimental_gaussian_process → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_gaussian_process-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-experimental_gaussian_process-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-experimental_gaussian_process-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → experimental_gaussian_process → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_gaussian_process → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → experimental_gaussian_process → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_gaussian_process → nugget

# nugget

Value for the fixed nugget parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ None

**Description**

This keyword is used to specify a value for the nugget parameter. Its presence will turn off nugget estimation.

_Default Behavior_ No nugget when this keyword is absent. Alternatively a nugget may be estimated as part of the regression procedure.


---

###### model → surrogate → global → experimental_gaussian_process → num_restarts

# num_restarts

Number of optimization restarts for L-BFGS-B

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Number of optimization runs at random initial guesses for maximum likelihood estimation. Multiple restarts are used to increase the chance of finding the global minimum with a gradient-based (i.e. local) optimization method.

_Default Behavior_ Default value of 20 restarts.


---

###### model → surrogate → global → experimental_gaussian_process → options_file

# options_file

Filename for a YAML file that specifies Gaussian process options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no advanced options file

**Description**

The YAML file contains configuration options that are used to populate a Teuchos ParameterList used by the Gaussian process that will override other keyword-specified parameters. Missing options in the YAML file are set to default values.

**Examples**

    # FILE: gp_options.yaml
    
    GP Parameters:
      Sigma Bounds:
        lower bound: 1.0e-2
        upper bound: 1.0e2
      scaler name: standardization
      num restarts: 20
      Nugget:
        fixed nugget: 0.0
        estimate nugget: true
        Bounds:
          lower bound: 3.17e-8
          upper bound: 1.0e-2
      gp seed: 42
      standardize response: false
      Trend:
        estimate trend: true
        Options:
          max degree: 2
          reduced basis: false
          p-norm: 1.0
          scaler type: none
          regression solver type: SVD
          verbosity: 1
      kernel type: squared exponential
      Length-scale Bounds:
        lower bound: 1.0e-2
        upper bound: 1.0e2
      verbosity: 1


---

###### model → surrogate → global → experimental_gaussian_process → trend

# trend

This keyword enables the use of deterministic polynomial trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ reduced_quadratic

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Trend Order | [none](model-surrogate-global-experimental_gaussian_process-trend-none.html) | No trend function  
[constant](model-surrogate-global-experimental_gaussian_process-trend-constant.html) | Constant trend function  
[linear](model-surrogate-global-experimental_gaussian_process-trend-linear.html) | Use a linear polynomial or trend function  
[reduced_quadratic](model-surrogate-global-experimental_gaussian_process-trend-reduced_quadratic.html) | Quadratic polynomials - main effects only  
[quadratic](model-surrogate-global-experimental_gaussian_process-trend-quadratic.html) | Use a quadratic polynomial or trend function  
  
**Description**

Enable the use of a deterministic polynomial trend function whose coefficients are found through regression. The user may choose a constant, linear, or quadratic polynomial.

_Default Behavior_ Defaults to no trend when keyword is absent.


---

###### model → surrogate → global → experimental_gaussian_process → trend → constant

# constant

Constant trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → experimental_gaussian_process → trend → linear

# linear

Use a linear polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → experimental_gaussian_process → trend → none

# none

No trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → experimental_gaussian_process → trend → quadratic

# quadratic

Use a quadratic polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → experimental_gaussian_process → trend → reduced_quadratic

# reduced_quadratic

Quadratic polynomials - main effects only

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In 2 or more dimensions, this polynomial omits the interaction, or mixed, terms.


---

##### model → surrogate → global → experimental_polynomial

# experimental_polynomial

Use a deterministic polynomial surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [basis_order](model-surrogate-global-experimental_polynomial-basis_order.html) | Total degree of the polynomial basis  
Optional | [options_file](model-surrogate-global-experimental_polynomial-options_file.html) | Filename for a YAML file that specifies polynomial surrogate options  
Optional | [export_model](model-surrogate-global-experimental_polynomial-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-experimental_polynomial-import_model.html) | Import surrogate model from archive file  
  
**Description**

Compute a polynomial surrogate for a specified total order by least-squares regression. This model is implemented in Dakota’s surrogates module and is considered experimental.


---

###### model → surrogate → global → experimental_polynomial → basis_order

# basis_order

Total degree of the polynomial basis

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This keyword’s argument specifies the terms in the polynomial according to a total order scheme.


---

###### model → surrogate → global → experimental_polynomial → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_polynomial-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-experimental_polynomial-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_polynomial → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → experimental_polynomial → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-experimental_polynomial-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-experimental_polynomial-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → experimental_polynomial → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_polynomial → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_polynomial → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_polynomial-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-experimental_polynomial-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-experimental_polynomial-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → experimental_polynomial → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_polynomial → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → experimental_polynomial → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → experimental_polynomial → options_file

# options_file

Filename for a YAML file that specifies polynomial surrogate options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no advanced options file

**Description**

The YAML file contains configuration options that are used to populate a Teuchos ParameterList used by the polynomial that will override other keyword-specified parameters. Missing options in the YAML file are set to default values.

**Examples**

    # FILE: polynomial_options.yaml
    
    Polynomial Parameters:
      max degree: 3
      scaler type: none
      reduced basis: false
      p-norm: 1.0
      regression solver type: SVD
      standardize response: false
      verbosity: 1


---

##### model → surrogate → global → experimental_python

# experimental_python

Use the experimental python surrogates interface

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [options_file](model-surrogate-global-experimental_python-options_file.html) | An unused placeholder option for experimental python-based surrogates  
Required | [class_path_and_name](model-surrogate-global-experimental_python-class_path_and_name.html) | Specify the module and class name of the python surrogate  
Optional | [export_model](model-surrogate-global-experimental_python-export_model.html) | An unused placeholder option for experimental python-based surrogates  
Optional | [import_model](model-surrogate-global-experimental_python-import_model.html) | An unused placeholder option for experimental python-based surrogates  
  
**Description**

This is an experimental capability that allows the user to expose a python-based surrogate to Dakota.


---

###### model → surrogate → global → experimental_python → class_path_and_name

# class_path_and_name

Specify the module and class name of the python surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Description**

Specify the module and class name of the python surrogate. At minimum, the class must contain the methods ‘construct’ and ‘predict’. Dakota passes to the former a matrix (num samples x num variables) of variable values and a num samples x 1 matrix of responses to use to train the surrogate. The predict method receives a matrix of num samples x num variables and expects a matrix of num samples x 1 predicted values to be returned. An optional ‘gradient’ method is supported as well and returns gradients for a matrix of variables passed from Dakota.

**Examples**

Use of python-based surrogates in Dakota is enabled via:

    model,
        id_model = 'SURR'
        surrogate global,
        dace_method_pointer = 'DACE'
        experimental_python
            class_path_and_name = "surrogate_polynomial.Surrogate"

An corresponding python class (incomplete) would contain at minimum:

    class Surrogate:
    
      def construct(self, var, resp):
          var2 = np.hstack((np.ones((var.shape[0], 1)), var))
          self.coeffs = np.zeros(var.shape[1])
          z = np.linalg.inv(np.dot(var2.T, var2))
          self.coeffs = np.dot(z, np.dot(var2.T, resp))
          return
    
      def predict(self, pts):
          return self.coeffs[0]+pts.dot(self.coeffs[1:])


---

###### model → surrogate → global → experimental_python → export_model

# export_model

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_python-export_model-filename_prefix.html) | An unused placeholder option for experimental python-based surrogates  
Required | [formats](model-surrogate-global-experimental_python-export_model-formats.html) | An unused placeholder option for experimental python-based surrogates  
  
**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → export_model → filename_prefix

# filename_prefix

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → export_model → formats

# formats

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-experimental_python-export_model-formats-text_archive.html) | An unused placeholder option for experimental python-based surrogates  
Optional | [binary_archive](model-surrogate-global-experimental_python-export_model-formats-binary_archive.html) | An unused placeholder option for experimental python-based surrogates  
  
**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → export_model → formats → binary_archive

# binary_archive

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → export_model → formats → text_archive

# text_archive

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → import_model

# import_model

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-experimental_python-import_model-filename_prefix.html) | An unused placeholder option for experimental python-based surrogates  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-experimental_python-import_model-text_archive.html) | An unused placeholder option for experimental python-based surrogates  
[binary_archive](model-surrogate-global-experimental_python-import_model-binary_archive.html) | An unused placeholder option for experimental python-based surrogates  
  
**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → import_model → binary_archive

# binary_archive

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → import_model → filename_prefix

# filename_prefix

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → import_model → text_archive

# text_archive

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

###### model → surrogate → global → experimental_python → options_file

# options_file

An unused placeholder option for experimental python-based surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no advanced options file

**Description**

This option is not active but instead serves as a placeholder for future extensions to the experimental python-based surrogates capability.


---

##### model → surrogate → global → export_approx_points_file

# export_approx_points_file

Output file for surrogate model value evaluations

**Specification**

  * _Alias:_ export_points_file

  * _Arguments:_ STRING

  * _Default:_ no point export to a file

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](model-surrogate-global-export_approx_points_file-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](model-surrogate-global-export_approx_points_file-annotated.html) | Selects annotated tabular file format  
[freeform](model-surrogate-global-export_approx_points_file-freeform.html) | Selects freeform file format  
  
**Description**

Specifies a file in which the points (input variable values) at which the surrogate model is evaluated and corresponding response values computed by the surrogate model will be written. The response values are the surrogate’s predicted approximation to the truth model responses at those points.

_Usage Tips_

Dakota exports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

###### model → surrogate → global → export_approx_points_file → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → export_approx_points_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](model-surrogate-global-export_approx_points_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](model-surrogate-global-export_approx_points_file-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](model-surrogate-global-export_approx_points_file-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → export_approx_points_file → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → export_approx_points_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → export_approx_points_file → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → export_approx_points_file → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### model → surrogate → global → function_train

# function_train

Global surrogate model based on functional tensor train decomposition

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [regression_type](model-surrogate-global-function_train-regression_type.html) | Type of solver for forming function train approximations by regression  
Optional | [max_solver_iterations](model-surrogate-global-function_train-max_solver_iterations.html) | Maximum iterations in determining polynomial coefficients  
Optional | [max_cross_iterations](model-surrogate-global-function_train-max_cross_iterations.html) | Maximum number of iterations for cross-approximation during a rank adaptation.  
Optional | [solver_tolerance](model-surrogate-global-function_train-solver_tolerance.html) | Convergence tolerance for the optimizer used during the regression solve.  
Optional | [response_scaling](model-surrogate-global-function_train-response_scaling.html) | Perform bounds-scaling on response values prior to surrogate emulation  
Optional | [tensor_grid](model-surrogate-global-function_train-tensor_grid.html) | Use sub-sampled tensor-product quadrature points to build a polynomial chaos expansion.  
Optional | [rounding_tolerance](model-surrogate-global-function_train-rounding_tolerance.html) | An accuracy tolerance that is used to guide rounding during rank adaptation.  
Optional | [arithmetic_tolerance](model-surrogate-global-function_train-arithmetic_tolerance.html) | A secondary rounding tolerance used for post-processing  
Optional | [start_order](model-surrogate-global-function_train-start_order.html) | (Initial) polynomial order of each univariate function within the functional tensor train.  
Optional | [adapt_order](model-surrogate-global-function_train-adapt_order.html) | Activate adaptive procedure for determining the best basis order  
Optional | [kick_order](model-surrogate-global-function_train-kick_order.html) | increment used when adapting the basis order in function train methods  
Optional | [max_order](model-surrogate-global-function_train-max_order.html) | Maximum polynomial order of each univariate function within the functional tensor train.  
Optional | [max_cv_order_candidates](model-surrogate-global-function_train-max_cv_order_candidates.html) | Limit the number of cross-validation candidates for basis order  
Optional | [start_rank](model-surrogate-global-function_train-start_rank.html) | The initial rank used for the starting point during a rank adaptation.  
Optional | [adapt_rank](model-surrogate-global-function_train-adapt_rank.html) | Activate adaptive procedure for determining best rank representation  
Optional | [kick_rank](model-surrogate-global-function_train-kick_rank.html) | The increment in rank employed during each iteration of the rank adaptation.  
Optional | [max_rank](model-surrogate-global-function_train-max_rank.html) | Limits the maximum rank that is explored during a rank adaptation.  
Optional | [max_cv_rank_candidates](model-surrogate-global-function_train-max_cv_rank_candidates.html) | Limit the number of cross-validation candidates for rank  
  
**Description**

Tensor train decompositions are approximations that exploit low rank structure in an input-output mapping. The form of the approximation can be written as a set of matrix valued products:

\\[f_r(x) = F_1(x_1) F_2(x_2) \dots F_d(x_d)\\]

where the “cores” expand to

\\[\begin{split}F_k(x_k) = \begin{bmatrix} f_k^{11}(x_k) & dots ````& f_k^{1r_k}(x_k)\\\ \vdots & \ddots & \vdots\\\ f_k^{r_{k-1}1}(x_k) & dots ````& f_k^{r_{k-1}r_k}(x_k) \end{bmatrix}\end{split}\\]

An example expansion over four random variables with rank vector (1,7,5,3,1) is

\\[\begin{split}f_r(x) = \begin{bmatrix} f_1^{11}(x_1) & dots ````& f_1^{17}(x_1) \end{bmatrix} \begin{bmatrix} f_2^{11}(x_2) & dots ````& f_2^{15}(x_2)\\\ \vdots & \ddots & \vdots\\\ f_2^{71}(x_2) & dots ````& f_2^{75}(x_2) \end{bmatrix} \begin{bmatrix} f_3^{11}(x_3) & dots ````& f_3^{13}(x_3)\\\ \vdots & \ddots & \vdots\\\ f_3^{51}(x_3) & dots ````& f_3^{53}(x_3) \end{bmatrix} \begin{bmatrix} f_4^{11}(x_4) \\\ \vdots \\\ f_4^{31}(x_4) \end{bmatrix}\end{split}\\]

In the current implementation, orthogonal polynomial basis functions (Hermite and Legendre) are employed as the basis functions \\(f_i^{jk}(x_i)\\) , although the C3 library will enable additional options in the future.

The number of coefficients that must be computed by the regression solver can be inferred from the construction above. For each QoI, the regression size can be determined as follows:

  * For a v variables, orders a o is a v-vector and ranks a r is a v+1-vector

  * the first core is a \\(1 \times r_1\\) row vector and contributes \\((o_0 + 1) r_1\\) terms

  * the last core is a \\(r_{v-1} \times 1\\) col vector and contributes \\((o_{v-1}+1) r_{v-1}\\) terms

  * the middle v-2 cores are \\(r_i \times r_{i+1}\\) matrices that contribute \\(r_i r_{i+1} (o_i + 1)\\) terms, \\(i = 1, ..., v-2\\)

  * neighboring vec/mat dimensions must match, so there are v-1 unique ranks

_Usage Tips_

This new capability is stabilizing and beginning to be embedded in higher-level strategies such as multilevel-multifidelity algorithms. It is not included in the Dakota build by default, as some C3 library dependencies (CBLAS) can induce small differences in our regression suite.

This capability is also being used as a prototype to explore model-based versus method-based specification of stochastic expansions. While the model specification is stand-alone, it currently requires a corresponding method specification to exercise the model, which can be a generic UQ strategy such as `surrogate_based_uq` method or a `sampling` method. The intent is to migrate function train, polynomial chaos, and stochastic collocation toward model-only specifications that can then be employed in any surrogate/emulator context.

**Examples**

    model,
     id_model = 'FT'
     surrogate global function_train
       start_order = 2
       start_rank  = 2  kick_rank = 2  max_rank = 10
       adapt_rank
     dace_method_pointer = 'SAMPLING'


---

###### model → surrogate → global → function_train → adapt_order

# adapt_order

Activate adaptive procedure for determining the best basis order

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ false

**Description**

This option activates a cross validation procedure to select the best basis order for representing the current set of data. It searches from `start_order` to `max_order` in increments of `kick_order`.

_Default Behavior_

No cross validation for basis order.

**Examples**

This example shows specification of an order adaptation starting at order 2, incrementing by 2, and limited at order 6.

    model,
     id_model = 'FT'
     surrogate global function_train
       start_rank = 4
       adapt_order  start_order = 2  kick_order = 2  max_order = 6
       solver_tolerance   = 1e-12
       rounding_tolerance = 1e-12
     dace_method_pointer = 'SAMPLING'

Note that `adapt_rank` and `adapt_order` can either be combined or used separately.


---

###### model → surrogate → global → function_train → adapt_rank

# adapt_rank

Activate adaptive procedure for determining best rank representation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ false

**Description**

The adaptive algorithm proceeds as follows:

  1. Start from rank `start_rank` and form an approximation

  2. Adapt the current approximation by searching for a solution with lower rank that achieves L2 accuracy within epsilon tolerance of the reference.

  3. If a lower rank solution is found with comparable accuracy, then stop. If not, increase the rank by an amount specified by `kick_rank`.

  4. Return to step 2 and continue until either `max_rank` is reached or a converged rank (rank less than current reference with comparable accuracy) is found.

_Default Behavior_

No cross validation for rank.

**Examples**

This example shows specification of a rank adaptation starting at rank 2, incrementing by 2, and limited at rank 10.

    model,
     id_model = 'FT'
     surrogate global function_train
       start_order = 5
       adapt_rank  start_rank = 2  kick_rank = 2  max_rank = 10
       solver_tolerance   = 1e-12
       rounding_tolerance = 1e-12
     dace_method_pointer = 'SAMPLING'

Note that `adapt_rank` and `adapt_order` can either be combined or used separately.


---

###### model → surrogate → global → function_train → arithmetic_tolerance

# arithmetic_tolerance

A secondary rounding tolerance used for post-processing

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.e-10

**Description**

After forming one or more function train approximations, this tolerance is used for rolling up final results, in particular, combining multilevel/multifidelity expansions and forming products for higher-order moment estimation. It is generally intended as a looser tolerance than `rounding_tolerance`.

_Default Behavior_

If this tolerance is not specified, then we adopt the internal C3 default.


---

###### model → surrogate → global → function_train → kick_order

# kick_order

increment used when adapting the basis order in function train methods

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 1

**Description**

Used in combination with `adapt_order`, `kick_order` defines the size of the increment that is used during the cross validation process, within the range of `start_order` and `max_order`.

_Default Behavior_

Default is 1.


---

###### model → surrogate → global → function_train → kick_rank

# kick_rank

The increment in rank employed during each iteration of the rank adaptation.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 2

**Description**

The `adapt_rank` procedure increments the rank on each iteration, using an increment defined by `kick_rank`

_Default Behavior_

Default is 1.


---

###### model → surrogate → global → function_train → max_cross_iterations

# max_cross_iterations

Maximum number of iterations for cross-approximation during a rank adaptation.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 1

**Description**

The number of iterations for the cross-approximation algorithm is limited within the `adapt_rank` procedure.


---

###### model → surrogate → global → function_train → max_cv_order_candidates

# max_cv_order_candidates

Limit the number of cross-validation candidates for basis order

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ ushort max

**Description**

When generating refinement candidates by advancing the upper bound used for a cross-validation range, the number of cross-validation can eventually become excessive and hamper algorithm progress. This control limits the maximum number of candidates, which when active, will increase the lower bound for this range as the upper bound is advanced.


---

###### model → surrogate → global → function_train → max_cv_rank_candidates

# max_cv_rank_candidates

Limit the number of cross-validation candidates for rank

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ size_t max

**Description**

When generating refinement candidates by advancing the upper bound used for a cross-validation range, the number of cross-validation can eventually become excessive and hamper algorithm progress. This control limits the maximum number of candidates, which when active, will increase the lower bound for this range as the upper bound is advanced.


---

###### model → surrogate → global → function_train → max_order

# max_order

Maximum polynomial order of each univariate function within the functional tensor train.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 4

**Description**

This specification is inactive for curren adaptation options.

In future adaptation options (i.e., cross-approximation adaptation), this will specify the maximum order that can be obtained during adaptation, where polynomial order will start from `start_order` and be limited by `max_order`.

_Default Behavior_

Max order is unbounded (adaptive sweep will stop when CV error increases).


---

###### model → surrogate → global → function_train → max_rank

# max_rank

Limits the maximum rank that is explored during a rank adaptation.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 3

**Description**

The `adapt_rank` procedure increments the rank on each iteration, and the maximum value is limited by `max_rank`

_Default Behavior_

Max rank is unbounded (adaptive sweep will stop when CV error increases).


---

###### model → surrogate → global → function_train → max_solver_iterations

# max_solver_iterations

Maximum iterations in determining polynomial coefficients

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 100

**Description**

When using an iterative polynomial coefficient estimation approach, e.g., cross-validation-based solvers, limits the maximum iterations in the coefficient solver.


---

###### model → surrogate → global → function_train → regression_type

# regression_type

Type of solver for forming function train approximations by regression

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Regression Type | [ls](model-surrogate-global-function_train-regression_type-ls.html) | Use least squares solver for forming function train approximations by regression  
[rls2](model-surrogate-global-function_train-regression_type-rls2.html) | Use regularized regression solver for forming function train approximations  
  
**Description**

Function train approximations are formed based on regression for the set of coefficients described at `[function_train](../../usingdakota/reference/model-surrogate-global-function_train.html)`. Solver options include least squares and regularized regression, where the latter penalizes high-order terms to mitigate overfitting.

_Default Behavior_

The default regression solver is least squares.


---

###### model → surrogate → global → function_train → regression_type → ls

# ls

Use least squares solver for forming function train approximations by regression

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Least squares is the standard solver for forming function train approximations based on regression. This option does not include regularization.


---

###### model → surrogate → global → function_train → regression_type → rls2

# rls2

Use regularized regression solver for forming function train approximations

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [l2_penalty](model-surrogate-global-function_train-regression_type-rls2-l2_penalty.html) | Penalty value applied in regularized regression solver for function train approximations  
  
**Description**

RLS2 is a regularized least squares solver that applies an a L2 penalty to mitigate overfitting. It includes the option to specify a penalty parameter.


---

###### model → surrogate → global → function_train → regression_type → rls2 → l2_penalty

# l2_penalty

Penalty value applied in regularized regression solver for function train approximations

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

Regularized regression applies a penalty to aspects of the recovered solution (e.g., cardinality, higher-order oscillatory terms, et al.) to mitigate overfitting. This specification provides the scalar penalty parameter (its value is not currently adapted or cross-validated).

_Default Behavior_

If this penalty parameter is not specified, then we adopt the internal C3 default.


---

###### model → surrogate → global → function_train → response_scaling

# response_scaling

Perform bounds-scaling on response values prior to surrogate emulation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ use original data

**Description**

Particularly in multifidelity methods based on regression solutions at each level of a model hierarchy, scaling can be an issue since the magnitude of discrepancy data may decay rapidly and regression solvers may employ absolute tolerances in places.

By activating `response_scaling`, the set of response data used for each recovery is scaled to [0,1] based on the minimum value and range of the data set. This prevents loss of accuracy due to imbalances in scale across model levels.


---

###### model → surrogate → global → function_train → rounding_tolerance

# rounding_tolerance

An accuracy tolerance that is used to guide rounding during rank adaptation.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.e-10

**Description**

The rounding tolerance is used to measure how closely one can approximate a functional tensor train representation at a step of the optimization procedure using one of lower ranks. If this tolerance is small, then less compression is achieved but greater accuracy is obtained. If this tolerance is big, greater compression is achieved at the expense of accuracy.

_Default Behavior_

If this tolerance is not specified, then we adopt the internal C3 default.


---

###### model → surrogate → global → function_train → solver_tolerance

# solver_tolerance

Convergence tolerance for the optimizer used during the regression solve.

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 1.0e-10

**Description**


---

###### model → surrogate → global → function_train → start_order

# start_order

(Initial) polynomial order of each univariate function within the functional tensor train.

**Specification**

  * _Alias:_ order

  * _Arguments:_ INTEGER

  * _Default:_ 2

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [dimension_preference](model-surrogate-global-function_train-start_order-dimension_preference.html) | A set of weights specifying the realtive importance of each uncertain variable (dimension)  
  
**Description**

This specifies the initial polynomial order of each univariate function that makes up the functional tensor train decomposition. In the case of `adapt_order`, polynomial order starts from this value and is then advanced; its maximum value is limited by `max_order` and increments are controlled by `kick_order`.

_Default Behavior_

Default is 2.


---

###### model → surrogate → global → function_train → start_order → dimension_preference

# dimension_preference

A set of weights specifying the realtive importance of each uncertain variable (dimension)

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ isotropic grids

**Description**

A set of weights specifying the realtive importance of each uncertain variable (dimension). Using this specification leada to anisotropic integrations with differing refinement levels for different random dimensions.


---

###### model → surrogate → global → function_train → start_rank

# start_rank

The initial rank used for the starting point during a rank adaptation.

**Specification**

  * _Alias:_ rank

  * _Arguments:_ INTEGER

  * _Default:_ 2

**Description**

The `adapt_rank` procedure increments the rank on each iteration, starting from the rank defined by `start_rank`

_Default Behavior_

Default is 2.


---

###### model → surrogate → global → function_train → tensor_grid

# tensor_grid

Use sub-sampled tensor-product quadrature points to build a polynomial chaos expansion.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ regression with random sample set

**Description**

Tthe collocation grid is defined using a subset of tensor-product quadrature points: the order of the tensor-product grid is selected as one more than the expansion order in each dimension (to avoid sampling at roots of the basis polynomials) and then the tensor multi-index is uniformly sampled to generate a non-repeated subset of tensor quadrature points.


---

##### model → surrogate → global → gaussian_process

# gaussian_process

Gaussian Process surrogate model

**Specification**

  * _Alias:_ kriging

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | GP Implementation | [dakota](model-surrogate-global-gaussian_process-dakota.html) | Select the built in Gaussian Process surrogate  
[surfpack](model-surrogate-global-gaussian_process-surfpack.html) | Use the Surfpack version of Gaussian Process surrogates  
Optional | [export_approx_variance_file](model-surrogate-global-gaussian_process-export_approx_variance_file.html) | Output file for surrogate model variance evaluations  
  
**Description**

Use the Gaussian process (GP) surrogate from Surfpack, which is specified using the `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)` keyword.

An alternate version of GP surrogates was available in prior versions of Dakota. _For now, both versions are supported but the ``dakota`` version is deprecated and intended to be removed in a future release._

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._


---

###### model → surrogate → global → gaussian_process → dakota

# dakota

Select the built in Gaussian Process surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [point_selection](model-surrogate-global-gaussian_process-dakota-point_selection.html) | Enable greedy selection of well-spaced build points  
Optional | [trend](model-surrogate-global-gaussian_process-dakota-trend.html) | Choose a trend function for a Gaussian process surrogate  
  
**Description**

A second version of GP surrogates was available in prior versions of Dakota. _For now, both versions are supported but the ``dakota`` version is deprecated and intended to be removed in a future release._

Historically these models were drastically different, but in Dakota 5.1, they became quite similar. They now differ in that the Surfpack GP has a richer set of features/options and tends to be more accurate than the Dakota version. Due to how the Surfpack GP handles ill-conditioned correlation matrices (which significantly contributes to its greater accuracy), the `Surfpack` GP can be a factor of two or three slower than Dakota’s. As of Dakota 5.2, the Surfpack implementation is the default in all contexts except Bayesian calibration.

More details on the `gaussian_process` dakota model can be found in [[McF08](../../misc/bibliography.html#id197 "J. M. McFarland. Uncertainty Analysis for Computer Simulations through Validation and Calibration. PhD thesis, Vanderbilt University, Nashville, Tennesssee, 2008. available for download at http://etd.library.vanderbilt.edu/ETD-db/available/etd-03282008-125137/.")].

Dakota’s GP deals with ill-conditioning in two ways. First, when it encounters a non-invertible correlation matrix it iteratively increases the size of a “nugget,” but in such cases the resulting approximation smooths rather than interpolates the data. Second, it has a `point_selection` option (default off) that uses a greedy algorithm to select a well-spaced subset of points prior to the construction of the GP. In this case, the GP will only interpolate the selected subset. Typically, one should not need point selection in trust-region methods because a small number of points are used to develop a surrogate within each trust region. Point selection is most beneficial when constructing with a large number of points, typically more than order one hundred, though this depends on the number of variables and spacing of the sample points.

This differs from the `point_selection` option of the Dakota GP which initially chooses a well-spaced subset of points and finds the correlation parameters that are most likely for that one subset.


---

###### model → surrogate → global → gaussian_process → dakota → point_selection

# point_selection

Enable greedy selection of well-spaced build points

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no point selection

**Description**

The Dakota Gaussian Process model has a `point_selection` option (default off) that uses a greedy algorithm to select a well-spaced subset of points prior to the construction of the GP. In this case, the GP will only interpolate the selected subset. Typically, one should not need point selection in trust-region methods because a small number of points are used to develop a surrogate within each trust region. Point selection is most beneficial when constructing with a large number of points, typically more than order one hundred, though this depends on the number of variables and spacing of the sample points.


---

###### model → surrogate → global → gaussian_process → dakota → trend

# trend

Choose a trend function for a Gaussian process surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ reduced_quadratic

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Trend Order | [constant](model-surrogate-global-gaussian_process-dakota-trend-constant.html) | Constant trend function  
[linear](model-surrogate-global-gaussian_process-dakota-trend-linear.html) | Use a linear polynomial or trend function  
[reduced_quadratic](model-surrogate-global-gaussian_process-dakota-trend-reduced_quadratic.html) | Quadratic polynomials - main effects only  
  
**Description**

The only trend functions that are currently supported are polynomials.

The trend function is selected using the `trend` keyword, with options `constant`, `linear`, or `reduced_quadratic`. The `reduced_quadratic` trend function includes the main effects, but not mixed/interaction terms. The Surfpack GP (See `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)`) has the additional option of (a full) `quadratic`.


---

###### model → surrogate → global → gaussian_process → dakota → trend → constant

# constant

Constant trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → dakota → trend → linear

# linear

Use a linear polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → dakota → trend → reduced_quadratic

# reduced_quadratic

Quadratic polynomials - main effects only

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In 2 or more dimensions, this polynomial omits the interaction, or mixed, terms.


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file

# export_approx_variance_file

Output file for surrogate model variance evaluations

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no variance export to a file

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](model-surrogate-global-gaussian_process-export_approx_variance_file-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](model-surrogate-global-gaussian_process-export_approx_variance_file-annotated.html) | Selects annotated tabular file format  
[freeform](model-surrogate-global-gaussian_process-export_approx_variance_file-freeform.html) | Selects freeform file format  
  
**Description**

Specifies a file in which the points (input variable values) at which the surrogate model is evaluated and corresponding response _variance_ values computed by the surrogate model will be written. The response values are the surrogate’s predicted approximation to the truth model responses at those points.

_Usage Tips_

Dakota exports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](model-surrogate-global-gaussian_process-export_approx_variance_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](model-surrogate-global-gaussian_process-export_approx_variance_file-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](model-surrogate-global-gaussian_process-export_approx_variance_file-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → gaussian_process → export_approx_variance_file → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → gaussian_process → surfpack

# surfpack

Use the Surfpack version of Gaussian Process surrogates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [trend](model-surrogate-global-gaussian_process-surfpack-trend.html) | Choose a trend function for a Gaussian process surrogate  
Optional | [optimization_method](model-surrogate-global-gaussian_process-surfpack-optimization_method.html) | Change the optimization method used to compute hyperparameters  
Optional | [max_trials](model-surrogate-global-gaussian_process-surfpack-max_trials.html) | Max number of likelihood function evaluations  
Optional (Choose One) | Nugget | [nugget](model-surrogate-global-gaussian_process-surfpack-nugget.html) | Specify a nugget to handle ill-conditioning  
[find_nugget](model-surrogate-global-gaussian_process-surfpack-find_nugget.html) | Have Surfpack compute a nugget to handle ill-conditioning  
Optional | [correlation_lengths](model-surrogate-global-gaussian_process-surfpack-correlation_lengths.html) | Specify the correlation lengths for the Gaussian process  
Optional | [export_model](model-surrogate-global-gaussian_process-surfpack-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-gaussian_process-surfpack-import_model.html) | Import surrogate model from archive file  
  
**Description**

This keyword specifies the use of the Gaussian process that is incorporated in our surface fitting library called Surfpack.

Several user options are available:

  1. Optimization methods: Maximum Likelihood Estimation (MLE) is used to find the optimal values of the hyper-parameters governing the trend and correlation functions. By default the global optimization method DIRECT is used for MLE, but other options for the optimization method are available. See `[optimization_method](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-optimization_method.html)`. The total number of evaluations of the likelihood function can be controlled using the `max_trials` keyword followed by a positive integer. Note that the likelihood function does not require running the “truth” model, and is relatively inexpensive to compute.

  2. Trend Function: The GP models incorporate a parametric trend function whose purpose is to capture large-scale variations. See `[trend](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-trend.html)`.

  3. Correlation Lengths: Correlation lengths are usually optimized by Surfpack, however, the user can specify the lengths manually. See `[correlation_lengths](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-correlation_lengths.html)`.

  4. Ill-conditioning One of the major problems in determining the governing values for a Gaussian process or Kriging model is the fact that the correlation matrix can easily become ill-conditioned when there are too many input points close together. Since the predictions from the Gaussian process model involve inverting the correlation matrix, ill-conditioning can lead to poor predictive capability and should be avoided. Note that a sufficiently bad sample design could require correlation lengths to be so short that any interpolatory GP model would become inept at extrapolation and interpolation. The `surfpack` model handles ill-conditioning internally by default, but behavior can be modified using

  5. Gradient Enhanced Kriging (GEK). The `use_derivatives` keyword will cause the Surfpack GP to be constructed from a combination of function value and gradient information (if available). See notes in the Theory section.

**Theory**

_Gradient Enhanced Kriging_

Incorporating gradient information will only be beneficial if accurate and inexpensive derivative information is available, and the derivatives are not infinite or nearly so. Here “inexpensive” means that the cost of evaluating a function value plus gradient is comparable to the cost of evaluating only the function value, for example gradients computed by analytical, automatic differentiation, or continuous adjoint techniques. It is not cost effective to use derivatives computed by finite differences. In tests, GEK models built from finite difference derivatives were also significantly less accurate than those built from analytical derivatives. Note that GEK’s correlation matrix tends to have a significantly worse condition number than Kriging for the same sample design.

This issue was addressed by using a pivoted Cholesky factorization of Kriging’s correlation matrix (which is a small sub-matrix within GEK’s correlation matrix) to rank points by how much unique information they contain. This reordering is then applied to whole points (the function value at a point immediately followed by gradient information at the same point) in GEK’s correlation matrix. A standard non-pivoted Cholesky is then applied to the reordered GEK correlation matrix and a bisection search is used to find the last equation that meets the constraint on the (estimate of) condition number. The cost of performing pivoted Cholesky on Kriging’s correlation matrix is usually negligible compared to the cost of the non-pivoted Cholesky factorization of GEK’s correlation matrix. In tests, it also resulted in more accurate GEK models than when pivoted Cholesky or whole-point-block pivoted Cholesky was performed on GEK’s correlation matrix.


---

###### model → surrogate → global → gaussian_process → surfpack → correlation_lengths

# correlation_lengths

Specify the correlation lengths for the Gaussian process

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ internally computed correlation_lengths

**Description**

Directly specify `correlation_lengths` as a list of N real numbers where N is the number of input dimensions.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-gaussian_process-surfpack-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-gaussian_process-surfpack-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-gaussian_process-surfpack-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-gaussian_process-surfpack-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
Optional | [algebraic_file](model-surrogate-global-gaussian_process-surfpack-export_model-formats-algebraic_file.html) | Export surrogate model in algebraic format to a file  
Optional | [algebraic_console](model-surrogate-global-gaussian_process-surfpack-export_model-formats-algebraic_console.html) | Export surrogate model in algebraic format to the console  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → formats → algebraic_console

# algebraic_console

Export surrogate model in algebraic format to the console

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to the console (screen, or output file if Dakota was run using the -o option) in a human-readable “algebraic” format. The output contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the screen for the exported model matches exactly the output written to file when `algebraic_file` is specified. Use of `algebraic_file` is preferred over `algebraic_console`, which exists largely to provide a measure of backward compatibility.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → formats → algebraic_file

# algebraic_file

Export surrogate model in algebraic format to a file

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to a file in a human-readable “algebraic” format. The file is named using the pattern `{prefix}`.{response_descriptor}.alg. See `{response_descriptor}.alg.filename_prefix` for further information about exported surrogate file naming. The file contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the file matches exactly the output written to the console when `algebraic_console` is specified.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → gaussian_process → surfpack → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → gaussian_process → surfpack → find_nugget

# find_nugget

Have Surfpack compute a nugget to handle ill-conditioning

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ None

**Description**

By default, the Surfpack GP handles ill-conditioning and does not use a nugget. If the user wishes to specify a nugget, there are two approaches.

  * The user can specify the value of a nugget with `[nugget](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-nugget.html)`.

  * Have Surfpack find the optimal value of the nugget. This is specified by `[find_nugget](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-find_nugget.html)`. There are two options for find_nugget.

    * `find_nugget` = 1: assume that the reciprocal condition number of the correlation matrix R, rcondR, is zero and calculate the nugget needed to make the worst case of R not ill-conditioned.

    * `find_nugget` = 2: calculate rcondR, which requires a Cholesky factorization. If rcondR indicates that R is not ill-conditioned, then kriging uses the Cholesky factorization. Otherwise, if rcondR says R is ill conditioned, then kriging will calculate the nugget needed to make the worst case of R not ill conditioned. `find_nugget` = 1 and 2 are similar, the second option just takes more computation (the initial Cholesky factorization) for larger problems.


---

###### model → surrogate → global → gaussian_process → surfpack → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-gaussian_process-surfpack-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-gaussian_process-surfpack-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-gaussian_process-surfpack-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → gaussian_process → surfpack → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → gaussian_process → surfpack → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → gaussian_process → surfpack → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → gaussian_process → surfpack → max_trials

# max_trials

Max number of likelihood function evaluations

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → surfpack → nugget

# nugget

Specify a nugget to handle ill-conditioning

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ None

**Description**

By default, the Surfpack GP handles ill-conditioning and does not use a nugget. If the user wishes to specify a nugget, there are two approaches.

  * The user can specify the value of a nugget with `[nugget](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-nugget.html)`.

  * Have Surfpack find the optimal value of the nugget. This is specified by `[find_nugget](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack-find_nugget.html)`. There are two options for find_nugget.

    * `find_nugget` = 1: assume that the reciprocal condition number of the correlation matrix R, rcondR, is zero and calculate the nugget needed to make the worst case of R not ill-conditioned.

    * `find_nugget` = 2: calculate rcondR, which requires a Cholesky factorization. If rcondR indicates that R is not ill-conditioned, then kriging uses the Cholesky factorization. Otherwise, if rcondR says R is ill conditioned, then kriging will calculate the nugget needed to make the worst case of R not ill conditioned. `find_nugget` = 1 and 2 are similar, the second option just takes more computation (the initial Cholesky factorization) for larger problems.


---

###### model → surrogate → global → gaussian_process → surfpack → optimization_method

# optimization_method

Change the optimization method used to compute hyperparameters

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ global

**Description**

Select the optimization method to compute hyperparameters of the Gaussian Process by specifying one of these arguments:

  * `global` (default) - DIRECT method

  * `local` \- CONMIN method

  * `sampling` \- generates several random guesses and picks the candidate with greatest likelihood

  * `none` \- no optimization, pick the center of the feasible region

The `none` option, and the starting location of the `local` optimization, default to the center, in log(correlation length) scale, of the of feasible region.

Surfpack picks a small feasible region of correlation parameters.

Note that we have found the `global` optimization method to be the most robust.


---

###### model → surrogate → global → gaussian_process → surfpack → trend

# trend

Choose a trend function for a Gaussian process surrogate

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ reduced_quadratic

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Trend Order | [constant](model-surrogate-global-gaussian_process-surfpack-trend-constant.html) | Constant trend function  
[linear](model-surrogate-global-gaussian_process-surfpack-trend-linear.html) | Use a linear polynomial or trend function  
[reduced_quadratic](model-surrogate-global-gaussian_process-surfpack-trend-reduced_quadratic.html) | Quadratic polynomials - main effects only  
[quadratic](model-surrogate-global-gaussian_process-surfpack-trend-quadratic.html) | Use a quadratic polynomial or trend function  
  
**Description**

The only trend functions that are currently supported are polynomials.

The trend function is selected using the `trend` keyword, with options `constant`, `linear`, or `reduced_quadratic`. The `reduced_quadratic` trend function includes the main effects, but not mixed/interaction terms. The Surfpack GP (See `[surfpack](../../usingdakota/reference/model-surrogate-global-gaussian_process-surfpack.html)`) has the additional option of (a full) `quadratic`.


---

###### model → surrogate → global → gaussian_process → surfpack → trend → constant

# constant

Constant trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → surfpack → trend → linear

# linear

Use a linear polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → surfpack → trend → quadratic

# quadratic

Use a quadratic polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → gaussian_process → surfpack → trend → reduced_quadratic

# reduced_quadratic

Quadratic polynomials - main effects only

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

In 2 or more dimensions, this polynomial omits the interaction, or mixed, terms.


---

##### model → surrogate → global → import_build_points_file

# import_build_points_file

File containing points you wish to use to build a surrogate

**Specification**

  * _Alias:_ import_points_file samples_file

  * _Arguments:_ STRING

  * _Default:_ no point import from a file

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](model-surrogate-global-import_build_points_file-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](model-surrogate-global-import_build_points_file-annotated.html) | Selects annotated tabular file format  
[freeform](model-surrogate-global-import_build_points_file-freeform.html) | Selects freeform file format  
Optional | [active_only](model-surrogate-global-import_build_points_file-active_only.html) | Import only active variables from tabular data file  
  
**Description**

The `import_build_points_file` allows the user to specify a file that contains a list of points and truth model responses used to construct a surrogate model. These can be used by all methods that (explicitly, e.g. surrogate-based optimization, or implicitly, e.g. efficient global optimization) operate on a surrogate. In particular, these points and responses are used in place of truth model evaluations to construct the initial surrogate. When used to construct surrogate models or emulators these are often called build points or training data.

_Default Behavior_

By default, methods do not import points from a file.

_Usage Tips_

Dakota parses input files without regard to whitespace, but the import_build_points_file must be in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`

**Examples**

    method
      polynomial_chaos
        expansion_order = 4
        import_build_points_file = 'dakota_uq_rosenbrock_pce_import.annot.pts.dat'


---

###### model → surrogate → global → import_build_points_file → active_only

# active_only

Import only active variables from tabular data file

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

By default, files for tabular data imports are expected to contain columns for all variables, active and inactive. The keyword `active_only` indicates that the file to import contains only the active variables.

This option should only be used in contexts where the inactive variables have no influence, for example, building a surrogate over active variables, with the state variables held at nominal. It should not be used in more complex nested contexts, where the values of inactive variables are relevant to the function evaluations used to build the surrogate.


---

###### model → surrogate → global → import_build_points_file → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [use_variable_labels](model-surrogate-global-import_build_points_file-annotated-use_variable_labels.html) | Validate/use variable labels from tabular file header  
  
**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → import_build_points_file → annotated → use_variable_labels

# use_variable_labels

Validate/use variable labels from tabular file header

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When importing global surrogate training data (or challenge evaluation points) from a tabular data file containing a header ( `annotated` or `custom_annotated` `header`), this keyword toggles validation of variable labels present in the header row.

The labels are validated against the descriptors of the variables being imported to. If the tabular file labels can be rearranged to match the expected labels, the columns in the data file will be reordered on read to match the Dakota variable order. If the read labels are not a permutation of, nor equal to, the expected labels, an error will result.

_Default Behavior_

When not specified, variable labels will be read, but not strictly enforced or reordered. A warning will be issued if the variable labels are not as expected, and guidance offered if they can be permuted to match expectations.

_Expected Output_

Console output will be generated for any warnings, as well as to indicate whether any variable reordering is taking place.

_Usage Tips_

The use of this keyword is recommended when importing header-annotated tabular data files where the variables are appropriately labeled. Tabular files do not always contain response labels, so no attempt is made to disambiguate variable from response labels. The variable labels must appear contiguously in the header after any leading column IDs such as ‘eval_id’ or ‘interface’.

**Examples**

This example enforces variable labels for both build and challenge points

    model
      id_model = 'SURR'
      surrogate global
        polynomial quadratic
        import_build_points_file = 'dakota_surrogate_import.unc_fixedothers.dat'
          annotated  use_variable_labels
        challenge_points_file = 'dakota_surrogate_import.dat'
          annotated  use_variable_labels


---

###### model → surrogate → global → import_build_points_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](model-surrogate-global-import_build_points_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](model-surrogate-global-import_build_points_file-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](model-surrogate-global-import_build_points_file-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → import_build_points_file → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_build_points_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [use_variable_labels](model-surrogate-global-import_build_points_file-custom_annotated-header-use_variable_labels.html) | Validate/use variable labels from tabular file header  
  
**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_build_points_file → custom_annotated → header → use_variable_labels

# use_variable_labels

Validate/use variable labels from tabular file header

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When importing global surrogate training data (or challenge evaluation points) from a tabular data file containing a header ( `annotated` or `custom_annotated` `header`), this keyword toggles validation of variable labels present in the header row.

The labels are validated against the descriptors of the variables being imported to. If the tabular file labels can be rearranged to match the expected labels, the columns in the data file will be reordered on read to match the Dakota variable order. If the read labels are not a permutation of, nor equal to, the expected labels, an error will result.

_Default Behavior_

When not specified, variable labels will be read, but not strictly enforced or reordered. A warning will be issued if the variable labels are not as expected, and guidance offered if they can be permuted to match expectations.

_Expected Output_

Console output will be generated for any warnings, as well as to indicate whether any variable reordering is taking place.

_Usage Tips_

The use of this keyword is recommended when importing header-annotated tabular data files where the variables are appropriately labeled. Tabular files do not always contain response labels, so no attempt is made to disambiguate variable from response labels. The variable labels must appear contiguously in the header after any leading column IDs such as ‘eval_id’ or ‘interface’.

**Examples**

This example enforces variable labels for both build and challenge points

    model
      id_model = 'SURR'
      surrogate global
        polynomial quadratic
        import_build_points_file = 'dakota_surrogate_import.unc_fixedothers.dat'
          annotated  use_variable_labels
        challenge_points_file = 'dakota_surrogate_import.dat'
          annotated  use_variable_labels


---

###### model → surrogate → global → import_build_points_file → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_build_points_file → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### model → surrogate → global → import_challenge_points_file

# import_challenge_points_file

Datafile of points to assess surrogate quality

**Specification**

  * _Alias:_ challenge_points_file

  * _Arguments:_ STRING

  * _Default:_ no user challenge data

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](model-surrogate-global-import_challenge_points_file-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](model-surrogate-global-import_challenge_points_file-annotated.html) | Selects annotated tabular file format  
[freeform](model-surrogate-global-import_challenge_points_file-freeform.html) | Selects freeform file format  
Optional | [active_only](model-surrogate-global-import_challenge_points_file-active_only.html) | Import only active variables from tabular data file  
  
**Description**

Specifies a data file containing variable and response (truth) values, in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`

The surrogate is evaluated at the points in the file, and the surrogate (approximate) responses are compared against the truth results from the file. All metrics specified with `[metrics](../../usingdakota/reference/model-surrogate-global-metrics.html)` will be computed for the challenge data.


---

###### model → surrogate → global → import_challenge_points_file → active_only

# active_only

Import only active variables from tabular data file

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

By default, files for tabular data imports are expected to contain columns for all variables, active and inactive. The keyword `active_only` indicates that the file to import contains only the active variables.

This option should only be used in contexts where the inactive variables have no influence, for example, building a surrogate over active variables, with the state variables held at nominal. It should not be used in more complex nested contexts, where the values of inactive variables are relevant to the function evaluations used to build the surrogate.


---

###### model → surrogate → global → import_challenge_points_file → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [use_variable_labels](model-surrogate-global-import_challenge_points_file-annotated-use_variable_labels.html) | Validate/use variable labels from tabular file header  
  
**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → import_challenge_points_file → annotated → use_variable_labels

# use_variable_labels

Validate/use variable labels from tabular file header

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When importing global surrogate training data (or challenge evaluation points) from a tabular data file containing a header ( `annotated` or `custom_annotated` `header`), this keyword toggles validation of variable labels present in the header row.

The labels are validated against the descriptors of the variables being imported to. If the tabular file labels can be rearranged to match the expected labels, the columns in the data file will be reordered on read to match the Dakota variable order. If the read labels are not a permutation of, nor equal to, the expected labels, an error will result.

_Default Behavior_

When not specified, variable labels will be read, but not strictly enforced or reordered. A warning will be issued if the variable labels are not as expected, and guidance offered if they can be permuted to match expectations.

_Expected Output_

Console output will be generated for any warnings, as well as to indicate whether any variable reordering is taking place.

_Usage Tips_

The use of this keyword is recommended when importing header-annotated tabular data files where the variables are appropriately labeled. Tabular files do not always contain response labels, so no attempt is made to disambiguate variable from response labels. The variable labels must appear contiguously in the header after any leading column IDs such as ‘eval_id’ or ‘interface’.

**Examples**

This example enforces variable labels for both build and challenge points

    model
      id_model = 'SURR'
      surrogate global
        polynomial quadratic
        import_build_points_file = 'dakota_surrogate_import.unc_fixedothers.dat'
          annotated  use_variable_labels
        challenge_points_file = 'dakota_surrogate_import.dat'
          annotated  use_variable_labels


---

###### model → surrogate → global → import_challenge_points_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](model-surrogate-global-import_challenge_points_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](model-surrogate-global-import_challenge_points_file-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](model-surrogate-global-import_challenge_points_file-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### model → surrogate → global → import_challenge_points_file → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_challenge_points_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [use_variable_labels](model-surrogate-global-import_challenge_points_file-custom_annotated-header-use_variable_labels.html) | Validate/use variable labels from tabular file header  
  
**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_challenge_points_file → custom_annotated → header → use_variable_labels

# use_variable_labels

Validate/use variable labels from tabular file header

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When importing global surrogate training data (or challenge evaluation points) from a tabular data file containing a header ( `annotated` or `custom_annotated` `header`), this keyword toggles validation of variable labels present in the header row.

The labels are validated against the descriptors of the variables being imported to. If the tabular file labels can be rearranged to match the expected labels, the columns in the data file will be reordered on read to match the Dakota variable order. If the read labels are not a permutation of, nor equal to, the expected labels, an error will result.

_Default Behavior_

When not specified, variable labels will be read, but not strictly enforced or reordered. A warning will be issued if the variable labels are not as expected, and guidance offered if they can be permuted to match expectations.

_Expected Output_

Console output will be generated for any warnings, as well as to indicate whether any variable reordering is taking place.

_Usage Tips_

The use of this keyword is recommended when importing header-annotated tabular data files where the variables are appropriately labeled. Tabular files do not always contain response labels, so no attempt is made to disambiguate variable from response labels. The variable labels must appear contiguously in the header after any leading column IDs such as ‘eval_id’ or ‘interface’.

**Examples**

This example enforces variable labels for both build and challenge points

    model
      id_model = 'SURR'
      surrogate global
        polynomial quadratic
        import_build_points_file = 'dakota_surrogate_import.unc_fixedothers.dat'
          annotated  use_variable_labels
        challenge_points_file = 'dakota_surrogate_import.dat'
          annotated  use_variable_labels


---

###### model → surrogate → global → import_challenge_points_file → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See description of parent `custom_annotated`


---

###### model → surrogate → global → import_challenge_points_file → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### model → surrogate → global → mars

# mars

Multivariate Adaptive Regression Spline (MARS)

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [max_bases](model-surrogate-global-mars-max_bases.html) | Maximum number of MARS bases  
Optional | [interpolation](model-surrogate-global-mars-interpolation.html) | MARS model interpolation type  
Optional | [export_model](model-surrogate-global-mars-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-mars-import_model.html) | Import surrogate model from archive file  
  
**Description**

This surface fitting method uses multivariate adaptive regression splines from the MARS3.5 package [[Fri91](../../misc/bibliography.html#id97 "J. H. Friedman. Multivariate adaptive regression splines. Annals of Statistics, 19\(1\):1–141, March 1991.")] developed at Stanford University.

The MARS reference material does not indicate the minimum number of data points that are needed to create a MARS surface model. However, in practice it has been found that at least \\(n_{c_{quad}}\\) , and sometimes as many as 2 to 4 times \\(n_{c_{quad}}\\) , data points are needed to keep the MARS software from terminating. Provided that sufficient data samples can be obtained, MARS surface models can be useful in SBO and OUU applications, as well as in the prediction of global trends throughout the parameter space.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

The form of the MARS model is based on the following expression:

\\[\hat{f}(\mathbf{x})=\sum_{m=1}^{M}a_{m}B_{m}(\mathbf{x})\\]

where the \\(a_{m}\\) are the coefficients of the truncated power basis functions \\(B_{m}\\) , and \\(M\\) is the number of basis functions. The MARS software partitions the parameter space into subregions, and then applies forward and backward regression methods to create a local surface model in each subregion. The result is that each subregion contains its own basis functions and coefficients, and the subregions are joined together to produce a smooth, \\(C^{2}\\) -continuous surface model.

MARS is a nonparametric surface fitting method and can represent complex multimodal data trends. The regression component of MARS generates a surface model that is not guaranteed to pass through all of the response data values. Thus, like the quadratic polynomial model, it provides some smoothing of the data.


---

###### model → surrogate → global → mars → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-mars-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-mars-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → mars → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → mars → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-mars-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-mars-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → mars → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → mars → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → mars → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-mars-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-mars-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-mars-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → mars → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → mars → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → mars → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → mars → interpolation

# interpolation

MARS model interpolation type

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Interpolation Order | [linear](model-surrogate-global-mars-interpolation-linear.html) | Linear interpolation  
[cubic](model-surrogate-global-mars-interpolation-cubic.html) | Cubic interpolation  
  
**Description**

The MARS model interpolation type: linear or cubic.


---

###### model → surrogate → global → mars → interpolation → cubic

# cubic

Cubic interpolation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use cubic interpolation in the MARS model.


---

###### model → surrogate → global → mars → interpolation → linear

# linear

Linear interpolation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Use linear interpolation in the MARS model.


---

###### model → surrogate → global → mars → max_bases

# max_bases

Maximum number of MARS bases

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

The maximum number of basis functions allowed in the MARS approximation model.


---

##### model → surrogate → global → metrics

# metrics

Compute surrogate quality metrics

**Topics**

surrogate_models

**Specification**

  * _Alias:_ diagnostics

  * _Arguments:_ STRINGLIST

  * _Default:_ No diagnostics

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [cross_validation](model-surrogate-global-metrics-cross_validation.html) | Perform k-fold cross validation  
Optional | [press](model-surrogate-global-metrics-press.html) | Leave-one-out cross validation  
  
**Description**

Diagnostic metrics assess the goodness of fit of a global surrogate to its training data.

The default diagnostics are:

  * `root_mean_squared`

  * `mean_abs`

  * `rsquared`

Additional available diagnostics include

  * `sum_squared`

  * `mean_squared`

  * `sum_abs`

  * `max_abs`

The keywords `press` and `cross_validation` further specify leave-one-out or k-fold cross validation, respectively, for all of the active metrics from above.

_Usage Tips_ When specified, the `metrics` keyword must be followed by a list of quoted strings, each of which activates a metric.

**Examples**

This example input fragment constructs a quadratic polynomial surrogate and computes four metrics on the fit, both with and without 5-fold cross validation. (Also see dakota/share/dakota/test/dakota_surrogate_import.in for additional examples.)

    model
      surrogate global
        polynomial quadratic
        metrics = "root_mean_squared" "sum_abs" "mean_abs" "max_abs"
        cross_validation folds = 5

**Theory**

Most of these diagnostics refer to some operation on the residuals (the difference between the surrogate model and the truth model at the data points upon which the surrogate is built).

For example, `sum_squared` refers to the sum of the squared residuals, and `mean_abs` refers to the mean of the absolute value of the residuals. `rsquared` refers to the R-squared value typically used in regression analysis (the proportion of the variability in the response that can be accounted for by the surrogate model). Care should be taken when interpreting metrics, for example, errors may be near zero for interpolatory models or rsquared may not be applicable for non-polynomial models.


---

###### model → surrogate → global → metrics → cross_validation

# cross_validation

Perform k-fold cross validation

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ No cross validation

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Partition Control | [folds](model-surrogate-global-metrics-cross_validation-folds.html) | Number of cross validation folds  
[percent](model-surrogate-global-metrics-cross_validation-percent.html) | Percent data per cross validation fold  
  
**Description**

General k-fold cross validation may be performed by specifying `cross_validation`. The cross-validation statistics will be calculated for all metrics.

Cross validation may further specify:

  * `folds`, the number of folds into which to divide the build data (between 2 and number of data points) or

  * `percent`, the fraction of data (between 0 and 0.5) to use in each fold.

These will be adjusted as needed based on the number of available training points. The default number of folds k = 10, or 0.1


---

###### model → surrogate → global → metrics → cross_validation → folds

# folds

Number of cross validation folds

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 10

**Description**

Number of folds (partitions) of the training data to use in cross validation (default 10).


---

###### model → surrogate → global → metrics → cross_validation → percent

# percent

Percent data per cross validation fold

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

  * _Default:_ 0.1

**Description**

Percent of the training data to use in each cross validation fold (default 0.1).


---

###### model → surrogate → global → metrics → press

# press

Leave-one-out cross validation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ No PRESS cross validation

**Description**

Leave-one-out (PRESS) cross validation may be performed by specifying `press`. The cross-validation statistics will be calculated for all metrics.


---

##### model → surrogate → global → minimum_points

# minimum_points

Construct surrogate with minimum number of points

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The minimum is d+1, for d input dimensions, except for polynomials. See parent page.


---

##### model → surrogate → global → moving_least_squares

# moving_least_squares

Moving Least Squares surrogate models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [basis_order](model-surrogate-global-moving_least_squares-basis_order.html) | Polynomial order for the MLS bases  
Optional | [weight_function](model-surrogate-global-moving_least_squares-weight_function.html) | Selects the weight function for the MLS model  
Optional | [export_model](model-surrogate-global-moving_least_squares-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-moving_least_squares-import_model.html) | Import surrogate model from archive file  
  
**Description**

Moving least squares is a further generalization of weighted least squares where the weighting is “moved” or recalculated for every new point where a prediction is desired [[Nea04](../../misc/bibliography.html#id208 "A. Nealen. A short-as-possible introduction to the least squares, weighted least squares, and moving least squares methods for scattered data approximation and interpolation. Technical Report, Discrete Geometric Modeling Group, Technishe Universitaet, Berlin, Germany, 2004.")].

_The implementation of moving least squares is still under development._ It tends to work well in trust region optimization methods where the surrogate model is constructed in a constrained region over a few points. The present implementation may not work as well globally.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

Moving Least Squares can be considered a more specialized version of linear regression models. In linear regression, one usually attempts to minimize the sum of the squared residuals, where the residual is defined as the difference between the surrogate model and the true model at a fixed number of points.

In weighted least squares, the residual terms are weighted so the determination of the optimal coefficients governing the polynomial regression function, denoted by \\(\hat{f}({\bf x})\\) , are obtained by minimizing the weighted sum of squares at N data points:

\\[\sum_{n=1}^{N}w_{n}({\parallel \hat{f}({\bf x_{n}})-f({\bf x_{n}})\parallel})\\]


---

###### model → surrogate → global → moving_least_squares → basis_order

# basis_order

Polynomial order for the MLS bases

**Specification**

  * _Alias:_ poly_order

  * _Arguments:_ INTEGER

**Description**

The polynomial order for the moving least squares basis function (default = 2).


---

###### model → surrogate → global → moving_least_squares → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-moving_least_squares-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-moving_least_squares-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → moving_least_squares → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → moving_least_squares → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-moving_least_squares-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-moving_least_squares-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → moving_least_squares → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → moving_least_squares → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → moving_least_squares → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-moving_least_squares-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-moving_least_squares-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-moving_least_squares-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → moving_least_squares → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → moving_least_squares → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → moving_least_squares → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → moving_least_squares → weight_function

# weight_function

Selects the weight function for the MLS model

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

The weight function decays as a function of distance from the training data. Specify one of:

  * 1 (default): exponential decay in weight function; once differentiable MLS model

  * 2: twice differentiable MLS model

  * 3: three times differentiable MLS model


---

##### model → surrogate → global → neural_network

# neural_network

Artificial neural network model

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [max_nodes](model-surrogate-global-neural_network-max_nodes.html) | Maximum number of hidden layer nodes  
Optional | [range](model-surrogate-global-neural_network-range.html) | Range for neural network random weights  
Optional | [random_weight](model-surrogate-global-neural_network-random_weight.html) | (Inactive) Random weight control  
Optional | [export_model](model-surrogate-global-neural_network-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-neural_network-import_model.html) | Import surrogate model from archive file  
  
**Description**

Dakota’s artificial neural network surrogate is a stochastic layered perceptron network, with a single hidden layer. Weights for the input layer are chosen randomly, while those in the hidden layer are estimated from data using a variant of the Zimmerman direct training approach [[Zim96](../../misc/bibliography.html#id314 "D. C. Zimmerman. Genetic algorithms for navigating expensive and complex design spaces. September 1996. Final Report for Sandia National Laboratories contract AO-7736 CA 02.")].

This typically yields lower training cost than traditional neural networks, yet good out-of-sample performance. This is helpful in surrogate-based optimization and optimization under uncertainty, where multiple surrogates may be repeatedly constructed during the optimization process, e.g., a surrogate per response function, and a new surrogate for each optimization iteration.

The neural network is a non parametric surface fitting method. Thus, along with Kriging (Gaussian Process) and MARS, it can be used to model data trends that have slope discontinuities as well as multiple maxima and minima. However, unlike Kriging, the neural network surrogate is not guaranteed to interpolate the data from which it was constructed.

This surrogate can be constructed from fewer than \\(n_{c_{quad}}\\) data points, however, it is a good rule of thumb to use at least \\(n_{c_{quad}}\\) data points when possible.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

The form of the neural network model is

\\[\hat{f}(\mathbf{x}) \approx \tanh\left\\{ \mathbf{A}_{1} \tanh\left( \mathbf{A}_{0}^{T} \mathbf{x} +\theta_{0}^T \right)+\theta_{1} \right\\}\\]

where \\(\mathbf{x}\\) is the evaluation point in \\(n\\) -dimensional parameter space; the terms \\(\mathbf{A}_{0}, \theta_{0}\\) are the random input layer weight matrix and bias vector, respectively; and \\(\mathbf{A}_{1}, \theta_{1}\\) are a weight vector and bias scalar, respectively, estimated from training data. These coefficients are analogous to the polynomial coefficients obtained from regression to training data. The neural network uses a cross validation-based orthogonal matching pursuit solver to determine the optimal number of nodes and to solve for the weights and offsets.


---

###### model → surrogate → global → neural_network → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-neural_network-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-neural_network-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → neural_network → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → neural_network → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-neural_network-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-neural_network-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
Optional | [algebraic_file](model-surrogate-global-neural_network-export_model-formats-algebraic_file.html) | Export surrogate model in algebraic format to a file  
Optional | [algebraic_console](model-surrogate-global-neural_network-export_model-formats-algebraic_console.html) | Export surrogate model in algebraic format to the console  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → neural_network → export_model → formats → algebraic_console

# algebraic_console

Export surrogate model in algebraic format to the console

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to the console (screen, or output file if Dakota was run using the -o option) in a human-readable “algebraic” format. The output contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the screen for the exported model matches exactly the output written to file when `algebraic_file` is specified. Use of `algebraic_file` is preferred over `algebraic_console`, which exists largely to provide a measure of backward compatibility.


---

###### model → surrogate → global → neural_network → export_model → formats → algebraic_file

# algebraic_file

Export surrogate model in algebraic format to a file

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to a file in a human-readable “algebraic” format. The file is named using the pattern `{prefix}`.{response_descriptor}.alg. See `{response_descriptor}.alg.filename_prefix` for further information about exported surrogate file naming. The file contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the file matches exactly the output written to the console when `algebraic_console` is specified.


---

###### model → surrogate → global → neural_network → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → neural_network → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → neural_network → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-neural_network-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-neural_network-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-neural_network-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → neural_network → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → neural_network → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → neural_network → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → neural_network → max_nodes

# max_nodes

Maximum number of hidden layer nodes

**Topics**

surrogate_models

**Specification**

  * _Alias:_ nodes

  * _Arguments:_ INTEGER

  * _Default:_ numTrainingData - 1

**Description**

Limits the maximum number of hidden layer nodes in the neural network model. The default is to use one less node than the number of available training data points yielding a fully-determined linear least squares problem. However, reducing the number of nodes can help reduce overfitting and more importantly, can drastically reduce surrogate construction time when building from a large data set. (Historically, Dakota limited the number of nodes to 100.)

The keyword `max_nodes` provides an upper bound. Dakota’s orthogonal matching pursuit algorithm may further reduce the effective number of nodes in the final model to achieve better generalization to unseen points.


---

###### model → surrogate → global → neural_network → random_weight

# random_weight

(Inactive) Random weight control

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This option is not currently in use and is likely to be removed


---

###### model → surrogate → global → neural_network → range

# range

Range for neural network random weights

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ REAL

**Description**

Controls the range of the input layer random weights in the neural network model. The default range is 2.0, resulting in weights in (-1, 1). These weights are applied after the training inputs have been scaled into [-0.8, 0.8].


---

##### model → surrogate → global → polynomial

# polynomial

Polynomial surrogate model

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Polynomial Order | [basis_order](model-surrogate-global-polynomial-basis_order.html) | Polynomial order  
[linear](model-surrogate-global-polynomial-linear.html) | Use a linear polynomial or trend function  
[quadratic](model-surrogate-global-polynomial-quadratic.html) | Use a quadratic polynomial or trend function  
[cubic](model-surrogate-global-polynomial-cubic.html) | Use a cubic polynomial  
Optional | [export_model](model-surrogate-global-polynomial-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-polynomial-import_model.html) | Import surrogate model from archive file  
  
**Description**

Linear, quadratic, and cubic polynomial surrogate models are available in Dakota. The utility of the simple polynomial models stems from two sources:

  * over a small portion of the parameter space, a low-order polynomial model is often an accurate approximation to the true data trends

  * the least-squares procedure provides a surface fit that smooths out noise in the data.

Local surrogate-based optimization methods ( `[surrogate_based_local](../../usingdakota/reference/method-surrogate_based_local.html)`) are often successful when using polynomial models, particularly quadratic models. However, a polynomial surface fit may not be the best choice for modeling data trends globally over the entire parameter space, unless it is known a priori that the true data trends are close to linear, quadratic, or cubic. See [[MM95](../../misc/bibliography.html#id205 "R. H. Myers and D. C. Montgomery. Response Surface Methodology: Process and Product Optimization Using Designed Experiments. John Wiley & Sons, Inc., New York, 1995.")] for more information on polynomial models.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

The form of the linear polynomial model is

\\[\hat{f}(\mathbf{x}) \approx c_{0}+\sum_{i=1}^{n}c_{i}x_{i}\\]

the form of the quadratic polynomial model is:

\\[\hat{f}(\mathbf{x}) \approx c_{0}+\sum_{i=1}^{n}c_{i}x_{i} +\sum_{i=1}^{n}\sum_{j \ge i}^{n}c_{ij}x_{i}x_{j}\\]

and the form of the cubic polynomial model is:

\\[\hat{f}(\mathbf{x}) \approx c_{0}+\sum_{i=1}^{n}c_{i}x_{i} +\sum_{i=1}^{n}\sum_{j \ge i}^{n}c_{ij}x_{i}x_{j} +\sum_{i=1}^{n}\sum_{j \ge i}^{n}\sum_{k \ge j}^{n} c_{ijk}x_{i}x_{j}x_{k}\\]

In all of the polynomial models, \\(\hat{f}(\mathbf{x})\\) is the response of the polynomial model; the \\(x_{i},x_{j},x_{k}\\) terms are the components of the \\(n\\) -dimensional design parameter values; the \\(c_{0}\\) , \\(c_{i}\\) , \\(c_{ij}\\) , \\(c_{ijk}\\) terms are the polynomial coefficients, and \\(n\\) is the number of design parameters. The number of coefficients, \\(n_{c}\\) , depends on the order of polynomial model and the number of design parameters. For the linear polynomial:

\\[n_{c_{linear}}=n+1\\]

for the quadratic polynomial:

\\[n_{c_{quad}}=\frac{(n+1)(n+2)}{2}\\]

and for the cubic polynomial:

\\[n_{c_{cubic}}=\frac{(n^{3}+6 n^{2}+11 n+6)}{6}\\]

There must be at least \\(n_{c}\\) data samples in order to form a fully determined linear system and solve for the polynomial coefficients. In Dakota, a least-squares approach involving a singular value decomposition numerical method is applied to solve the linear system.


---

###### model → surrogate → global → polynomial → basis_order

# basis_order

Polynomial order

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

The polynomial order for the polynomial regression model (default = 2).


---

###### model → surrogate → global → polynomial → cubic

# cubic

Use a cubic polynomial

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → polynomial → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-polynomial-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-polynomial-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → polynomial → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → polynomial → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-polynomial-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-polynomial-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
Optional | [algebraic_file](model-surrogate-global-polynomial-export_model-formats-algebraic_file.html) | Export surrogate model in algebraic format to a file  
Optional | [algebraic_console](model-surrogate-global-polynomial-export_model-formats-algebraic_console.html) | Export surrogate model in algebraic format to the console  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → polynomial → export_model → formats → algebraic_console

# algebraic_console

Export surrogate model in algebraic format to the console

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to the console (screen, or output file if Dakota was run using the -o option) in a human-readable “algebraic” format. The output contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the screen for the exported model matches exactly the output written to file when `algebraic_file` is specified. Use of `algebraic_file` is preferred over `algebraic_console`, which exists largely to provide a measure of backward compatibility.


---

###### model → surrogate → global → polynomial → export_model → formats → algebraic_file

# algebraic_file

Export surrogate model in algebraic format to a file

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to a file in a human-readable “algebraic” format. The file is named using the pattern `{prefix}`.{response_descriptor}.alg. See `{response_descriptor}.alg.filename_prefix` for further information about exported surrogate file naming. The file contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the file matches exactly the output written to the console when `algebraic_console` is specified.


---

###### model → surrogate → global → polynomial → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → polynomial → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → polynomial → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-polynomial-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-polynomial-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-polynomial-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → polynomial → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → polynomial → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → polynomial → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → polynomial → linear

# linear

Use a linear polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

###### model → surrogate → global → polynomial → quadratic

# quadratic

Use a quadratic polynomial or trend function

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page


---

##### model → surrogate → global → radial_basis

# radial_basis

Radial basis function (RBF) model

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [bases](model-surrogate-global-radial_basis-bases.html) | Initial number of radial basis functions  
Optional | [max_pts](model-surrogate-global-radial_basis-max_pts.html) | Maximum number of RBF CVT points  
Optional | [min_partition](model-surrogate-global-radial_basis-min_partition.html) | (Inactive) Minimum RBF partition  
Optional | [max_subsets](model-surrogate-global-radial_basis-max_subsets.html) | Number of trial RBF subsets  
Optional | [export_model](model-surrogate-global-radial_basis-export_model.html) | Exports surrogate model in user-specified format(s)  
Optional | [import_model](model-surrogate-global-radial_basis-import_model.html) | Import surrogate model from archive file  
  
**Description**

Radial basis functions \\(\phi\\) are functions whose value typically depends on the distance from a center point, called the centroid, \\({\bf c}\\) .

The surrogate model approximation comprises a sum of K weighted radial basis functions:

\\[\hat{f}({\bf x})=\sum_{k=1}^{K}w_{k}\phi({\parallel {\bf x} - {\bf c_{k}} \parallel})\\]

These basis functions take many forms, but Gaussian kernels or splines are most common. The Dakota implementation uses a Gaussian radial basis function. The weights are determined via a linear least squares solution approach. See [[Orr96](../../misc/bibliography.html#id224 "M. J. L. Orr. Introduction to radial basis function networks. Technical Report, University of Edinburgh, Edinburgh, Scotland, 1996.")] for more details.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._


---

###### model → surrogate → global → radial_basis → bases

# bases

Initial number of radial basis functions

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Initial number of radial basis functions. The default value is the smaller of the number of training points and 100.


---

###### model → surrogate → global → radial_basis → export_model

# export_model

Exports surrogate model in user-specified format(s)

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-radial_basis-export_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required | [formats](model-surrogate-global-radial_basis-export_model-formats.html) | Formats for surrogate model export  
  
**Description**

Export the surrogate for later re-import to Dakota or evaluation using the surfpack exectuable ( `bin/surfpack`) or a user-developed tool. Export format is controlled using the `formats` specification. Four formats are available in Dakota; however, not all have been enabled for all surrogates.

The four formats are:

  * `text_archive` \- Plain-text, machine-readable archive for re-import or use with the surfpack executable

  * `binary_archive` \- Binary, machine-readable archive for re-import or use with the surfpack executable

  * `algebraic_file` \- Plain-text, human-readable file intended for use with user-created tools; not compatible with Dakota or the surfpack executable

  * `algebraic_console` \- Print the model in algebraic format to the screen; not compatible with Dakota or the surfpack executable

These global surrogates can be exported in all four formats:

  * Gaussian process (keyword `gaussian_process` surfpack)

  * Artificial neural network (keyword `neural_network`)

  * Radial basis Funtions (keyword `radial_basis`)

  * Polynomial (keyword `polynomial`)

However, for experimental Gaussian Process and polynomial models as well as Multivariate Adaptive Regression Spline (keyword `mars`) and moving least squares (keyword `moving_least_squares`) models, only `text_archive` and `binary_archive` formats may be used.

Currently, no other surrogate models can be exported.

_Default Behavior_

No export.

_Expected Output_

Output depends on selected format; see the `formats` specification.

_Additional Discussion_

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → radial_basis → export_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → radial_basis → export_model → formats

# formats

Formats for surrogate model export

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [text_archive](model-surrogate-global-radial_basis-export_model-formats-text_archive.html) | Surrogate model plain-text archive file format  
Optional | [binary_archive](model-surrogate-global-radial_basis-export_model-formats-binary_archive.html) | Surrogate model binary archive file format  
Optional | [algebraic_file](model-surrogate-global-radial_basis-export_model-formats-algebraic_file.html) | Export surrogate model in algebraic format to a file  
Optional | [algebraic_console](model-surrogate-global-radial_basis-export_model-formats-algebraic_console.html) | Export surrogate model in algebraic format to the console  
  
**Description**

Select from among the 2-4 available export formats available for this surrogate. Multiple selections are permitted.

See `export_model` and the entries for the format selection keywords for further information.


---

###### model → surrogate → global → radial_basis → export_model → formats → algebraic_console

# algebraic_console

Export surrogate model in algebraic format to the console

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to the console (screen, or output file if Dakota was run using the -o option) in a human-readable “algebraic” format. The output contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the screen for the exported model matches exactly the output written to file when `algebraic_file` is specified. Use of `algebraic_file` is preferred over `algebraic_console`, which exists largely to provide a measure of backward compatibility.


---

###### model → surrogate → global → radial_basis → export_model → formats → algebraic_file

# algebraic_file

Export surrogate model in algebraic format to a file

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

After the surrogate model has been built, Dakota will export it to a file in a human-readable “algebraic” format. The file is named using the pattern `{prefix}`.{response_descriptor}.alg. See `{response_descriptor}.alg.filename_prefix` for further information about exported surrogate file naming. The file contains sufficient information for the user to (re)construct and evaluate the model outside of Dakota.

_Expected Output_

<p>The format depends on the type of surrogate model, but in general will include a LaTeX-like representation of the analytic form of the model to aid tool development, all needed model hyperparameters, and headers describing the shape or dimension of the provided data.

The output written to the file matches exactly the output written to the console when `algebraic_console` is specified.


---

###### model → surrogate → global → radial_basis → export_model → formats → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → radial_basis → export_model → formats → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → radial_basis → import_model

# import_model

Import surrogate model from archive file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [filename_prefix](model-surrogate-global-radial_basis-import_model-filename_prefix.html) | User-customizable portion of exported/imported surrogate model filenames  
Required (Choose One) | Surrogate Import Format | [text_archive](model-surrogate-global-radial_basis-import_model-text_archive.html) | Surrogate model plain-text archive file format  
[binary_archive](model-surrogate-global-radial_basis-import_model-binary_archive.html) | Surrogate model binary archive file format  
  
**Description**

Imports a surrogate model from a file in `binary_archive` or `text_archive` format, typically instead of constructing it from generated or imported data. Importing can result in significant time savings with some models, such as Gaussian processes. The file from which to import is further specified with the child keywords `filename_prefix` and `binary_archive` or `text_archive`.

_Default Behavior_

When used in the context of an iteratively adapted or rebuilt surrogate, the imported model will only replace the initial surrogate model. Subsequent surrogate model builds require imported build points or a design of experiments method specified through `dace_method_pointer`.

_Usage Tips_

When importing a surrogate model, _it is crucial_ that the `[global](../../usingdakota/reference/model-surrogate-global.html)` surrogate model part of the Dakota input file be identical for export and import, except for changing `export_model` and its child keywords to those needed for `import_model`. Any other keywords such as specifying a dace_iterator or imported points must remain intact to satisfy internal surrogate constructor requirements.

All variables over which the surrogate model was built (typically the active variables) when exported must be present in the Dakota study that imports it. The variables in the importing study will be matched by descriptor to those in the surrogate model. This allows the order and type of variables to change between export and import, for example to fix unimportant parameters as state variables even if they were previously active design variables.

For example, suppose the original Dakota study for the surrogate build had active uniform uncertain variables: a, b, c, d, and inactive state variables s, t, and the surrogate gets built over a, b, c, d. The follow-on study could perform design optimization over active design variables b and d, with inactive state variables a, c, s, t, held at fixed `initial_state` values. In this case the values of a, b, c, d will get mapped to the surrogate and s, t dropped when evaluating it. So it is important the state values for s and t haven’t changed in some way that invalidates the surrogate.

While it might be possible to import surrogates externally-generated using the `surfpack` binary or the experimental `dakota`.surrogates Python module, it is untested and not the primary use case for this capability.

**Examples**

The following model block exports a Gaussian process after it’s built:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          export_model
            filename_prefix = 'gp_export'
            formats = binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'

And this model block imports it using a nearly identical model specification:

    model
      id_model = 'SURR_M'
      surrogate global
        dace_method_pointer = 'DACE'
        experimental_gaussian_process
          metrics 'root_mean_squared'
            cross_validation folds 5
          import_model
            filename_prefix = 'gp_export'
        binary_archive
          export_approx_points_file 'gp_values.dat'
          export_approx_variance_file 'gp_variance.dat'


---

###### model → surrogate → global → radial_basis → import_model → binary_archive

# binary_archive

Surrogate model binary archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a binary archive format.

Experimental surrogates will export to (import from) binary archives named `{prefix}`.{response_descriptor}.bin.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.bsps, in which ‘bsps’ stands for binary Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → radial_basis → import_model → filename_prefix

# filename_prefix

User-customizable portion of exported/imported surrogate model filenames

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ exported_surrogate

**Description**

Dakota surrogate model files are exported and imported using one file per response, per requested format. The files are named using the pattern `{prefix}`.{response_descriptor}.{extension}. This `filename_prefix` keyword is used to supply the prefix portion of the pattern.

The `response_descriptor` portion of the pattern is filled in using the response `[descriptors](../../usingdakota/reference/responses-descriptors.html)` provided by the user (or, if none are specified, descriptors automatically generated by Dakota). Extension is a three or four letter string that depends on the format.

**Examples**

This input snippet directs Dakota to write one algebraic format file and one binary archive file for each response. The names of the files will follow the patterns `my_surrogate`.{response_descriptor}.alg (for the algebraic files) and `my_surrogate`.{response_descriptor}.bsps (for the binary files).

    surrogate global gaussian_process surfpack
      export_model
        filename_prefix = 'my_surrogate'
          formats
            algebraic_file
            binary_archive


---

###### model → surrogate → global → radial_basis → import_model → text_archive

# text_archive

Surrogate model plain-text archive file format

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

When specified, the surrogate model will be exported (or imported) using a plain-text archive format.

Experimental surrogates will export to (import from) text archives named `{prefix}`.{response_descriptor}.txt.

Other (Surfpack) surrogates will export/import using files named `{prefix}`.{response_descriptor}.sps, in which ‘sps’ stands for Surfpack surrogate.

See `filename_prefix` for further information about surrogate file naming.

**Examples**

The Dakota examples include a demonstration of using the surfpack executable with an exported model file.


---

###### model → surrogate → global → radial_basis → max_pts

# max_pts

Maximum number of RBF CVT points

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Maximum number of CVT points to use in generating each RBF center. basis computing centroid of each. Defaults to 10 * ( `[bases](../../usingdakota/reference/model-surrogate-global-radial_basis-bases.html)`). Reducing this will reduce model build time.


---

###### model → surrogate → global → radial_basis → max_subsets

# max_subsets

Number of trial RBF subsets

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

Number of passes to take to identify the best subset of basis functions to use. Defaults to the smaller of 3 * ( `[bases](../../usingdakota/reference/model-surrogate-global-radial_basis-bases.html)`) and 100.


---

###### model → surrogate → global → radial_basis → min_partition

# min_partition

(Inactive) Minimum RBF partition

**Topics**

surrogate_models

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This option currently has no effect and will likely be removed.


---

##### model → surrogate → global → recommended_points

# recommended_points

Construct surrogate with recommended number of points

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This is the default option. It requires 5*d build points for d input dimensions, except for polynomial models. See parent page.


---

##### model → surrogate → global → reuse_points

# reuse_points

Surrogate model training data reuse control

**Topics**

surrogate_models

**Specification**

  * _Alias:_ reuse_samples

  * _Arguments:_ None

  * _Default:_ all for import; none otherwise

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Reuse Domain | [all](model-surrogate-global-reuse_points-all.html) | Option for `reuse_points`  
[region](model-surrogate-global-reuse_points-region.html) | Option for `reuse_points`  
[none](model-surrogate-global-reuse_points-none.html) | Option for `reuse_points`  
  
**Description**

Dakota’s global surrogate methods rely on training data, which can either come from evaluation of a “truth” model, which is generated by the method specified with `[dace_method_pointer](../../usingdakota/reference/model-surrogate-global-dace_method_pointer.html)`, from a file of existing training data, identified by `[import_build_points_file](../../usingdakota/reference/model-surrogate-global-import_build_points_file.html)`, or both.

The `reuse_points` keyword controls the amount of training data used in building a surrogate model, either initially, or during iterative rebuild, as in surrogate-based optimization. If `[import_build_points_file](../../usingdakota/reference/model-surrogate-global-import_build_points_file.html)` is specified, `reuse_points` controls how the file contents are used. If used during iterative rebuild, it controls what data from previous surrogate builds is reused in building the current model.

  * `all` (default for file import) - use all points in the file or available from previous builds

  * `region` \- use only the points falling in the current trust region (see `[surrogate_based_local](../../usingdakota/reference/method-surrogate_based_local.html)`)

  * `none` (default when no import) - ignore the contents of the file or previous build points, and gather new training data using the specified DACE method


---

###### model → surrogate → global → reuse_points → all

# all

Option for `reuse_points`

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This is described on the parent page.


---

###### model → surrogate → global → reuse_points → none

# none

Option for `reuse_points`

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This is described on the parent page.


---

###### model → surrogate → global → reuse_points → region

# region

Option for `reuse_points`

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

This is described on the parent page.


---

##### model → surrogate → global → total_points

# total_points

Specified number of training points

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ recommended_points

**Description**

See parent page.


---

##### model → surrogate → global → truth_model_pointer

# truth_model_pointer

A surrogate model pointer that guides a method to whether it should use a surrogate model or compute truth function evaluations

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Description**

Dakota methods use global surrogate models to compute surrogate function approximations. They also need to know the true function evaluations. A global surrogate model now must have a truth_model_pointer keyword to decide for the method whether to evaluate the global surrogate model, or compute the true function evaluations if truth_model_pointer = ‘TRUTH’.


---

##### model → surrogate → global → use_derivatives

# use_derivatives

Use derivative data to construct surrogate models

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ use function values only

**Description**

The `use_derivatives` flag specifies that any available derivative information should be used in global approximation builds, for those global surrogate types that support it (currently, polynomial regression and the Surfpack Gaussian process).

However, it’s use with Surfpack Gaussian process is not recommended.


---

#### model → surrogate → id_surrogates

# id_surrogates

Identifies the subset of the response functions by number that are to be approximated (the default is all functions).

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

  * _Default:_ All response functions are approximated

**Description**

In the `surrogate` model case, the specification first allows a mixture of surrogate and actual response mappings through the use of the optional `id_surrogates` specification. This identifies the subset of the response functions by number that are to be approximated (the default is all functions). The valid response function identifiers range from 1 through the total number of response functions (see `[response_functions](../../usingdakota/reference/responses-response_functions.html)`).


---

#### model → surrogate → local

# local

Build a locally accurate surrogate from data at a single point

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [taylor_series](model-surrogate-local-taylor_series.html) | Construct a Taylor Series expansion around a point  
Required | [truth_model_pointer](model-surrogate-local-truth_model_pointer.html) | Pointer to specify a “truth” model, from which to construct a surrogate  
  
**Description**

Local approximations use value, gradient, and possibly Hessian data from a single point to form a series expansion for approximating data in the vicinity of this point.

The currently available local approximation is the `taylor_series` selection.

The truth model to be used to generate the value/gradient/Hessian data used in the series expansion is identified through the required `truth_model_pointer` specification. The use of a model pointer (as opposed to an interface pointer) allows additional flexibility in defining the approximation. In particular, the derivative specification for the truth model may differ from the derivative specification for the approximation, and the truth model results being approximated may involve a model recursion (e.g., the values/gradients from a nested model).


---

##### model → surrogate → local → taylor_series

# taylor_series

Construct a Taylor Series expansion around a point

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The Taylor series model is purely a local approximation method. That is, it provides local trends in the vicinity of a single point in parameter space.

The order of the Taylor series may be either first-order or second-order, which is automatically determined from the gradient and Hessian specifications in the responses specification (see `[responses](../../usingdakota/reference/responses.html)` for info on how to specify gradients and Hessians) for the truth model

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

The first-order Taylor series expansion is: anchor eq-taylor1 f{equation} hat{f}({bf x}) approx f({bf x}_0) + nabla_{bf x} f({bf x}_0)^T ({bf x} - {bf x}_0) f} and the second-order expansion is: anchor eq-taylor2 f{equation} hat{f}({bf x}) approx f({bf x}_0) + nabla_{bf x} f({bf x}_0)^T ({bf x} - {bf x}_0) + frac{1}{2} ({bf x} - {bf x}_0)^T nabla^2_{bf x} f({bf x}_0) ({bf x} - {bf x}_0) f}

where \\({\bf x}_0\\) is the expansion point in \\(n\\) -dimensional parameter space and \\(f({\bf x}_0)\\) , \\(\nabla_{\bf x} f({\bf x}_0)\\) , and \\(\nabla^2_{\bf x} f({\bf x}_0)\\) are the computed response value, gradient, and Hessian at the expansion point, respectively.

As dictated by the responses specification used in building the local surrogate, the gradient may be analytic or numerical and the Hessian may be analytic, numerical, or based on quasi-Newton secant updates.

In general, the Taylor series model is accurate only in the region of parameter space that is close to \\({\bf x}_0\\) . While the accuracy is limited, the first-order Taylor series model reproduces the correct value and gradient at the point \\(\mathbf{x}_{0}\\) , and the second-order Taylor series model reproduces the correct value, gradient, and Hessian. This consistency is useful in provably-convergent surrogate-based optimization. The other surface fitting methods do not use gradient information directly in their models, and these methods rely on an external correction procedure in order to satisfy the consistency requirements of provably-convergent SBO.


---

##### model → surrogate → local → truth_model_pointer

# truth_model_pointer

Pointer to specify a “truth” model, from which to construct a surrogate

**Topics**

block_pointer

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Description**

This must point to a model block, identified by `[id_model](../../usingdakota/reference/model-id_model.html)`. That model will be run to generate training data, from which a surrogate model will be constructed.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.


---

#### model → surrogate → multipoint

# multipoint

Construct a surrogate from multiple existing training points

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Multipoint Surrogate | [tana](model-surrogate-multipoint-tana.html) | Local multi-point model via two-point nonlinear approximation  
[qmea](model-surrogate-multipoint-qmea.html) | Multi-point surrogate approximation based on QMEA algorithm  
Required | [truth_model_pointer](model-surrogate-multipoint-truth_model_pointer.html) | Pointer to specify a “truth” model, from which to construct a surrogate  
  
**Description**

Multipoint approximations use data from previous design points to improve the accuracy of local approximations. The data often comes from the current and previous iterates of a minimization algorithm.

Currently, only the Two-point Adaptive Nonlinearity Approximation (TANA-3) method of [[XG98](../../misc/bibliography.html#id313 "S. Xu and R. V. Grandhi. Effective two-point function approximation for design optimization. AIAA J., 36\(12\):2269–2275, 1998.")] is supported with the `tana` keyword.

The truth model to be used to generate the value/gradient data used in the approximation is identified through the required `truth_model_pointer` specification.


---

##### model → surrogate → multipoint → qmea

# qmea

Multi-point surrogate approximation based on QMEA algorithm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The Quadratic Multipoint Exponential Approximation (QMEA) builds a multi-point approximation from values and gradients at multiple expansion points. It is a generalization of the two-point exponential approximation (TPEA).

This capability is b experimental.


---

##### model → surrogate → multipoint → tana

# tana

Local multi-point model via two-point nonlinear approximation

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

_TANA_ stands for Two Point Adaptive Nonlinearity Approximation.

The TANA-3 method [[XG98](../../misc/bibliography.html#id313 "S. Xu and R. V. Grandhi. Effective two-point function approximation for design optimization. AIAA J., 36\(12\):2269–2275, 1998.")] is a multipoint approximation method based on the two point exponential approximation [[FRB90](../../misc/bibliography.html#id89 "G. M. Fadel, M. F. Riley, and J.-F. M. Barthelemy. Two point exponential approximation method for structural optimization. Structural Optimization, 2\(2\):117–124, 1990.")]. This approach involves a Taylor series approximation in intermediate variables where the powers used for the intermediate variables are selected to match information at the current and previous expansion points.

_Known Issue: When using discrete variables, there have been sometimes significant differences in surrogate behavior observed across computing platforms in some cases. The cause has not yet been fully diagnosed and is currently under investigation. In addition, guidance on appropriate construction and use of surrogates with discrete variables is under development. In the meantime, users should therefore be aware that there is a risk of inaccurate results when using surrogates with discrete variables._

**Theory**

The form of the TANA model is:

\\[\hat{f}({\bf x}) \approx f({\bf x}_2) + \sum_{i=1}^n \frac{\partial f}{\partial x_i}({\bf x}_2) \frac{x_{i,2}^{1-p_i}}{p_i} (x_i^{p_i} - x_{i,2}^{p_i}) + \frac{1}{2} \epsilon({\bf x}) \sum_{i=1}^n (x_i^{p_i} - x_{i,2}^{p_i})^2\\]

where \\(n\\) is the number of variables and:

\\[p_i = 1 + \ln \left[ \frac{\frac{\partial f}{\partial x_i}({\bf x}_1)} {\frac{\partial f}{\partial x_i}({\bf x}_2)} \right] \left/ \ln \left[ \frac{x_{i,1}}{x_{i,2}} \right] \right. \epsilon({\bf x}) = \frac{H}{\sum_{i=1}^n (x_i^{p_i} - x_{i,1}^{p_i})^2 + \sum_{i=1}^n (x_i^{p_i} - x_{i,2}^{p_i})^2} H = 2 \left[ f({\bf x}_1) - f({\bf x}_2) - \sum_{i=1}^n \frac{\partial f}{\partial x_i}({\bf x}_2) \frac{x_{i,2}^{1-p_i}}{p_i} (x_{i,1}^{p_i} - x_{i,2}^{p_i}) \right]\\]

and \\({\bf x}_2\\) and \\({\bf x}_1\\) are the current and previous expansion points. Prior to the availability of two expansion points, a first-order Taylor series is used.


---

##### model → surrogate → multipoint → truth_model_pointer

# truth_model_pointer

Pointer to specify a “truth” model, from which to construct a surrogate

**Topics**

block_pointer

**Specification**

  * _Alias:_ actual_model_pointer

  * _Arguments:_ STRING

**Description**

This must point to a model block, identified by `[id_model](../../usingdakota/reference/model-id_model.html)`. That model will be run to generate training data, from which a surrogate model will be constructed.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.


---

### model → variables_pointer

# variables_pointer

Specify which variables block will be included with this model block

**Topics**

block_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ model use of last variables parsed

**Description**

The `variables_pointer` is used to specify which variables block will be used by the model, by cross-referencing with `[id_variables](../../usingdakota/reference/variables-id_variables.html)` keyword in the `[variables](../../usingdakota/reference/variables.html)` block.

See [Pointers](../topics/pointers.html#topic-block-pointer) for details about pointers.

_Default Behavior_

When a variables pointer is not specified, the model will use the last variables block parsed from the input file.

