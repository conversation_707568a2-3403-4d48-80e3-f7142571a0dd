<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SensitivityWidget</class>
 <widget class="QWidget" name="SensitivityWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>350</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Sensitivity Analysis Parameters</string>
  </property>
  <layout class="QVBoxLayout" name="mainLayout">
   <property name="spacing">
    <number>10</number>
   </property>
   <property name="leftMargin">
    <number>5</number>
   </property>
   <property name="topMargin">
    <number>5</number>
   </property>
   <property name="rightMargin">
    <number>5</number>
   </property>
   <property name="bottomMargin">
    <number>5</number>
   </property>
   <item>
    <widget class="QGroupBox" name="methodGroup">
     <property name="title">
      <string>Sensitivity Analysis Method</string>
     </property>
     <layout class="QVBoxLayout" name="methodLayout">
      <item>
       <layout class="QFormLayout" name="methodFormLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="methodLabel">
          <property name="text">
           <string>Method:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QComboBox" name="methodCombo">
          <item>
           <property name="text">
            <string>sampling</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>local_reliability</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>polynomial_chaos</string>
           </property>
          </item>
          <item>
           <property name="text">
            <string>stoch_collocation</string>
           </property>
          </item>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <widget class="QLabel" name="methodDescription">
        <property name="text">
         <string>Sampling: Monte Carlo and Latin Hypercube sampling for sensitivity analysis</string>
        </property>
        <property name="wordWrap">
         <bool>true</bool>
        </property>
        <property name="styleSheet">
         <string>color: #666; font-style: italic; padding: 5px;</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="samplingGroup">
     <property name="title">
      <string>Sampling Parameters</string>
     </property>
     <layout class="QFormLayout" name="samplingFormLayout">
      <item row="0" column="0">
       <widget class="QLabel" name="samplesLabel">
        <property name="text">
         <string>Sample Size:</string>
        </property>
       </widget>
      </item>
      <item row="0" column="1">
       <widget class="QSpinBox" name="samplesSpin">
        <property name="minimum">
         <number>10</number>
        </property>
        <property name="maximum">
         <number>100000</number>
        </property>
        <property name="value">
         <number>1000</number>
        </property>
        <property name="suffix">
         <string> samples</string>
        </property>
       </widget>
      </item>
      <item row="1" column="0">
       <widget class="QLabel" name="samplingTypeLabel">
        <property name="text">
         <string>Sampling Type:</string>
        </property>
       </widget>
      </item>
      <item row="1" column="1">
       <widget class="QComboBox" name="samplingTypeCombo">
        <item>
         <property name="text">
          <string>random</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>lhs</string>
         </property>
        </item>
        <item>
         <property name="text">
          <string>incremental_lhs</string>
         </property>
        </item>
       </widget>
      </item>
      <item row="2" column="0">
       <widget class="QLabel" name="seedLabel">
        <property name="text">
         <string>Random Seed:</string>
        </property>
       </widget>
      </item>
      <item row="2" column="1">
       <widget class="QSpinBox" name="seedSpin">
        <property name="minimum">
         <number>1</number>
        </property>
        <property name="maximum">
         <number>2147483647</number>
        </property>
        <property name="value">
         <number>12345</number>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="analysisGroup">
     <property name="title">
      <string>Analysis Options</string>
     </property>
     <layout class="QVBoxLayout" name="analysisLayout">
      <item>
       <widget class="QCheckBox" name="varianceBasedCheck">
        <property name="text">
         <string>Variance-Based Decomposition</string>
        </property>
        <property name="toolTip">
         <string>Compute Sobol indices for global sensitivity analysis</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QFormLayout" name="varianceFormLayout">
        <item row="0" column="0">
         <widget class="QLabel" name="toleranceLabel">
          <property name="text">
           <string>Convergence Tolerance:</string>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="QDoubleSpinBox" name="toleranceSpin">
          <property name="decimals">
           <number>6</number>
          </property>
          <property name="minimum">
           <double>0.000001000000000</double>
          </property>
          <property name="maximum">
           <double>0.100000000000000</double>
          </property>
          <property name="value">
           <double>0.001000000000000</double>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <widget class="QGroupBox" name="outputGroup">
     <property name="title">
      <string>Output Options</string>
     </property>
     <layout class="QVBoxLayout" name="outputLayout">
      <item>
       <widget class="QCheckBox" name="correlationMatrixCheck">
        <property name="text">
         <string>Generate Correlation Matrix</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="sensitivityIndicesCheck">
        <property name="text">
         <string>Compute Sensitivity Indices</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="mainEffectsCheck">
        <property name="text">
         <string>Main Effects Analysis</string>
        </property>
        <property name="checked">
         <bool>true</bool>
        </property>
       </widget>
      </item>
      <item>
       <widget class="QCheckBox" name="interactionEffectsCheck">
        <property name="text">
         <string>Interaction Effects Analysis</string>
        </property>
       </widget>
      </item>
     </layout>
    </widget>
   </item>
   <item>
    <spacer name="verticalSpacer">
     <property name="orientation">
      <enum>Qt::Vertical</enum>
     </property>
     <property name="sizeHint" stdset="0">
      <size>
       <width>20</width>
       <height>40</height>
      </size>
     </property>
    </spacer>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui> 