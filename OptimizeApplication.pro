QMAKE_CXXFLAGS += -g
QT += core gui widgets charts

# Qt 5.14.2 compatibility configuration
greaterThan(QT_MAJOR_VERSION, 4): QT += widgets
CONFIG += c++14
CONFIG += warn_on

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

# Application information
TARGET = OptimizeApplication
TEMPLATE = app
VERSION = 1.0.0

# Application information is defined in source code

# Source file directory structure
INCLUDEPATH += src \
               src/ui \
               src/widgets \
               src/core \
               src/utils \
               src/SARibbonBar \
               "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include" \
               "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets" \
               "D:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore"

# Application source files
SOURCES += \
    src/main.cpp \
    src/ui/MainWindow.cpp \
    src/widgets/CustomTreeWidget.cpp \
    src/widgets/ParamWidget.cpp \
    src/widgets/OptimizeWidget.cpp \
    src/widgets/SensitivityWidget.cpp \
    src/widgets/UQWidget.cpp \
    src/widgets/InputWidget.cpp \
    src/widgets/OutputWidget.cpp \
    src/widgets/SolverOutputWidget.cpp \
    src/widgets/ChartWidget.cpp \
    src/widgets/RibbonWidget.cpp \
    src/widgets/PipeWidget.cpp \
    src/widgets/TubeWidget.cpp \
    src/core/ApplicationCore.cpp \
    src/core/ConfigManager.cpp \
    src/core/ThemeManager.cpp \
    src/utils/Logger.cpp \
    src/utils/Utils.cpp \
    src/data/InputData.cpp \
    src/data/IFileExample.cpp

# SARibbonBar source files
SOURCES += \
    src/SARibbonBar/SARibbonBar.cpp \
    src/SARibbonBar/SARibbonMainWindow.cpp \
    src/SARibbonBar/SARibbonCategory.cpp \
    src/SARibbonBar/SARibbonPannel.cpp \
    src/SARibbonBar/SARibbonToolButton.cpp \
    src/SARibbonBar/SARibbonActionsManager.cpp \
    src/SARibbonBar/SARibbonApplicationButton.cpp \
    src/SARibbonBar/SARibbonButtonGroupWidget.cpp \
    src/SARibbonBar/SARibbonCategoryLayout.cpp \
    src/SARibbonBar/SARibbonCheckBox.cpp \
    src/SARibbonBar/SARibbonComboBox.cpp \
    src/SARibbonBar/SARibbonContextCategory.cpp \
    src/SARibbonBar/SARibbonControlButton.cpp \
    src/SARibbonBar/SARibbonCtrlContainer.cpp \
    src/SARibbonBar/SARibbonCustomizeData.cpp \
    src/SARibbonBar/SARibbonCustomizeDialog.cpp \
    src/SARibbonBar/SARibbonCustomizeWidget.cpp \
    src/SARibbonBar/SARibbonDrawHelper.cpp \
    src/SARibbonBar/SARibbonElementCreateDelegate.cpp \
    src/SARibbonBar/SARibbonElementManager.cpp \
    src/SARibbonBar/SARibbonGallery.cpp \
    src/SARibbonBar/SARibbonGalleryGroup.cpp \
    src/SARibbonBar/SARibbonGalleryItem.cpp \
    src/SARibbonBar/SARibbonLineEdit.cpp \
    src/SARibbonBar/SARibbonLineWidgetContainer.cpp \
    src/SARibbonBar/SARibbonMenu.cpp \
    src/SARibbonBar/SARibbonPannelItem.cpp \
    src/SARibbonBar/SARibbonPannelLayout.cpp \
    src/SARibbonBar/SARibbonPannelOptionButton.cpp \
    src/SARibbonBar/SARibbonQuickAccessBar.cpp \
    src/SARibbonBar/SARibbonSeparatorWidget.cpp \
    src/SARibbonBar/SARibbonStackedWidget.cpp \
    src/SARibbonBar/SARibbonTabBar.cpp \
    src/SARibbonBar/SAFramelessHelper.cpp \
    src/SARibbonBar/SAWindowButtonGroup.cpp \
    src/SARibbonBar/saribbonuser.cpp

# Application header files
HEADERS += \
    src/ui/MainWindow.h \
    src/widgets/CustomTreeWidget.h \
    src/widgets/ParamWidget.h \
    src/widgets/OptimizeWidget.h \
    src/widgets/SensitivityWidget.h \
    src/widgets/UQWidget.h \
    src/widgets/InputWidget.h \
    src/widgets/OutputWidget.h \
    src/widgets/SolverOutputWidget.h \
    src/widgets/ChartWidget.h \
    src/widgets/RibbonWidget.h \
    src/widgets/PipeWidget.h \
    src/widgets/TubeWidget.h \
    src/core/ApplicationCore.h \
    src/core/ConfigManager.h \
    src/core/ThemeManager.h \
    src/utils/Logger.h \
    src/utils/Common.h \
    src/data/InputData.h \
    src/data/IFileExample.h

# SARibbonBar header files
HEADERS += \
    src/SARibbonBar/SARibbonBar.h \
    src/SARibbonBar/SARibbonMainWindow.h \
    src/SARibbonBar/SARibbonCategory.h \
    src/SARibbonBar/SARibbonPannel.h \
    src/SARibbonBar/SARibbonToolButton.h \
    src/SARibbonBar/SARibbonActionsManager.h \
    src/SARibbonBar/SARibbonApplicationButton.h \
    src/SARibbonBar/SARibbonButtonGroupWidget.h \
    src/SARibbonBar/SARibbonCategoryLayout.h \
    src/SARibbonBar/SARibbonCheckBox.h \
    src/SARibbonBar/SARibbonComboBox.h \
    src/SARibbonBar/SARibbonContextCategory.h \
    src/SARibbonBar/SARibbonControlButton.h \
    src/SARibbonBar/SARibbonCtrlContainer.h \
    src/SARibbonBar/SARibbonCustomizeData.h \
    src/SARibbonBar/SARibbonCustomizeDialog.h \
    src/SARibbonBar/SARibbonCustomizeWidget.h \
    src/SARibbonBar/SARibbonDrawHelper.h \
    src/SARibbonBar/SARibbonElementCreateDelegate.h \
    src/SARibbonBar/SARibbonElementManager.h \
    src/SARibbonBar/SARibbonGallery.h \
    src/SARibbonBar/SARibbonGalleryGroup.h \
    src/SARibbonBar/SARibbonGalleryItem.h \
    src/SARibbonBar/SARibbonGlobal.h \
    src/SARibbonBar/SARibbonLineEdit.h \
    src/SARibbonBar/SARibbonLineWidgetContainer.h \
    src/SARibbonBar/SARibbonMenu.h \
    src/SARibbonBar/SARibbonPannelItem.h \
    src/SARibbonBar/SARibbonPannelLayout.h \
    src/SARibbonBar/SARibbonPannelOptionButton.h \
    src/SARibbonBar/SARibbonQuickAccessBar.h \
    src/SARibbonBar/SARibbonSeparatorWidget.h \
    src/SARibbonBar/SARibbonStackedWidget.h \
    src/SARibbonBar/SARibbonTabBar.h \
    src/SARibbonBar/SAFramelessHelper.h \
    src/SARibbonBar/SAWindowButtonGroup.h \
    src/SARibbonBar/saribbonuser.h

FORMS += \
    src/ui/MainWindow.ui \
    src/widgets/OptimizeWidget.ui \
    src/widgets/SensitivityWidget.ui \
    src/widgets/UQWidget.ui \
    src/widgets/InputWidget.ui \
    src/widgets/OutputWidget.ui \
    src/widgets/SolverOutputWidget.ui \
    src/widgets/PipeWidget.ui \
    src/widgets/TubeWidget.ui

# Resource files
RESOURCES += \
    resources/icons.qrc \
    src/SARibbonBar/resource.qrc

# Enable Qt Ribbon support
CONFIG += qtribbon

# SARibbonBar configuration - disable DLL import/export since we're compiling sources directly
DEFINES += SA_RIBBON_BAR_NO_EXPORT

# Windows specific configuration
win32 {
    QMAKE_TARGET_COMPANY = "Optimize Solutions"
    QMAKE_TARGET_PRODUCT = "Optimize Application"
    QMAKE_TARGET_DESCRIPTION = "Advanced Optimization Application"
    QMAKE_TARGET_COPYRIGHT = "Copyright (C) 2024"
    
    # Windows specific defines
    DEFINES += _CRT_SECURE_NO_WARNINGS NOMINMAX
}

# Debug configuration
CONFIG(debug, debug|release) {
    DEFINES += DEBUG_MODE
    TARGET = $${TARGET}_debug
}

# Release configuration
CONFIG(release, debug|release) {
    DEFINES += QT_NO_DEBUG_OUTPUT
}

# Deployment configuration
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target

# Deployment
DESTDIR = bin
MOC_DIR = build
OBJECTS_DIR = build
RCC_DIR = build
UI_DIR = build

# Compiler flags
QMAKE_CXXFLAGS += /std:c++14 /W4

# Disable specific warnings if needed
QMAKE_CXXFLAGS += /wd4996  # Disable deprecation warnings
