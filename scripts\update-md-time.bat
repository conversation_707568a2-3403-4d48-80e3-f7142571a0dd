@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Markdown Time Rules Batch Script
:: 在写入markdown文件时自动插入当前时间的规则脚本

if "%1"=="" (
    echo 使用方法: update-md-time.bat [文件路径] [操作类型]
    echo 操作类型: update ^(默认^), append, create
    echo 示例: update-md-time.bat program.md update
    exit /b 1
)

set "FILE_PATH=%1"
set "ACTION=%2"
if "%ACTION%"=="" set "ACTION=update"

:: 使用PowerShell获取当前时间
for /f "usebackq delims=" %%i in (`powershell -command "Get-Date -Format 'yyyy年MM月dd日 HH:mm:ss'"`) do (
    set "TIMESTAMP=%%i"
)

if not exist "%FILE_PATH%" (
    if "%ACTION%"=="create" (
        echo 创建新的markdown文件: %FILE_PATH%
        (
            echo # 新文档
            echo.
            echo **创建时间：** !TIMESTAMP!
            echo **最后更新：** !TIMESTAMP!
            echo.
            echo ---
            echo.
        ) > "%FILE_PATH%"
        echo 已创建文件并添加时间戳: !TIMESTAMP!
    ) else (
        echo 错误: 文件不存在 %FILE_PATH%
        exit /b 1
    )
) else (
    if "%ACTION%"=="update" (
        echo 更新现有文件的时间戳: %FILE_PATH%
        
        :: 使用PowerShell更新时间戳
        powershell -command "$content = Get-Content '%FILE_PATH%' -Raw -Encoding UTF8; $timestamp = '!TIMESTAMP!'; $patterns = @('\*\*最后更新：\*\*\s+[^\r\n]+', '\*\*更新时间：\*\*\s+[^\r\n]+', '\*\*文档更新时间：\*\*\s+[^\r\n]+'); $updated = $false; foreach ($pattern in $patterns) { if ($content -match $pattern) { $content = $content -replace $pattern, ('**最后更新：** ' + $timestamp); $updated = $true; break; } } if (-not $updated) { $content += \"`r`n`r`n**最后更新：** $timestamp\"; } Set-Content -Path '%FILE_PATH%' -Value $content -Encoding UTF8;"
        
        echo 已更新时间戳: !TIMESTAMP!
        
    ) else if "%ACTION%"=="append" (
        echo 在文件末尾追加时间戳: %FILE_PATH%
        echo. >> "%FILE_PATH%"
        echo **最后更新：** !TIMESTAMP! >> "%FILE_PATH%"
        echo 已追加时间戳: !TIMESTAMP!
    )
)

echo 操作完成。 