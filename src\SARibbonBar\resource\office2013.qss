﻿/* start ribbon set*/

SARibbonMainWindow {
  background-color: white;
  border: 1px solid #707070;
}

/*SARibbonBar*/

SARibbonBar {
  background-color: white;
  border: solid #707070;
  border-width: 1px 1px 0px 1px;
}

/*SARibbonButtonGroupWidget*/

SARibbonButtonGroupWidget {
  background-color: transparent;
}

SARibbonPannel > SARibbonButtonGroupWidget {
  border: 1px solid #c2d0df;
}

/*SARibbonQuickAccessBar*/

SARibbonQuickAccessBar {
  background-color: #cee8fc;
}

/*SARibbonCtrlContainer*/

SARibbonCtrlContainer {
  background-color: transparent;
}

/*SARibbonCategory*/

SARibbonCategory:focus {
  outline: none;
}

SARibbonCategory {
  background-color: #fcfdfe;
}

/*SARibbonStackedWidget*/

SARibbonStackedWidget {
  background-color: white;
  border: 1px solid #c5d2e0;
  border-top-width: 0px;
}

SARibbonStackedWidget:focus {
  outline: none;
}

/*SARibbonApplicationButton*/

SARibbonApplicationButton {
  color: white;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
  background-color: #2b579a;
}

SARibbonApplicationButton::hover {
  background-color: #5888d0;
}

SARibbonApplicationButton::pressed {
  background-color: #3369b9;
}

SARibbonApplicationButton:focus {
  outline: none;
}

/*SARibbonTabBar*/

SARibbonTabBar {
  background-color: transparent;
}

SARibbonTabBar::tab {
  color: #5779af;
  border: none;
  background: transparent;
  margin-top: 0px;
  margin-right: 0px;
  margin-left: 6px;
  margin-bottom: 0px;
  min-width: 60px;
  max-width: 200px;
  min-height: 25px;
  max-height: 25px;
  padding-left: 1px;
  padding-right: 1px;
  padding-top: 1px;
  padding-bottom: 1px;
}

SARibbonTabBar::tab:selected, SARibbonTabBar::tab:hover {
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

SARibbonTabBar::tab:selected {
  color: #5779af;
  border: 1px solid #c5d2e0;
  background: white;
  border-bottom-color: #FFFFFF;
}

SARibbonTabBar::tab:hover:!selected {
  border: 1px solid #c5d2e0;
  color: #5779af;
}

SARibbonTabBar::tab:!selected {
  margin-top: 0px;
}

/*SARibbonToolButton*/

SARibbonToolButton {
  border: 1px solid transparent;
  color: #333;
  background-color: white;
}

SARibbonToolButton:focus {
  border: 1px solid #b9defa;
  color: #333;
  background-color: #fcfdfe;
}

SARibbonToolButton::pressed {
  color: #000;
  border: 1px solid#269bf4;
  background-color: #9ed2f9;
}

SARibbonToolButton::checked {
  color: #333;
  border: 1px solid #b9defa;
  background-color: #cee8fc;
}

SARibbonToolButton::hover {
  color: #000;
  border: 1px solid #badffa;
  background-color: #cee7fc;
}

/*SARibbonControlButton*/

SARibbonControlButton {
  background-color: transparent;
  border: 1px solid transparent;
  color: #333;
}

SARibbonControlButton#SARibbonGalleryButtonUp, #SARibbonGalleryButtonDown, #SARibbonGalleryButtonMore {
  border: 1px solid #cee8fc;
}

SARibbonControlButton#SARibbonBarHidePannelButton {
  border: 1px solid transparent;
}

SARibbonControlButton::pressed {
  border: 1px solid #269bf4;
  background-color: #9ed2f9;
}

SARibbonControlButton::checked {
  border: 1px solid #b9defa;
  background-color: #cee8fc;
}

SARibbonControlButton::hover {
  border: 1px solid #badffa;
  background-color: #cee7fc;
}

/*SARibbonMenu*/

SARibbonMenu {
  color: #333;
  background-color: #FCFCFC;
  border: 1px solid #c2d0df;
}

SARibbonMenu::item {
  padding: 5px 25px 5px 25px;
  background-color: transparent;
}

SARibbonMenu::item:selected {
  background-color: #cee8fc;
}

SARibbonMenu::item:hover {
  color: #000;
  background-color: #cee7fc;
  border: 1px solid #badffa;
}

SARibbonMenu::icon {
  margin-left: 1px;
}

/*SARibbonPannelOptionButton*/

SARibbonPannelOptionButton {
  background-color: transparent;
  color: #333;
}

SARibbonPannelOptionButton::hover {
  background-color: #cee7fc;
  border: 0px;
}

/*SARibbonPannel*/

SARibbonPannel {
  background-color: white;
  border: 0px;
}

/*SARibbonGallery*/

SARibbonGallery {
  background-color: transparent;
  color: #333;
}

/*SARibbonGalleryGroup*/

SARibbonGalleryGroup {
  show-decoration-selected: 1;
  background-color: transparent;
  color: #333;
  border: 1px solid #c2d0df;
}

SARibbonGalleryGroup::item:selected {
  background-color: #9ed2f9;
  color: black;
}

SARibbonGalleryGroup::item:hover {
  border: 2px solid #badffa;
  background-color: #cee7fc;
}

/*RibbonGalleryViewport*/

RibbonGalleryViewport {
  background-color: white;
}

/*SARibbonLineEdit*/

SARibbonLineEdit {
  border: 1px solid #C0C2C4;
  background: #FFF;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

/*SARibbonComboBox*/

SARibbonComboBox {
  border: 1px solid #c2d0df;
}

SARibbonComboBox:hover {
  border: 1px solid #269bf4;
  color: #000;
}

SARibbonComboBox:editable {
  color: #000;
  background: white;
  selection-background-color: #9BBBF7;
  selection-color: #000;
}

SARibbonComboBox::drop-down {
  subcontrol-origin: padding;
  subcontrol-position: top right;
  width: 15px;
  border-left: none;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

SARibbonComboBox::drop-down:hover {
  border: 1px solid #FDEEB3;
  background-color: #9ed2f9;
}

SARibbonComboBox::down-arrow {
  image: url(:/image/resource/ArrowDown.png);
}

/*SARibbonSeparatorWidget*/

SARibbonSeparatorWidget {
  /*background-color: transparent;*/
  background-color: white;
}

SARibbonCategoryScrollButton {
  border: 0px solid #c5d2e0;
  color: #333;
  background-color: white;
}

SARibbonCategoryScrollButton[arrowType="3"] {
  border-right-width: 1px;
}

SARibbonCategoryScrollButton[arrowType="4"] {
  border-left-width: 1px;
}

SARibbonCategoryScrollButton::hover {
  color: #000000;
  background-color: #cee7fc;
}

SAWindowToolButton { 
  background-color: transparent; 
  border:none;
}

SAWindowToolButton:focus {
  outline: none;
}

SAWindowToolButton#SAMinimizeWindowButton {
  image: url(:/image/resource/Titlebar_Min.png);
}

SAWindowToolButton#SAMaximizeWindowButton:checked {
  image:url(:/image/resource/Titlebar_Normal.png);
}

SAWindowToolButton#SAMaximizeWindowButton {
  image:url(:/image/resource/Titlebar_Max.png);
}

SAWindowToolButton#SAMinimizeWindowButton:hover,#SAMaximizeWindowButton:hover {
  background-color: #e5e5e5;
}

SAWindowToolButton#SAMinimizeWindowButton:pressed,#SAMaximizeWindowButton:pressed {
  background-color: #cacacb;
}

SAWindowToolButton#SACloseWindowButton {
  image: url(:/image/resource/Titlebar_Close.png);
}

SAWindowToolButton#SACloseWindowButton:hover {
  background-color: #e81123;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

SAWindowToolButton#SACloseWindowButton:pressed {
  background-color: #f1707a;
  image: url(:/image/resource/Titlebar_Close_Hover.png);
}

SAWindowToolButton#SARibbonBarHidePannelButton {
  titlebar-shade-icon: url(:/image/resource/Titlebar_Shade.png);
  titlebar-unshade-icon: url(:/image/resource/Titlebar_Unshade.png);
}
