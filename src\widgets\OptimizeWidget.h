#ifndef OPTIMIZEWIDGET_H
#define OPTIMIZEWIDGET_H

#include <QWidget>

QT_BEGIN_NAMESPACE
class QComboBox;
class QSpinBox;
class QDoubleSpinBox;
class QCheckBox;
class QLabel;
QT_END_NAMESPACE

namespace Ui {
class OptimizeWidget;
}

class OptimizeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit OptimizeWidget(QWidget *parent = nullptr);
    ~OptimizeWidget();

    // Getter methods for Dakota parameters
    QString getDakotaMethod() const;
    int getMaxIterations() const;
    int getMaxFunctionEvaluations() const;
    double getConvergenceTolerance() const;
    
    // SOGA parameters
    int getPopulationSize() const;
    double getMutationRate() const;
    double getCrossoverRate() const;
    QString getReplacementType() const;
    
    // Pareto Set parameters
    int getMultiObjectiveWeightSets() const;
    bool isRandomWeightSetsEnabled() const;
    
    // CONMIN parameters
    double getGradientTolerance() const;
    double getMaxStepSize() const;
    bool isSpeculativeGradientEnabled() const;
    
    // Output settings
    bool isVerboseOutputEnabled() const;
    bool isDebugOutputEnabled() const;
    bool isQuietModeEnabled() const;

    // Setter methods
    void setDakotaMethod(const QString &method);
    void setMaxIterations(int iterations);
    void setMaxFunctionEvaluations(int evaluations);
    void setConvergenceTolerance(double tolerance);
    void setPopulationSize(int size);
    void setMutationRate(double rate);
    void setCrossoverRate(double rate);
    void setReplacementType(const QString &type);
    void setMultiObjectiveWeightSets(int sets);
    void setRandomWeightSetsEnabled(bool enabled);
    void setGradientTolerance(double tolerance);
    void setMaxStepSize(double stepSize);
    void setSpeculativeGradientEnabled(bool enabled);
    void setVerboseOutputEnabled(bool enabled);
    void setDebugOutputEnabled(bool enabled);
    void setQuietModeEnabled(bool enabled);

signals:
    void parametersChanged();
    void methodChanged(const QString &method);

private slots:
    void onMethodChanged();
    void onParameterChanged();

private:
    void setupConnections();
    void updateMethodDescription();
    void updateParameterVisibility();

    Ui::OptimizeWidget *ui;
};

#endif // OPTIMIZEWIDGET_H 