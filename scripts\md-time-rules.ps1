# Markdown Time Rules Script
# 在写入markdown文件时自动插入当前时间的规则脚本

# 函数：获取格式化的当前时间
function Get-FormattedDateTime {
    param(
        [string]$Format = "yyyy-MM-dd HH:mm:ss"
    )
    
    $now = Get-Date
    switch ($Format) {
        "chinese" { return $now.ToString("yyyy年MM月dd日 HH:mm:ss") }
        "iso" { return $now.ToString("yyyy-MM-dd HH:mm:ss") }
        "us" { return $now.ToString("MM/dd/yyyy HH:mm:ss") }
        "timestamp" { return $now.ToString("yyyyMMdd_HHmmss") }
        default { return $now.ToString($Format) }
    }
}

# 函数：在markdown文件中插入时间戳
function Add-TimeStampToMarkdown {
    param(
        [string]$FilePath,
        [string]$TimeFormat = "chinese",
        [string]$Position = "end"  # "start", "end", "header"
    )
    
    $timestamp = Get-FormattedDateTime -Format $TimeFormat
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "文件不存在: $FilePath" -ForegroundColor Red
        return
    }
    
    $content = Get-Content $FilePath -Raw
    
    switch ($Position) {
        "start" {
            $timeEntry = "**更新时间：** $timestamp`n`n"
            $newContent = $timeEntry + $content
        }
        "end" {
            $timeEntry = "`n`n**最后更新：** $timestamp"
            $newContent = $content + $timeEntry
        }
        "header" {
            # 在第一个标题后插入时间
            $lines = $content -split "`n"
            $headerFound = $false
            $newLines = @()
            
            foreach ($line in $lines) {
                $newLines += $line
                if (-not $headerFound -and $line -match "^#\s+") {
                    $newLines += ""
                    $newLines += "**文档更新时间：** $timestamp"
                    $newLines += ""
                    $headerFound = $true
                }
            }
            $newContent = $newLines -join "`n"
        }
        default {
            $timeEntry = "`n`n**最后更新：** $timestamp"
            $newContent = $content + $timeEntry
        }
    }
    
    Set-Content -Path $FilePath -Value $newContent -Encoding UTF8
    Write-Host "已在 $FilePath 中插入时间戳: $timestamp" -ForegroundColor Green
}

# 函数：批量处理markdown文件
function Update-MarkdownFilesWithTimestamp {
    param(
        [string]$Directory = ".",
        [string]$Pattern = "*.md",
        [string]$TimeFormat = "chinese",
        [string]$Position = "end"
    )
    
    $mdFiles = Get-ChildItem -Path $Directory -Filter $Pattern -Recurse
    
    foreach ($file in $mdFiles) {
        Write-Host "处理文件: $($file.FullName)" -ForegroundColor Yellow
        Add-TimeStampToMarkdown -FilePath $file.FullName -TimeFormat $TimeFormat -Position $Position
    }
}

# 函数：创建带时间戳的新markdown文件
function New-MarkdownWithTimestamp {
    param(
        [string]$FilePath,
        [string]$Title = "新文档",
        [string]$Content = "",
        [string]$TimeFormat = "chinese"
    )
    
    $timestamp = Get-FormattedDateTime -Format $TimeFormat
    
    $template = @"
# $Title

**创建时间：** $timestamp
**最后更新：** $timestamp

---

$Content

"@
    
    Set-Content -Path $FilePath -Value $template -Encoding UTF8
    Write-Host "已创建新的markdown文件: $FilePath" -ForegroundColor Green
}

# 函数：更新现有markdown文件的时间戳
function Update-MarkdownTimestamp {
    param(
        [string]$FilePath,
        [string]$TimeFormat = "chinese"
    )
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "文件不存在: $FilePath" -ForegroundColor Red
        return
    }
    
    $timestamp = Get-FormattedDateTime -Format $TimeFormat
    $content = Get-Content $FilePath -Raw
    
    # 查找并替换现有的时间戳
    $patterns = @(
        '\*\*最后更新：\*\*\s+[\d年月日\s\-:\/]+',
        '\*\*更新时间：\*\*\s+[\d年月日\s\-:\/]+',
        '\*\*文档更新时间：\*\*\s+[\d年月日\s\-:\/]+'
    )
    
    $updated = $false
    foreach ($pattern in $patterns) {
        if ($content -match $pattern) {
            $content = $content -replace $pattern, "**最后更新：** $timestamp"
            $updated = $true
            break
        }
    }
    
    # 如果没找到现有时间戳，在文件末尾添加
    if (-not $updated) {
        $content += "`n`n**最后更新：** $timestamp"
    }
    
    Set-Content -Path $FilePath -Value $content -Encoding UTF8
    Write-Host "已更新 $FilePath 的时间戳: $timestamp" -ForegroundColor Green
}

# 主要规则函数：智能处理markdown文件时间戳
function Invoke-MarkdownTimeRules {
    param(
        [string]$FilePath,
        [string]$Action = "update",  # "create", "update", "append"
        [string]$TimeFormat = "chinese",
        [string]$Position = "end"
    )
    
    switch ($Action) {
        "create" {
            if (Test-Path $FilePath) {
                Write-Host "文件已存在，将更新时间戳" -ForegroundColor Yellow
                Update-MarkdownTimestamp -FilePath $FilePath -TimeFormat $TimeFormat
            } else {
                $title = [System.IO.Path]::GetFileNameWithoutExtension($FilePath)
                New-MarkdownWithTimestamp -FilePath $FilePath -Title $title -TimeFormat $TimeFormat
            }
        }
        "update" {
            Update-MarkdownTimestamp -FilePath $FilePath -TimeFormat $TimeFormat
        }
        "append" {
            Add-TimeStampToMarkdown -FilePath $FilePath -TimeFormat $TimeFormat -Position $Position
        }
        default {
            Write-Host "未知操作: $Action" -ForegroundColor Red
        }
    }
}

# 使用示例和帮助信息
function Show-MarkdownTimeRulesHelp {
    Write-Host @"

=== Markdown Time Rules 使用说明 ===

1. 更新现有文件的时间戳：
   Invoke-MarkdownTimeRules -FilePath "example.md" -Action "update"

2. 创建新文件并添加时间戳：
   Invoke-MarkdownTimeRules -FilePath "new.md" -Action "create"

3. 在文件末尾追加时间戳：
   Invoke-MarkdownTimeRules -FilePath "example.md" -Action "append"

4. 批量更新目录中的所有markdown文件：
   Update-MarkdownFilesWithTimestamp -Directory "." -TimeFormat "chinese"

5. 时间格式选项：
   - "chinese": 2025年06月24日 09:10:00
   - "iso": 2025-06-24 09:10:00
   - "us": 06/24/2025 09:10:00
   - "timestamp": 20250624_091000

6. 位置选项：
   - "start": 文件开头
   - "end": 文件末尾
   - "header": 第一个标题后

=== 快捷命令 ===
. .\scripts\md-time-rules.ps1
Invoke-MarkdownTimeRules -FilePath "program.md" -Action "update"

"@ -ForegroundColor Cyan
}

# 导出函数
Export-ModuleMember -Function Get-FormattedDateTime, Add-TimeStampToMarkdown, Update-MarkdownFilesWithTimestamp, New-MarkdownWithTimestamp, Update-MarkdownTimestamp, Invoke-MarkdownTimeRules, Show-MarkdownTimeRulesHelp

Write-Host "Markdown Time Rules 脚本已加载。使用 Show-MarkdownTimeRulesHelp 查看帮助信息。" -ForegroundColor Green 