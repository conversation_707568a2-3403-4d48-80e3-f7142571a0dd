/****************************************************************************
** Resource object code
**
** Created by: The Resource Compiler for Qt version 5.14.2
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

static const unsigned char qt_resource_data[] = {
  // D:/Qt/source/OptimizeApplication/resources/icons/save.png
  0x0,0x0,0x0,0xe8,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x0,0x9a,0x49,0x44,0x41,0x54,0x58,0x85,0xed,
  0x93,0x4b,0xa,0x80,0x30,0xc,0x44,0xbb,0x10,0xbd,0x81,0x37,0x10,0xc1,0x9b,0x78,
  0x5a,0xed,0xc6,0xb3,0x54,0xd7,0x16,0xbc,0x85,0x4e,0xa0,0x8b,0xb8,0x28,0x7e,0xa8,
  0x29,0x48,0x1e,0xcc,0x36,0x6f,0x20,0x89,0x31,0x8a,0xa2,0xc8,0x50,0x20,0x3d,0x52,
  0xe7,0x90,0x97,0xc8,0x88,0xec,0x88,0xcb,0x21,0xb7,0x41,0x4e,0x99,0xa5,0xe5,0x3,
  0x93,0x6f,0x48,0xa7,0x72,0x95,0xdf,0x81,0x7e,0x95,0x7e,0xb6,0x78,0x28,0xe7,0xd7,
  0xee,0x91,0xf6,0x8d,0x9c,0x70,0x61,0xc8,0x18,0x6,0x8b,0xca,0x89,0x99,0xd,0xb3,
  0x17,0x25,0x92,0xcb,0x89,0x6,0x59,0xd9,0xd0,0x9,0xa9,0x22,0xf2,0xcf,0xe,0xee,
  0xaa,0x84,0xc8,0xb5,0xc7,0x4a,0x88,0xbe,0x1a,0xed,0xd3,0x9b,0xf3,0x4d,0x24,0xdf,
  0xf9,0xd3,0x12,0xa2,0xf2,0x58,0x9,0x51,0x39,0x2f,0xb1,0x84,0x88,0xcb,0x15,0x45,
  0xf9,0x2f,0x7,0x48,0x3e,0x55,0x56,0xf8,0xf,0x0,0xcb,0x0,0x0,0x0,0x0,0x49,
  0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // D:/Qt/source/OptimizeApplication/resources/icons/open.png
  0x0,0x0,0x2,0x8a,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x2,0x3c,0x49,0x44,0x41,0x54,0x58,0x85,0xdd,
  0x96,0xb1,0x4b,0x1c,0x41,0x14,0xc6,0x9f,0x44,0x41,0x4c,0xd4,0x2b,0x44,0x8,0xc4,
  0xa8,0x47,0xc,0x6a,0x11,0xac,0x93,0x2e,0x5d,0x20,0x49,0x65,0x1b,0x51,0x50,0xf,
  0x2c,0x2c,0x2c,0x44,0xab,0x13,0x4c,0x29,0xa8,0x55,0x44,0xc5,0xca,0x7f,0xc0,0x42,
  0x84,0x40,0x48,0x8,0xa4,0x48,0x15,0x2b,0x83,0xa7,0xe2,0xa9,0x85,0x85,0x88,0xa0,
  0x45,0x4e,0x11,0xce,0x6f,0xb8,0x6f,0xb3,0xef,0x26,0x7b,0xea,0xce,0xde,0x9d,0x98,
  0x7,0xbf,0x62,0xdf,0xcc,0xf7,0xbd,0x37,0x73,0xbb,0x73,0x23,0x72,0x8f,0xa3,0x12,
  0x7c,0x24,0x95,0x77,0xd1,0x40,0x2,0x64,0x49,0xa2,0xdc,0xc5,0xab,0x40,0x5a,0x35,
  0x90,0x66,0xae,0x6c,0x31,0xa0,0x8a,0x7b,0xf4,0x97,0xab,0xb8,0x59,0xe9,0xe,0x8b,
  0x66,0x48,0x96,0xb9,0xb2,0xec,0x42,0x9f,0xf8,0xab,0xfe,0x4,0xe6,0xd4,0x73,0x6f,
  0xa9,0x8b,0x3f,0x0,0x9b,0x2c,0x76,0x1,0x5a,0xc0,0x53,0x70,0xce,0xdc,0xb6,0x94,
  0xf8,0x8b,0xe8,0x11,0x7f,0xb5,0xf3,0x2a,0xbf,0xa0,0xf2,0x1f,0x4a,0x51,0xd8,0xac,
  0x3c,0x2e,0xf9,0xab,0x6f,0x55,0xe3,0xad,0xcc,0x65,0x39,0x27,0x4e,0x4d,0xe8,0x68,
  0x2,0xaf,0x25,0xf7,0x5d,0x4f,0x81,0x15,0xf0,0x5b,0xfc,0x2d,0xf6,0x58,0xa,0xd0,
  0x2e,0x59,0x73,0xce,0xa9,0x5d,0xa1,0x57,0x82,0xde,0x4d,0x41,0x85,0xdf,0x82,0x13,
  0xf9,0xf7,0xd3,0xa,0xe2,0xf,0x57,0x68,0x47,0x9c,0x63,0xb7,0xf1,0x38,0x61,0xcd,
  0xbf,0xb1,0x58,0x60,0xa2,0xd9,0xd6,0x14,0x58,0x5,0xd3,0x60,0x8,0x74,0x4,0xad,
  0x80,0xd1,0xc1,0x39,0xd3,0xd4,0xa4,0xc4,0xff,0x69,0x6c,0x16,0xb5,0xb0,0xd,0x1c,
  0xaa,0xc1,0x5d,0xf0,0x42,0x8a,0xf3,0x5d,0x57,0xd1,0x6b,0x57,0xf9,0x1f,0xb2,0x66,
  0x5e,0x3c,0x7,0x7,0x6a,0xd2,0x37,0xf0,0xa8,0x8,0xd,0xd4,0x80,0xcf,0x56,0xf1,
  0xce,0x42,0x93,0x5b,0xc4,0x3f,0xe5,0xc,0xdf,0x41,0x6d,0x84,0xe2,0xf,0xc1,0x17,
  0xe5,0xb7,0x7,0x9e,0xdd,0x24,0x32,0x7,0xcb,0x96,0x12,0xfd,0x0,0x75,0xe,0xc5,
  0xcd,0xee,0x7d,0x55,0x3e,0x69,0x9,0x7e,0x79,0x3,0xe3,0x89,0xe4,0x5e,0x20,0x4f,
  0xfc,0x13,0xc4,0x42,0x14,0x8f,0x51,0xe3,0xe9,0x53,0xf4,0xc,0x15,0x8f,0xc1,0x86,
  0x32,0x99,0xd,0xa1,0x9d,0x55,0xba,0xd,0x7a,0x39,0x45,0x57,0x11,0x1a,0xe8,0x72,
  0x2d,0x6e,0xe2,0xbd,0x32,0x1a,0xc,0xa1,0xd3,0xb7,0xa5,0x77,0x51,0x1a,0x18,0x57,
  0x46,0x2f,0x43,0xe8,0x5e,0x29,0xdd,0x58,0x94,0x6,0x96,0x95,0x91,0xfd,0x12,0x36,
  0x80,0x9,0xd2,0x60,0x8d,0xc5,0x94,0x6e,0x39,0x4a,0x3,0xeb,0x34,0xd9,0x57,0x39,
  0x73,0x2e,0x24,0xc1,0xa9,0x2a,0x72,0xca,0x9c,0x3e,0x33,0xf6,0x39,0xb6,0xee,0x5a,
  0xdc,0x5c,0x2c,0xbc,0xeb,0xd6,0x1a,0xa8,0x6,0x23,0xe0,0x48,0x15,0xb6,0x39,0xe2,
  0x9c,0x6a,0x6a,0xbc,0x6b,0x9b,0xd3,0x25,0xa5,0x5d,0x19,0xff,0x92,0xfc,0x63,0xda,
  0x70,0xc,0x46,0xc9,0xb1,0x35,0x76,0x40,0x8d,0xf7,0xdc,0xee,0xd2,0x40,0xb7,0x4,
  0xaf,0xf2,0xc,0x4c,0x82,0x7a,0x35,0xb7,0x9e,0xb9,0xb3,0x2,0x9a,0x6e,0x97,0x6,
  0x92,0x96,0x89,0xd9,0xca,0x19,0xd0,0x78,0x8d,0xa6,0x91,0x73,0x32,0x92,0xaf,0x4d,
  0xba,0x34,0x30,0x4c,0xf1,0xa5,0xe4,0x6e,0x3b,0xcd,0x21,0xb4,0xcd,0xd4,0x5c,0xd2,
  0x63,0xd8,0xa5,0x81,0xa,0xf0,0x46,0x2,0xfe,0xb7,0x43,0x44,0x1b,0x3d,0x2a,0x22,
  0x78,0xfc,0xe7,0x71,0x5,0x8,0xf5,0xee,0x6d,0x45,0x13,0xe5,0xc4,0x0,0x0,0x0,
  0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // D:/Qt/source/OptimizeApplication/resources/icons/cut.png
  0x0,0x0,0x2,0x6c,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x2,0x1e,0x49,0x44,0x41,0x54,0x58,0x85,0xcd,
  0x97,0x37,0x4b,0x4,0x51,0x14,0x85,0xdf,0x62,0x16,0xc1,0x84,0x19,0xcc,0x62,0xa9,
  0xfb,0x1b,0x6c,0x45,0x4,0x1b,0x3,0xa2,0x20,0xd8,0x98,0x6a,0xb,0x53,0xa5,0xd6,
  0xae,0xdd,0xa2,0xc8,0x9a,0xd6,0xca,0xd2,0x1f,0x21,0x8a,0xa1,0x12,0x41,0x5,0xd7,
  0xca,0x46,0xb0,0x11,0x4c,0xe7,0xb2,0x6f,0xf0,0xec,0xec,0x4,0x77,0x7c,0xb,0x5e,
  0xf8,0x9a,0xb9,0x77,0xee,0x39,0x93,0xde,0xbb,0xa3,0xd4,0x3f,0x8e,0x16,0x30,0x7,
  0x22,0xe0,0x28,0x20,0x1b,0xba,0x47,0x4b,0x26,0xc2,0xd,0xe0,0x0,0x7c,0x82,0x2f,
  0x43,0x48,0xaf,0x3d,0x50,0xef,0x27,0x1e,0x6,0x4f,0x6,0x85,0xed,0x24,0x40,0xb7,
  0xd7,0x95,0xb3,0xf8,0x5,0x18,0x2,0x35,0x7e,0xae,0x3d,0xa2,0x5a,0xf7,0xb8,0xb4,
  0x99,0xa8,0x73,0x2a,0x3e,0xa0,0xa2,0x5d,0x90,0xff,0x7,0x61,0x7b,0x48,0xaf,0x3d,
  0x5b,0xff,0x94,0x90,0x97,0xc4,0x7a,0xe6,0x17,0x86,0xc5,0xd9,0xc4,0x95,0xd6,0xf8,
  0x0,0xcd,0x9c,0x9c,0x25,0x77,0x83,0x86,0x4,0xfb,0xc1,0x16,0x68,0xa3,0x63,0x23,
  0xa4,0x33,0xcd,0xc5,0x9b,0x94,0xa8,0x36,0x64,0xe0,0x45,0xf7,0xdb,0xa6,0x63,0x35,
  0xa4,0x13,0xe1,0xe2,0x43,0x4a,0x84,0x7e,0xd1,0xbc,0x13,0x2c,0xa8,0x9f,0xef,0x7d,
  0x45,0xa5,0xbf,0xdd,0x51,0xf0,0xa,0x6,0xe8,0x58,0x88,0x74,0xe,0xb9,0x38,0x4e,
  0x9,0xaf,0x28,0x51,0xc9,0x2b,0x72,0xfb,0xcc,0x8e,0x41,0xa5,0x4f,0xf,0xab,0x36,
  0x9e,0xa9,0x81,0x22,0x70,0xee,0x21,0x6e,0x71,0xe7,0x63,0x22,0xb0,0x81,0x8,0xd5,
  0xdc,0x80,0x5e,0x90,0x7,0x72,0x41,0xf,0x38,0x53,0xa9,0x77,0xc2,0xa8,0x1,0x79,
  0x31,0xdf,0x75,0xfe,0x1e,0x94,0x3b,0xd4,0x14,0xda,0x4c,0x74,0x99,0x34,0x30,0x49,
  0xf9,0x31,0x97,0x1a,0x89,0x1e,0xaa,0x5b,0x36,0x69,0x60,0x9d,0xf2,0x8d,0x1e,0x6,
  0xe4,0x71,0x58,0xb,0x5a,0xda,0x6a,0xf7,0x17,0x3,0xab,0x94,0x6f,0xf5,0x30,0x50,
  0x40,0x75,0x3b,0x26,0xd,0x8c,0x52,0x7e,0xca,0xc3,0x40,0x1f,0xd5,0xcd,0x9b,0x34,
  0x50,0x6,0xde,0x74,0xfe,0x59,0xd9,0xd6,0x71,0x1d,0x15,0xe0,0x96,0xfa,0xb4,0x9b,
  0x34,0x20,0xb1,0x44,0x35,0x62,0x42,0xd6,0x72,0x59,0x11,0x3b,0xc0,0x38,0x78,0xa0,
  0x7c,0xd4,0xa3,0x4f,0x60,0x3,0x39,0xe0,0x84,0xea,0xdc,0x38,0x5,0xc5,0xd9,0x30,
  0x60,0x99,0x58,0x54,0x3f,0x8f,0x83,0x91,0x75,0x42,0x66,0xc0,0x42,0x9f,0x1e,0x8e,
  0x6,0x32,0xdd,0x8c,0x4a,0x55,0x72,0x6b,0x5d,0xd3,0x4c,0x80,0xaa,0x5f,0x9c,0xe7,
  0xba,0x19,0x65,0x63,0x3b,0x76,0x8a,0x5a,0xd2,0x49,0xd9,0x8e,0x79,0x20,0x19,0xca,
  0xa2,0x1,0xd7,0x81,0x84,0x47,0x32,0x19,0x20,0xb3,0x31,0x92,0xc9,0x22,0x75,0xad,
  0x35,0x64,0x24,0x6b,0xb2,0x17,0xec,0x93,0xbb,0x7d,0xc3,0x26,0x44,0x9c,0x87,0xde,
  0x98,0x53,0x91,0xfc,0x34,0x24,0xa8,0x48,0x6,0xc8,0x61,0x95,0x7c,0x6e,0x41,0x43,
  0xce,0x95,0xdb,0x6e,0x5d,0xb9,0xf0,0xa8,0x5c,0xc6,0x72,0x89,0xb0,0xcd,0x84,0x69,
  0x44,0xdc,0xf5,0xc7,0xc4,0xa,0xb9,0x13,0x32,0xc3,0x7f,0x18,0x14,0x96,0x5e,0x31,
  0x95,0xe1,0xdd,0x6c,0x6,0x33,0x2a,0xb9,0xb8,0x4,0xf9,0x31,0x8d,0xeb,0x73,0xa5,
  0x47,0xda,0xb,0xf7,0x6f,0xe2,0x1b,0x63,0x8a,0x59,0x5b,0x13,0xdb,0xca,0xf2,0x0,
  0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // D:/Qt/source/OptimizeApplication/resources/icons/copy.png
  0x0,0x0,0x1,0x9b,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x1,0x4d,0x49,0x44,0x41,0x54,0x58,0x85,0xed,
  0x97,0x41,0x4a,0x3,0x41,0x14,0x44,0x3f,0x66,0xeb,0x88,0x2b,0x83,0xde,0xc0,0x48,
  0xe,0x92,0x6d,0x70,0x20,0xdb,0xb8,0x8,0x8,0xe6,0x8,0x9a,0x8b,0xc8,0x2c,0xd,
  0x71,0xa3,0x27,0x49,0xb6,0xba,0x10,0xc,0x78,0x9,0x5d,0xa8,0x55,0x90,0x86,0x5a,
  0x24,0xc3,0xfc,0xee,0x38,0x13,0x21,0x5,0x6f,0xd5,0xfd,0x7f,0x55,0x98,0xe6,0xa7,
  0xdb,0x6c,0xc7,0xd4,0x5,0x13,0x30,0x5,0x8f,0xe,0xa,0x30,0x6,0x47,0x29,0xe6,
  0xd7,0xe0,0xb,0xfc,0x24,0xb0,0x4,0x17,0x31,0xe6,0xdd,0x2d,0x98,0x7,0x16,0xe0,
  0xc0,0x1b,0x60,0x22,0xd,0xae,0x40,0xcb,0x59,0x9f,0x81,0x67,0xe9,0x71,0xee,0xd,
  0x30,0x95,0x62,0xaf,0x79,0xd0,0x40,0x7a,0xf4,0xbd,0xc5,0x33,0x29,0x8e,0x55,0xbf,
  0xe9,0x0,0x6d,0xf0,0xba,0xa2,0xdd,0x44,0x80,0x24,0xfd,0xbb,0x0,0x67,0xa0,0x7,
  0x72,0x7,0x3c,0x17,0x1d,0xdb,0x70,0xc8,0xab,0x6,0x38,0x4,0xf7,0x56,0x7d,0x26,
  0xac,0x63,0x6e,0x6b,0x86,0x55,0xd5,0x0,0xa9,0xe6,0x81,0xf,0x70,0xec,0xd,0x70,
  0xa,0xbe,0x57,0x7b,0xde,0xc0,0xd0,0x7c,0x9f,0x80,0x73,0xe2,0x49,0x7c,0x6e,0xbc,
  0x1,0x7a,0xb2,0x67,0x58,0xb2,0xaf,0x4c,0x99,0xf4,0x28,0xbc,0x1,0x72,0xd9,0x93,
  0x47,0x6,0x30,0xe9,0x31,0xdb,0xa9,0x0,0x85,0x2c,0x64,0x4d,0x4,0x18,0xcb,0x2,
  0xf,0xa,0xf,0xcc,0x25,0x38,0xa9,0x2b,0x0,0x6f,0x32,0x4b,0x59,0xc,0xbc,0xd4,
  0x15,0x80,0xe2,0x70,0x98,0x37,0x19,0x80,0xe2,0x4d,0x86,0xe3,0x92,0x63,0xb3,0xd6,
  0x4f,0x50,0x45,0xfb,0x0,0xfb,0x0,0x7a,0xe7,0x1b,0x44,0x9a,0xb7,0xa4,0xc7,0x83,
  0xb7,0xb8,0x23,0xc5,0xbc,0x82,0x6f,0x9a,0x98,0x65,0xe6,0x23,0xe9,0x71,0xe7,0xd,
  0xc0,0x6,0xb,0x69,0x90,0xc2,0xa7,0x45,0xbe,0xa0,0x58,0xf4,0xbe,0x5,0xf3,0x51,
  0x8c,0x79,0x10,0xc7,0x36,0x2f,0x13,0xfc,0x3,0xf3,0x3c,0x60,0xf9,0xf8,0xb9,0xb5,
  0xc8,0x5f,0xfe,0x67,0xfa,0x5,0xb3,0xe8,0x17,0x9e,0x2f,0x87,0x43,0x96,0x0,0x0,
  0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // D:/Qt/source/OptimizeApplication/resources/icons/new.png
  0x0,0x0,0x1,0x6e,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x1,0x20,0x49,0x44,0x41,0x54,0x58,0x85,0xd5,
  0x97,0xb1,0xa,0xc2,0x30,0x10,0x86,0x4f,0xd0,0x7,0x71,0x52,0x5f,0xa5,0x8b,0x45,
  0xf0,0x5,0x74,0xf2,0x15,0x44,0x97,0xbe,0x80,0x82,0xa3,0x83,0xa3,0xb4,0x74,0x51,
  0xf4,0x29,0x1c,0xc5,0xd9,0x55,0x9d,0x1d,0xab,0xf5,0x2a,0x17,0xa8,0xa0,0xf4,0x92,
  0x4b,0x5b,0xfb,0xc3,0xb7,0xb4,0xc9,0xdd,0x7,0x49,0x4a,0xa,0xf0,0xa7,0xa9,0x21,
  0x3d,0x64,0x8e,0x4,0xc,0xf6,0xc8,0x99,0xc6,0x8b,0xd3,0x40,0x76,0x48,0x6c,0xc8,
  0x50,0x2a,0x30,0x15,0x34,0x4f,0x78,0x48,0x25,0x4e,0x54,0xe8,0x82,0xb4,0x98,0x73,
  0x7c,0x9b,0x12,0x11,0x15,0x9,0x35,0xe6,0xa4,0x5,0x1e,0x52,0x9,0x55,0xc8,0x37,
  0x14,0x18,0xa4,0x24,0x9e,0xc8,0xa8,0x68,0x1,0x90,0x4a,0xd8,0x10,0x10,0x49,0x48,
  0x5,0xd2,0xdf,0x87,0x3,0x35,0x57,0x12,0xb,0x53,0x81,0x3a,0xe2,0x20,0x7d,0xc2,
  0xa1,0x67,0x2a,0x6b,0xf8,0x3c,0x5,0xbf,0x78,0x9a,0xa,0x78,0x5f,0x8a,0x79,0xa9,
  0xf7,0x2e,0x72,0x67,0x4a,0xe4,0x22,0x90,0x95,0x6f,0x7b,0x44,0x4b,0x20,0x6b,0x9,
  0x72,0x17,0x90,0xa6,0xfa,0x2,0x3a,0x9b,0x2c,0xa6,0xb1,0xae,0x4d,0x81,0xa5,0x46,
  0x73,0xc5,0xd2,0xa6,0x40,0x13,0x59,0x1,0xef,0x72,0x12,0xd0,0xd8,0xa6,0x4d,0x1,
  0x69,0xaa,0x2f,0x50,0xfa,0x12,0x94,0xbe,0x9,0x4b,0x3f,0x86,0xd2,0x54,0x4b,0xc0,
  0xe4,0x52,0x9a,0x95,0x90,0x6a,0x46,0x9c,0xc1,0xea,0x5a,0x7e,0x45,0xda,0x16,0x9a,
  0x77,0x90,0x1b,0xd5,0x3c,0x72,0x26,0x4c,0x40,0x7f,0xc7,0x73,0x19,0x73,0x4,0x92,
  0x5f,0xb3,0x6d,0xe,0xcd,0x37,0xa0,0x77,0x87,0x78,0x1f,0xa3,0x19,0xf0,0x3f,0x3e,
  0xbf,0x48,0x6a,0x74,0x75,0x1a,0x17,0x9a,0x17,0x33,0x74,0x58,0x98,0x2f,0xa4,0x2f,
  0xa,0x0,0x0,0x0,0x0,0x49,0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
    // D:/Qt/source/OptimizeApplication/resources/icons/paste.png
  0x0,0x0,0x2,0xb8,
  0x89,
  0x50,0x4e,0x47,0xd,0xa,0x1a,0xa,0x0,0x0,0x0,0xd,0x49,0x48,0x44,0x52,0x0,
  0x0,0x0,0x20,0x0,0x0,0x0,0x20,0x8,0x6,0x0,0x0,0x0,0x73,0x7a,0x7a,0xf4,
  0x0,0x0,0x0,0x9,0x70,0x48,0x59,0x73,0x0,0x0,0xb,0x13,0x0,0x0,0xb,0x13,
  0x1,0x0,0x9a,0x9c,0x18,0x0,0x0,0x2,0x6a,0x49,0x44,0x41,0x54,0x58,0x85,0xcd,
  0x97,0xcd,0x4b,0x94,0x51,0x14,0xc6,0xf,0xd9,0xb8,0x49,0x29,0x33,0x12,0x32,0xb1,
  0xa2,0xdc,0x7,0x6d,0x5a,0x18,0x44,0xb,0x13,0xdb,0xd5,0xd2,0x8f,0x44,0x48,0xb2,
  0x4d,0x6a,0x58,0xe9,0x20,0x22,0x5,0x25,0x42,0xb4,0x4b,0x97,0x62,0xb,0x5d,0x24,
  0xee,0xc2,0x85,0x7f,0x80,0x18,0x2d,0x67,0xd3,0x2e,0xa2,0xd2,0xc2,0xe8,0xc3,0x42,
  0xfc,0xe8,0x3c,0xdd,0xf7,0x72,0x9f,0x79,0x9d,0x79,0x7d,0x67,0xba,0xef,0xe0,0x81,
  0x1f,0xc3,0xdc,0x7b,0xee,0x39,0xcf,0xdc,0x8f,0x73,0xef,0x88,0xec,0x33,0xab,0x57,
  0x6,0x94,0x69,0x65,0x96,0xc0,0xf7,0xb4,0x72,0x32,0xc9,0xe4,0x2d,0xca,0x37,0x65,
  0x27,0x82,0x9f,0xca,0xd5,0x24,0x92,0x57,0x29,0x2b,0x7b,0x24,0xb7,0xac,0x29,0x47,
  0x7c,0xb,0x68,0xa5,0x4,0x4f,0x94,0xf2,0x50,0x7f,0x4a,0x79,0x4a,0x3e,0x1d,0xbe,
  0x5,0xc,0x50,0xf0,0xf3,0x79,0x7c,0x6a,0xc9,0x67,0xd2,0xb7,0x80,0x76,0xa,0x7e,
  0x23,0xc2,0xef,0x73,0xe0,0xf3,0xc6,0xb7,0x80,0xb,0x24,0x60,0x38,0xc2,0x6f,0x2e,
  0xf0,0xd9,0x56,0x4e,0xf8,0x14,0x70,0x48,0xd9,0xa,0x82,0xcf,0x45,0xf8,0x75,0x8a,
  0x13,0xda,0xe9,0x53,0x0,0x6c,0x39,0x8,0xfc,0x5d,0x76,0x6f,0x42,0x6b,0x67,0x48,
  0xc0,0x98,0x6f,0x1,0xa3,0x14,0xfc,0x4a,0x1e,0x9f,0x5e,0xf2,0xe9,0xf3,0x2d,0xe0,
  0x22,0x5,0x9f,0xc8,0xd1,0xdf,0x25,0x6e,0x99,0x7e,0x29,0x75,0xbe,0x5,0x1c,0x50,
  0xde,0x89,0xab,0x78,0x5c,0x6c,0x9a,0x29,0x39,0x3e,0xdb,0x7c,0x27,0xb7,0xd6,0x2f,
  0x6e,0x16,0xee,0x52,0xfb,0x43,0x6a,0x9f,0x4f,0x2a,0x39,0xec,0xa8,0x98,0xe9,0x45,
  0xa2,0x8f,0x4a,0x65,0xd0,0x8e,0xd9,0xf8,0x20,0x6e,0x6,0x1a,0x93,0x14,0xc1,0x25,
  0xf7,0x31,0xb5,0xb7,0x50,0xfb,0xaa,0x72,0x36,0x29,0x1,0x87,0xc5,0x55,0xbc,0xdf,
  0x4a,0x3,0xf5,0x4d,0x90,0x88,0x8c,0x52,0x9d,0x94,0x88,0x5b,0x94,0x8,0xf5,0xc1,
  0xd6,0x5,0x5c,0x4a,0xaf,0x43,0x22,0x4e,0x87,0xc6,0xe2,0x4d,0x71,0x5f,0x4c,0x9d,
  0x40,0xb1,0xaa,0x2d,0x46,0x0,0x4e,0xc4,0x2,0x25,0x1a,0xa7,0xbe,0x4a,0x71,0x45,
  0xb,0x7c,0x52,0x2e,0x5,0x7d,0x38,0x1d,0xeb,0xd4,0x67,0xcb,0xf6,0x5b,0x31,0x17,
  0x18,0x6e,0xd1,0xaa,0xb8,0x22,0x50,0xeb,0x57,0x29,0xd0,0xed,0x90,0x8,0x9e,0x9,
  0x6c,0xcc,0x79,0x71,0x47,0x35,0xa,0x3c,0x7a,0x62,0x3f,0x6a,0x9a,0x94,0x8d,0x60,
  0xe0,0xa6,0x64,0xdf,0x94,0x58,0x8e,0x17,0x39,0x12,0x40,0x4,0x8e,0xf0,0x29,0x31,
  0x4b,0xf0,0x4a,0xdc,0x9e,0xe2,0x97,0x55,0xec,0xa5,0xb9,0x29,0x66,0x1a,0xad,0x88,
  0xee,0x50,0xff,0x35,0xc9,0x3e,0xa2,0x5d,0x79,0xe2,0x60,0x46,0xc7,0x48,0xc4,0x50,
  0x5c,0x1,0xb0,0x7e,0x12,0x1,0x70,0x54,0x53,0xd4,0x8f,0x3a,0x81,0x62,0xd5,0xbc,
  0x47,0x9c,0x72,0x8a,0x31,0x55,0x88,0x0,0x18,0x1e,0x2d,0x76,0x39,0xc0,0x92,0x72,
  0xae,0xd0,0x20,0x34,0x7e,0xa6,0x88,0xb1,0xff,0x7e,0xe1,0x57,0xa,0x82,0xaa,0x39,
  0x22,0xe6,0x3d,0x51,0x12,0x1,0x30,0xfc,0x37,0x58,0x94,0xec,0x4d,0xf5,0x5e,0xcc,
  0x29,0xa9,0x28,0x85,0x0,0x18,0xea,0xc4,0x1d,0xe5,0x8b,0xec,0x3e,0x62,0xcf,0xc5,
  0x5c,0xed,0x65,0x39,0xc6,0xa5,0xc8,0x77,0xfa,0x7f,0x4,0x58,0x43,0x51,0x79,0xa6,
  0xfc,0x9,0x9,0xb1,0xf7,0xc5,0x4b,0xe5,0x9e,0x98,0xe3,0x8c,0xfd,0x92,0xa6,0xfe,
  0x7,0x3e,0x4,0x58,0x3b,0xae,0xc,0x8a,0x59,0x8a,0x38,0x7f,0x6e,0x7e,0x88,0x79,
  0xde,0x79,0xb7,0x83,0x62,0x9e,0x73,0x28,0xdd,0x99,0x3c,0xc9,0xb1,0x4c,0xd7,0x93,
  0x48,0x9e,0xcb,0x8e,0x29,0x97,0x95,0x1e,0xe5,0x91,0x98,0x8d,0x5a,0x53,0xaa,0xe4,
  0xb1,0xec,0x2f,0xd8,0xfe,0xee,0xd9,0x88,0x90,0xa0,0x9e,0x0,0x0,0x0,0x0,0x49,
  0x45,0x4e,0x44,0xae,0x42,0x60,0x82,
  
};

static const unsigned char qt_resource_name[] = {
  // icons
  0x0,0x5,
  0x0,0x6f,0xa6,0x53,
  0x0,0x69,
  0x0,0x63,0x0,0x6f,0x0,0x6e,0x0,0x73,
    // save.png
  0x0,0x8,
  0x8,0xc8,0x58,0x67,
  0x0,0x73,
  0x0,0x61,0x0,0x76,0x0,0x65,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // open.png
  0x0,0x8,
  0x6,0xc1,0x59,0x87,
  0x0,0x6f,
  0x0,0x70,0x0,0x65,0x0,0x6e,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // cut.png
  0x0,0x7,
  0xa,0xc7,0x57,0x87,
  0x0,0x63,
  0x0,0x75,0x0,0x74,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // copy.png
  0x0,0x8,
  0x6,0x7c,0x5a,0x7,
  0x0,0x63,
  0x0,0x6f,0x0,0x70,0x0,0x79,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // new.png
  0x0,0x7,
  0x4,0xca,0x57,0xa7,
  0x0,0x6e,
  0x0,0x65,0x0,0x77,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
    // paste.png
  0x0,0x9,
  0xa,0xa8,0xba,0x47,
  0x0,0x70,
  0x0,0x61,0x0,0x73,0x0,0x74,0x0,0x65,0x0,0x2e,0x0,0x70,0x0,0x6e,0x0,0x67,
  
};

static const unsigned char qt_resource_struct[] = {
  // :
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x1,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons
  0x0,0x0,0x0,0x0,0x0,0x2,0x0,0x0,0x0,0x6,0x0,0x0,0x0,0x2,
0x0,0x0,0x0,0x0,0x0,0x0,0x0,0x0,
  // :/icons/new.png
  0x0,0x0,0x0,0x66,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x7,0x89,
0x0,0x0,0x1,0x97,0x3d,0x9b,0xbb,0x8c,
  // :/icons/copy.png
  0x0,0x0,0x0,0x50,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x5,0xea,
0x0,0x0,0x1,0x97,0x3d,0x9b,0xf0,0xff,
  // :/icons/open.png
  0x0,0x0,0x0,0x26,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0xec,
0x0,0x0,0x1,0x97,0x3d,0x9c,0x4c,0xb,
  // :/icons/save.png
  0x0,0x0,0x0,0x10,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x0,0x0,
0x0,0x0,0x1,0x97,0x3d,0x9c,0x16,0xff,
  // :/icons/paste.png
  0x0,0x0,0x0,0x7a,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x8,0xfb,
0x0,0x0,0x1,0x97,0x3d,0x9b,0x76,0xa1,
  // :/icons/cut.png
  0x0,0x0,0x0,0x3c,0x0,0x0,0x0,0x0,0x0,0x1,0x0,0x0,0x3,0x7a,
0x0,0x0,0x1,0x97,0x3d,0x9c,0x5c,0x1d,

};

#ifdef QT_NAMESPACE
#  define QT_RCC_PREPEND_NAMESPACE(name) ::QT_NAMESPACE::name
#  define QT_RCC_MANGLE_NAMESPACE0(x) x
#  define QT_RCC_MANGLE_NAMESPACE1(a, b) a##_##b
#  define QT_RCC_MANGLE_NAMESPACE2(a, b) QT_RCC_MANGLE_NAMESPACE1(a,b)
#  define QT_RCC_MANGLE_NAMESPACE(name) QT_RCC_MANGLE_NAMESPACE2( \
        QT_RCC_MANGLE_NAMESPACE0(name), QT_RCC_MANGLE_NAMESPACE0(QT_NAMESPACE))
#else
#   define QT_RCC_PREPEND_NAMESPACE(name) name
#   define QT_RCC_MANGLE_NAMESPACE(name) name
#endif

#ifdef QT_NAMESPACE
namespace QT_NAMESPACE {
#endif

bool qRegisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);
bool qUnregisterResourceData(int, const unsigned char *, const unsigned char *, const unsigned char *);

#ifdef QT_NAMESPACE
}
#endif

int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qRegisterResourceData)
        (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)();
int QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)()
{
    int version = 3;
    QT_RCC_PREPEND_NAMESPACE(qUnregisterResourceData)
       (version, qt_resource_struct, qt_resource_name, qt_resource_data);
    return 1;
}

namespace {
   struct initializer {
       initializer() { QT_RCC_MANGLE_NAMESPACE(qInitResources_icons)(); }
       ~initializer() { QT_RCC_MANGLE_NAMESPACE(qCleanupResources_icons)(); }
   } dummy;
}
