HEADERS += \
	$$PWD/SARibbonBar.h \
	$$PWD/SARibbonMainWindow.h \
	$$PWD/SAFramelessHelper.h \
	$$PWD/SAWindowButtonGroup.h \
	$$PWD/SARibbonApplicationButton.h \
	$$PWD/SARibbonTabBar.h \
	$$PWD/SARibbonCategory.h \
	$$PWD/SARibbonContextCategory.h \
	$$PWD/SARibbonPannel.h \
	$$PWD/SARibbonToolButton.h \
	$$PWD/SARibbonMenu.h \
	$$PWD/SARibbonGlobal.h \
	$$PWD/SARibbonPannelOptionButton.h \
	$$PWD/SARibbonSeparatorWidget.h \
	$$PWD/SARibbonCategoryLayout.h \
	$$PWD/SARibbonGallery.h \
	$$PWD/SARibbonControlButton.h \
	$$PWD/SARibbonGalleryGroup.h \
	$$PWD/SARibbonGalleryItem.h \
	$$PWD/SARibbonComboBox.h \
	$$PWD/SARibbonElementCreateDelegate.h \
	$$PWD/SARibbonElementManager.h \
	$$PWD/SARibbonDrawHelper.h \
	$$PWD/SARibbonLineEdit.h \
	$$PWD/SARibbonCheckBox.h \
	$$PWD/SARibbonButtonGroupWidget.h \
	$$PWD/SARibbonStackedWidget.h \
	$$PWD/SARibbonQuickAccessBar.h \
	$$PWD/SARibbonCtrlContainer.h \
	$$PWD/SARibbonActionsManager.h \
	$$PWD/SARibbonCustomizeData.h \
	$$PWD/SARibbonCustomizeDialog.h \
	$$PWD/SARibbonCustomizeWidget.h \
	$$PWD/SARibbonPannelLayout.h \
	$$PWD/SARibbonPannelItem.h \
        $$PWD/SARibbonLineWidgetContainer.h

SOURCES += \
	$$PWD/SARibbonBar.cpp \
	$$PWD/SARibbonMainWindow.cpp \
	$$PWD/SAFramelessHelper.cpp \
	$$PWD/SAWindowButtonGroup.cpp \
	$$PWD/SARibbonApplicationButton.cpp \
	$$PWD/SARibbonTabBar.cpp \
	$$PWD/SARibbonCategory.cpp \
	$$PWD/SARibbonContextCategory.cpp \
	$$PWD/SARibbonPannel.cpp \
	$$PWD/SARibbonToolButton.cpp \
	$$PWD/SARibbonMenu.cpp \
	$$PWD/SARibbonPannelOptionButton.cpp \
	$$PWD/SARibbonSeparatorWidget.cpp \
	$$PWD/SARibbonCategoryLayout.cpp \
	$$PWD/SARibbonGallery.cpp \
	$$PWD/SARibbonControlButton.cpp \
	$$PWD/SARibbonGalleryGroup.cpp \
	$$PWD/SARibbonGalleryItem.cpp \
	$$PWD/SARibbonComboBox.cpp \
	$$PWD/SARibbonElementCreateDelegate.cpp \
	$$PWD/SARibbonElementManager.cpp \
	$$PWD/SARibbonDrawHelper.cpp \
	$$PWD/SARibbonLineEdit.cpp \
	$$PWD/SARibbonCheckBox.cpp \
	$$PWD/SARibbonButtonGroupWidget.cpp \
	$$PWD/SARibbonStackedWidget.cpp \
	$$PWD/SARibbonQuickAccessBar.cpp \
	$$PWD/SARibbonCtrlContainer.cpp \
	$$PWD/SARibbonActionsManager.cpp \
	$$PWD/SARibbonCustomizeData.cpp \
	$$PWD/SARibbonCustomizeDialog.cpp \
	$$PWD/SARibbonCustomizeWidget.cpp \
	$$PWD/SARibbonPannelLayout.cpp \
	$$PWD/SARibbonPannelItem.cpp \
        $$PWD/SARibbonLineWidgetContainer.cpp
