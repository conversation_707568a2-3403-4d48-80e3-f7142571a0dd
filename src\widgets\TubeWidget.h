#ifndef TUBEWIDGET_H
#define TUBEWIDGET_H

#include <QWidget>
#include "../data/InputData.h"

QT_BEGIN_NAMESPACE
class QVBoxLayout;
class QHBoxLayout;
class QFormLayout;
class QLabel;
class QLineEdit;
class QDoubleSpinBox;
class QSpinBox;
class QGroupBox;
class QComboBox;
QT_END_NAMESPACE

namespace Ui {
class TubeWidget;
}

class TubeWidget : public QWidget
{
    Q_OBJECT

public:
    explicit TubeWidget(QWidget *parent = nullptr);
    explicit TubeWidget(const JunctionComponent& junction, QWidget *parent = nullptr);
    ~TubeWidget();

    void setJunctionComponent(const JunctionComponent& junction);
    JunctionComponent getJunctionComponent() const;
    void updateDisplay();
    void clear();
    
    // RELAP5相关功能
    bool validateData();
    QString generateRELAP5Cards() const;
    void loadFromRELAP5Cards(const QStringList& cards);

signals:
    void dataChanged();
    void componentModified(const QString& componentId);
    void validationError(const QString& message);

private slots:
    void onBasicDataChanged();
    void onGeometryDataChanged();
    void onInitialConditionChanged();
    void onControlFlagsChanged();
    void onComponentTypeChanged();
    void resetToDefaults();

private:
    void setupUI();
    void setupConnections();
    void updateControlFlags();
    void validateAndHighlightErrors();
    void setupComponentTypeCombo();
    
    // 数据验证
    bool validateGeometryData(QString& errorMsg);
    bool validateInitialConditions(QString& errorMsg);
    
    // 数据操作
    void fillWithDefaults();
    void updateFieldsForComponentType();

    // UI
    Ui::TubeWidget *ui;
    
    // 数据
    JunctionComponent m_junctionComponent;
    
    // 状态
    bool m_isUpdating;
};

#endif // TUBEWIDGET_H 