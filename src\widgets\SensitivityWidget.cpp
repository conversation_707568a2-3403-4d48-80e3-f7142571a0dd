#include "SensitivityWidget.h"
#include "ui_SensitivityWidget.h"

SensitivityWidget::SensitivityWidget(QWidget *parent)
    : QWidget(parent)
    , ui(new Ui::SensitivityWidget)
{
    ui->setupUi(this);
    setupConnections();
    updateMethodDescription();
    updateParameterVisibility();
}

SensitivityWidget::~SensitivityWidget()
{
    delete ui;
}

void SensitivityWidget::setupConnections()
{
    // Method selection
    connect(ui->methodCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SensitivityWidget::onMethodChanged);
    
    // Parameter changes
    connect(ui->samplesSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SensitivityWidget::onParameterChanged);
    connect(ui->samplingTypeCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &SensitivityWidget::onSamplingTypeChanged);
    connect(ui->seedSpin, QOverload<int>::of(&QSpinBox::valueChanged),
            this, &SensitivityWidget::onParameterChanged);
    connect(ui->toleranceSpin, QOverload<double>::of(&QDoubleSpinBox::valueChanged),
            this, &SensitivityWidget::onParameterChanged);
    
    // Checkboxes
    connect(ui->varianceBasedCheck, &QCheckBox::toggled,
            this, &SensitivityWidget::onVarianceBasedToggled);
    connect(ui->correlationMatrixCheck, &QCheckBox::toggled,
            this, &SensitivityWidget::onParameterChanged);
    connect(ui->sensitivityIndicesCheck, &QCheckBox::toggled,
            this, &SensitivityWidget::onParameterChanged);
    connect(ui->mainEffectsCheck, &QCheckBox::toggled,
            this, &SensitivityWidget::onParameterChanged);
    connect(ui->interactionEffectsCheck, &QCheckBox::toggled,
            this, &SensitivityWidget::onParameterChanged);
}

// Getter methods
QString SensitivityWidget::getMethod() const
{
    return ui->methodCombo->currentText();
}

int SensitivityWidget::getSamples() const
{
    return ui->samplesSpin->value();
}

QString SensitivityWidget::getSamplingType() const
{
    return ui->samplingTypeCombo->currentText();
}

bool SensitivityWidget::isVarianceBasedDecomp() const
{
    return ui->varianceBasedCheck->isChecked();
}

double SensitivityWidget::getConvergenceTolerance() const
{
    return ui->toleranceSpin->value();
}

int SensitivityWidget::getSeed() const
{
    return ui->seedSpin->value();
}

// Setter methods
void SensitivityWidget::setMethod(const QString &method)
{
    int index = ui->methodCombo->findText(method);
    if (index >= 0) {
        ui->methodCombo->setCurrentIndex(index);
    }
}

void SensitivityWidget::setSamples(int samples)
{
    ui->samplesSpin->setValue(samples);
}

void SensitivityWidget::setSamplingType(const QString &type)
{
    int index = ui->samplingTypeCombo->findText(type);
    if (index >= 0) {
        ui->samplingTypeCombo->setCurrentIndex(index);
    }
}

void SensitivityWidget::setVarianceBasedDecomp(bool enabled)
{
    ui->varianceBasedCheck->setChecked(enabled);
}

void SensitivityWidget::setConvergenceTolerance(double tolerance)
{
    ui->toleranceSpin->setValue(tolerance);
}

void SensitivityWidget::setSeed(int seed)
{
    ui->seedSpin->setValue(seed);
}

// Slots
void SensitivityWidget::onMethodChanged()
{
    updateMethodDescription();
    updateParameterVisibility();
    emit methodChanged(getMethod());
    emit parametersChanged();
}

void SensitivityWidget::onParameterChanged()
{
    emit parametersChanged();
}

void SensitivityWidget::onSamplingTypeChanged()
{
    emit parametersChanged();
}

void SensitivityWidget::onVarianceBasedToggled(bool enabled)
{
    // Enable/disable tolerance setting based on variance-based decomposition
    ui->toleranceLabel->setEnabled(enabled);
    ui->toleranceSpin->setEnabled(enabled);
    emit parametersChanged();
}

void SensitivityWidget::updateMethodDescription()
{
    QString method = getMethod();
    QString description;
    
    if (method == "sampling") {
        description = "Sampling: Monte Carlo and Latin Hypercube sampling for sensitivity analysis";
    } else if (method == "local_reliability") {
        description = "Local Reliability: Gradient-based sensitivity analysis using FORM/SORM";
    } else if (method == "polynomial_chaos") {
        description = "Polynomial Chaos: Spectral methods for global sensitivity analysis";
    } else if (method == "stoch_collocation") {
        description = "Stochastic Collocation: Interpolation-based uncertainty quantification";
    } else {
        description = "Select a method for sensitivity analysis";
    }
    
    ui->methodDescription->setText(description);
}

void SensitivityWidget::updateParameterVisibility()
{
    QString method = getMethod();
    
    // Show/hide parameters based on selected method
    bool showSampling = (method == "sampling" || method == "polynomial_chaos" || method == "stoch_collocation");
    ui->samplingGroup->setVisible(showSampling);
    
    // Variance-based decomposition is mainly for sampling methods
    ui->varianceBasedCheck->setVisible(method == "sampling");
    
    // Update tolerance visibility
    onVarianceBasedToggled(ui->varianceBasedCheck->isChecked());
} 