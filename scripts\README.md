# Markdown Time Rules - 使用说明

## 概述

这是一套用于在写入markdown文件时自动插入当前时间的规则和工具。

## 可用工具

### 1. PowerShell脚本 (推荐)

**文件：** `Update-MarkdownTime.ps1`

**使用方法：**
```powershell
# 更新现有文件的时间戳
powershell -ExecutionPolicy Bypass -File .\scripts\Update-MarkdownTime.ps1 -FilePath "program.md" -Action "update"

# 创建新文件并添加时间戳
powershell -ExecutionPolicy Bypass -File .\scripts\Update-MarkdownTime.ps1 -FilePath "new.md" -Action "create"

# 在文件末尾追加时间戳
powershell -ExecutionPolicy Bypass -File .\scripts\Update-MarkdownTime.ps1 -FilePath "example.md" -Action "append"
```

**参数说明：**
- `-FilePath`: 要处理的markdown文件路径
- `-Action`: 操作类型
  - `update`: 更新现有时间戳或在末尾添加新时间戳
  - `create`: 创建新文件并添加时间戳
  - `append`: 在文件末尾追加时间戳
- `-TimeFormat`: 时间格式
  - `chinese`: 2025年06月24日 09:30:00 (默认)
  - `iso`: 2025-06-24 09:30:00
  - `us`: 06/24/2025 09:30:00

### 2. 批处理脚本

**文件：** `update-md-time.bat`

**使用方法：**
```cmd
# 更新现有文件
.\scripts\update-md-time.bat program.md update

# 创建新文件
.\scripts\update-md-time.bat new.md create

# 追加时间戳
.\scripts\update-md-time.bat example.md append
```

### 3. 手动命令

**获取当前时间：**
```powershell
Get-Date -Format "yyyy年MM月dd日 HH:mm:ss"
```

**手动添加时间戳：**
```powershell
$timestamp = Get-Date -Format "yyyy年MM月dd日 HH:mm:ss"
Add-Content -Path "example.md" -Value "`n`n**最后更新：** $timestamp"
```

## 规则说明

### 自动时间戳规则

1. **创建新文件时**：
   - 自动添加创建时间和最后更新时间
   - 使用标准的markdown格式

2. **更新现有文件时**：
   - 查找现有的时间戳模式并更新
   - 如果没找到现有时间戳，在文件末尾添加
   - 支持的时间戳模式：
     - `**最后更新：**`
     - `**更新时间：**`
     - `**文档更新时间：**`

3. **追加模式**：
   - 直接在文件末尾添加新的时间戳
   - 不检查现有时间戳

### 时间戳格式

默认使用中文格式：`**最后更新：** 2025年06月24日 09:30:00`

## 快捷使用

### 创建别名 (PowerShell)

在PowerShell配置文件中添加：
```powershell
function Update-MdTime {
    param([string]$File, [string]$Action = "update")
    powershell -ExecutionPolicy Bypass -File ".\scripts\Update-MarkdownTime.ps1" -FilePath $File -Action $Action
}

# 使用示例
Update-MdTime "program.md"
```

### 批量更新

```powershell
# 更新当前目录下所有markdown文件
Get-ChildItem -Filter "*.md" | ForEach-Object {
    powershell -ExecutionPolicy Bypass -File ".\scripts\Update-MarkdownTime.ps1" -FilePath $_.Name -Action "update"
}
```

## 集成到工作流

### Git Hook 集成

在 `.git/hooks/pre-commit` 中添加：
```bash
#!/bin/sh
# 在提交前自动更新markdown文件时间戳
find . -name "*.md" -exec powershell -ExecutionPolicy Bypass -File "./scripts/Update-MarkdownTime.ps1" -FilePath {} -Action "update" \;
```

### VS Code 集成

在 `.vscode/tasks.json` 中添加：
```json
{
    "label": "Update Markdown Timestamp",
    "type": "shell",
    "command": "powershell",
    "args": [
        "-ExecutionPolicy", "Bypass",
        "-File", "./scripts/Update-MarkdownTime.ps1",
        "-FilePath", "${file}",
        "-Action", "update"
    ],
    "group": "build"
}
```

## 注意事项

1. **编码问题**：确保文件使用UTF-8编码
2. **权限问题**：可能需要设置PowerShell执行策略
3. **备份建议**：在批量处理前建议备份重要文件
4. **格式一致性**：建议在项目中统一使用相同的时间戳格式

## 故障排除

### PowerShell执行策略错误

```powershell
# 临时设置执行策略
Set-ExecutionPolicy -ExecutionPolicy Bypass -Scope Process

# 或使用 -ExecutionPolicy Bypass 参数
powershell -ExecutionPolicy Bypass -File script.ps1
```

### 编码问题

确保PowerShell使用正确的编码：
```powershell
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8
```

## 示例输出

**新创建的文件：**
```markdown
# 新文档

**创建时间：** 2025年06月24日 09:30:00
**最后更新：** 2025年06月24日 09:30:00

---

```

**更新后的文件末尾：**
```markdown
...现有内容...

**最后更新：** 2025年06月24日 09:30:00
``` 