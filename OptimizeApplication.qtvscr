<FlowDocument xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" FontFamily="Segoe UI" FontSize="12">
  <FlowDocument.Tag><![CDATA[{
  "timestamp": "2025-06-05 15:22:40",
  "files": {
    "D:\\Qt\\source\\OptimizeApplication\\OptimizeApplication.vcxproj": {
      "before": "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",
      "after": "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"
    },
    "D:\\Qt\\source\\OptimizeApplication\\OptimizeApplication.vcxproj.filters": {
      "before": "5VnbjptGGM51pb5BLyxfVhrAGNsQvBsZDMlKTZseotwgWRjGZiIYyMyw67bqQ/UNO8bLaUGBjY2lpJa54Bf833z/eYZ/f3jxYvnqEIWje0goivHNeCJI4xHEXuwjvL8Zp2wH1PGr2++/W74j8UfosRF/HNObccBY8lIUqRfAyKVChDwS03jHBC+ORB/ewzBOIBEjuk1R6IuyJE3HXM1otLxjMHpN4jTJbrnARiGDZHSHvTD14c3Yjkk04kJIT2+M+G/5HqNPKbzzIWZohyC5/VvTpoqmShqYzYwVULaaDwx7oQHVXhuGJKtzy5j+sxQbLx4Xkam0DgziI216m6KlWLktHnnnEgqzpdzu3JDCpViRnJ5aiqf1/z/YvIYYEpdBv4eDFhNrrfI/WJmGBhTT0oBhWBNgSZIxlSaKYk0mfR3kJYnu6d7hoEexpwe6D3d67Ic64heBVG9xX5djviEqb6Dr8wzqkTNTTZtNVQmomrHmOSMpQFUtA8zlmW1YM9kybKOvSwI94E4JuEuCSEeY+wJ7+oH6X+CKr339v0Eap8SDPTyw1tZzS1ZkYKsrGyhzSwGGZq+BKlmmYcqSsZpafT3wiXj6jy3m5uWyUqa+tHB9k6R+7+snxTYX04U9AeZiNQPKdDEHK2k+B/JqKq8XM3kl23ZfP5XFq1q2guSjvnWZ7tLoeB1aHNlVwr5iNkuxPgc0xwIzNOMo4WNAORlQ4jleTKCzSpIQeS7jzdvk9wK3cGVWeGzIVes8taRYaM/7diGowz0gfw8ZdczAJexDdjMgWkbOjPEO7d+62N1DMiBYQS2lLI7+IBAOzi9lKKTOT/F+WGYpct66CH9A2I8frmBBXm3d6ErGe3804YCcIm64s9X3yO4s6Izj5qCecK35HTSzuzozNLO71F7kdymqAxZpUMnwS+CFjxuaOtopA3hli2IsPBsm19lJqqWQPBusNFgnXGHD7J1KKRkStFZMBgWqlZMhkXI7VgvKuXg9crHZaX24TfcO32tterXaJ3upZj7mCEUk5YIyOwgMoUvhdUFLnp0N/tIcrwJY4dc9VVyc4XUgKxyflp/WufDiNK+GWjL93Ah1aX5DY5Wsuoa2SzO7Bl7JrlrUrxKYAwCWM0FZuEuGCYF8h0uFQPC2rDmydbqv1F70iVJUAlY7xQCQzdZ0YsiPWzbI48fF7eNxJ7tcb8EtFzSZXR6qMYymaFMJ/5Yho5vPo86MT48po82Vxx19fbeWombglF8EmpNFqbXnOn6OceVIgR8eZ0dC1Mlcy48Xkj+FBO+bi6ifhzUWclSbe7YDImVnI5gl7Wr01LgIPIqeTaOiuB8bDB/OZvN5e/EPSXhgiMSlDA6MQd37MyG6cyyP0cbJ2fql8ytzTuHh/JIwFKG/YGW0b5NtsrK3yTNE4NE0EvOguCxSC0adLP/wdvr+ePsf",
      "after": "5Vjdjps4GO31Snu3DxDlciUDAZJAyaQKBNqROrudtqPeII2IcYIrsKltZrJb9WX7JOuQJoEQNcxkUqndKFzYgu/4fD/Hn/31j2fPRi+WWdq5Q4xjSi66PUXrdhCBNMZkcdEtxBxY3Rfj338bvWH0I4KiI18n/KKbCJE/V1UOE5RFXMkwZJTTuVAgzdQY3aGU5oipGZ8VOI1VXdOMrjTT6YwuBcpeMlrk5VBOBDgViHUuCUyLGF10A8qyjpxEfP1FR/5GNwR/KtBljIjAc4zY+LNtG6ZtaTbo990JMGd2DNxgaAMrmLquplsD3zW+jNTGh6tFlCb9pUBkRZuPCzxSK8PtK28ixlG5lPE8SjkaqZWZ9Vsjdb3+/webl4ggFgkUtwjQsOdPLfkHE8+1gen5NnBdvwd8TXMNrWeafq/XNkAwzx3owOXSySh0EidGc4fGqYPlwxB3DoTvWGB+ISqvUBTLCmpRM4Zt9w1LA5btTmXNaCawLN8FA70fuH5f993AbRuSxElkUBIZkiRzMJGxINBZ8vgRofjZ1/8WcVowiFpEYGpPB75u6iCwJgEwB74JXDuYAkvzPdfTNXdi+G0j8IlB588D7pZyWZGpxwrXL0nqXds4mYE3NIZBD3jDSR+YxnAAJtpgAPSJoU+HfX2iB0HbOO3EqypbSf7RmUXCiXi2epYHAnlMwn5iNiO13gc02wIv9WiWyzZg1xlwBkNIGQoneZ5iGAm5eXtyrEgPV3qFbxty1Tv7nlS31jf79naiDneP4wUSPPSSiIkP5eCMaCU5j5I5XlxFJFogdkawLbWCC5q9ZwidnV8hcMrD13RxXmYFDq8iTD5gEtP7H+BBqbZR9oOcd7Ny4Rk5ZdJxJ5s/Xt3X4orCeqkdrOykWdfVbqFR16XdTU0fANkmfaWeT8Lw0m8HlzqZdaZLBcsoUR4KsLX5HSIHpOKhMK19tS8QTw1Uk4UnN14Tg6e2vsmnqgQ8HuN43XhlLNzVoXqXcDGaFYtQnoxuc4Zkj8GVRIEz0SydvTNPo3oq1jepV5naATKUooijc0Aed8G1uMF70lEX/AI3me8uFRqkS3sl3ePYf1FS6UfkybPsJ3mIobxGkL1J/o+Sk0UTvt5MN5awMrtx+BGIQpyMcC3ewooD91go8ljxYAKlyXYMCLo/mcH3fSRvnsiZIfKIC3RmDB7dnQhxPJ83edlotafPw2sRrtM7/DsXOMP/okrbfWjuttSh200+KTKPOuomKZ4W6QBGnay8qVtfWI7/Aw=="
    },
    "D:\\Qt\\source\\OptimizeApplication\\OptimizeApplication.vcxproj.user": {
      "before": "tZE9a8MwEIbTtdD/IETAyVDL/aCUYiVDCl06JG3obstnW0XWGekUUsgf7r+o7OItYzvee8f7PHDfF7NZvj52hh3AeY1W8ps04wyswkrbRvJA9fUjX6+uLvOtw09QxPaIxn9M95vgHFjiLLZYL3lL1D8J4VULXeHTTiuHHmtKFXaiggMY7MGJzpdBm0rcZtkdj+2MDf1xQ18vDkPPxLnwtSjBSL6jdyCKfp6zDdpK06iezBdxqnUTXDEky9N8sTUF1ei6ZSJl8gxlaE7Hh/vkFxmhO9pjUO3EHqExFufyXExno+I/CL6BgcLDXyqO0sPfVj8=",
      "after": "tZHNSsNAFIXrVvAdhqGQdmEm/iAimXRRwY2LVov7ZHKTjMzkhpk7pUJf1XdxEulGXOrynns454PzeTab5auDNWwPzmvsJb9KM86gV1jrvpU8UHN5z1fFxXm+cfgOitgO0fi3k38dnIOeOIspvZe8IxoehPCqA1v61Grl0GNDqUIratiDwQGcsL4K2tTiOstueExnbMyPH/p4chgGJn4Tn8sKjORbegWiyOc5W2Nfa5rQk/kiXo1ugytHZXmcLzampAadXSZSJo9QhfZ4uLtNvitj6ZZ2GFR36i5y8VOZ2MTJMMH9A9oLGCg9/A1cPuKOWxVf"
    }
  }
}]]></FlowDocument.Tag>
  <Section Margin="0,24" TextAlignment="Center">
    <Paragraph FontSize="24" FontWeight="Bold" Margin="12,0">
      <LineBreak />
      <Span Foreground="Gray">Qt Visual Studio Tools</Span>
    </Paragraph>
    <Paragraph FontSize="42" Margin="12,0" FontWeight="Bold">
      <Span TextDecorations="Underline">Project Format Conversion</Span>
    </Paragraph>
    <Paragraph Margin="12,8" FontSize="18">
      <Span>Report generated on 2025-06-05 15:22:40</Span>
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Files</Span>
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
    <Paragraph Margin="24,12,0,0">
      <Span FontFamily="Consolas" FontSize="14" Background="WhiteSmoke"><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user]]></Span>
      <LineBreak />
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user?before">[Before]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user?after">[After]</Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user?diff&amp;before&amp;after">
                        [Diff before/after]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user?diff&amp;before&amp;current">
                        [Diff before/current]
                    </Hyperlink>
      <Hyperlink NavigateUri="file://D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.user?diff&amp;after&amp;current">
                        [Diff after/current]
                    </Hyperlink>
      <LineBreak />
    </Paragraph>
  </Section>
  <Section>
    <Paragraph FontSize="32" FontWeight="Bold" Margin="12,0">
      <Span>Changes</Span>
    </Paragraph>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "$(QTDIR)"]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;src;src\ui;src\widgets;src\core;src\utils]]></Span><Span Background="LightCoral"><![CDATA[;..\..\Qt5.14.2\5.14.2\msvc2017_64\include;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtCharts;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore]]></Span><Span><![CDATA[;release;.;/include]]></Span><Span Background="LightCoral"><![CDATA[;..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;src;src\ui;src\widgets;src\core;src\utils]]></Span><Span Background="LightGreen"><![CDATA[;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;release;.;/include]]></Span><Span Background="LightGreen"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\5.14.2\msvc2017_64\lib\Qt5Charts.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\msvc2017_64\lib\Qt5Widgets.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\5.14.2\msvc2017_64\lib\Qt5Gui.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\msvc2017_64\lib\Qt5Core.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmain]]></Span><Span><![CDATA[.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\Qt5Charts]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Widgets]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Gui]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Core]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\qtmain]]></Span><Span><![CDATA[.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;src;src\ui;src\widgets;src\core;src\utils]]></Span><Span Background="LightCoral"><![CDATA[;..\..\Qt5.14.2\5.14.2\msvc2017_64\include;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtCharts;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtWidgets;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtGui;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtANGLE;..\..\Qt5.14.2\5.14.2\msvc2017_64\include\QtCore]]></Span><Span><![CDATA[;debug;.;/include]]></Span><Span Background="LightCoral"><![CDATA[;..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>.;src;src\ui;src\widgets;src\core;src\utils]]></Span><Span Background="LightGreen"><![CDATA[;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore]]></Span><Span><![CDATA[;debug;.;/include]]></Span><Span Background="LightGreen"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>D:\Qt\Qt5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\5.14.2\msvc2017_64\lib\Qt5Chartsd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\msvc2017_64\lib\Qt5Widgetsd.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\5.14.2\msvc2017_64\lib\Qt5Guid.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[2\msvc2017_64\lib\Qt5Cored.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\lib\qtmaind]]></Span><Span><![CDATA[.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\Qt5Chartsd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Widgetsd]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Guid]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\Qt5Cored]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[lib;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\lib\qtmaind]]></Span><Span><![CDATA[.lib;shell32.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ApplicationCore.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ApplicationCore.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o release\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o release\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ApplicationCore.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ApplicationCore.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o debug\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o debug\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ChartWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ChartWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o release\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o release\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ChartWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ChartWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o debug\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o debug\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ConfigManager.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ConfigManager.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o release\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o release\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ConfigManager.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ConfigManager.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o debug\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o debug\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\CustomTreeWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\CustomTreeWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o release\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o release\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\CustomTreeWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\CustomTreeWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o debug\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o debug\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\utils\Logger.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\utils\Logger.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o release\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o release\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\utils\Logger.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\utils\Logger.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o debug\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o debug\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o release\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o release\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o debug\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o debug\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ParamWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ParamWidget.h;release\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o release\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/release/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o release\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ParamWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ParamWidget.h;debug\moc_predefs.h;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/Qt5.14.2/5.14.2/msvc2017_64/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o debug\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include D:/Qt/source/OptimizeApplication/debug/moc_predefs.h ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/mkspecs/win32-msvc]]></Span><Span><![CDATA[ -ID:/Qt/source/OptimizeApplication -ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui -ID:/Qt/source/OptimizeApplication/src/widgets -ID:/Qt/source/OptimizeApplication/src/core -ID:/Qt/source/OptimizeApplication/src/utils ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCharts]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtWidgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtGui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtANGLE]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I$(QTDIR)/include/QtCore]]></Span><Span><![CDATA[ -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o debug\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -Zi -MDd -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E]]></Span><Span Background="LightCoral"><![CDATA[ ..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;debug\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -Zi -MDd -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E]]></Span><Span Background="LightGreen"><![CDATA[ $(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;debug\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -O2 -MD -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E]]></Span><Span Background="LightCoral"><![CDATA[ ..\..\Qt5.14.2\5.14.2\msvc2017_64\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;release\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -O2 -MD -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E]]></Span><Span Background="LightGreen"><![CDATA[ $(QTDIR)\mkspecs\features\data\dummy]]></Span><Span><![CDATA[.cpp 2&gt;NUL &gt;release\moc_predefs.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic]]></Span><Span><![CDATA[.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic]]></Span><Span><![CDATA[.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.ui;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic]]></Span><Span><![CDATA[.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.ui;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\uic]]></Span><Span><![CDATA[.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\uic]]></Span><Span><![CDATA[.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">resources\icons.qrc;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc]]></Span><Span><![CDATA[.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">resources\icons.qrc;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\rcc]]></Span><Span><![CDATA[.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc]]></Span><Span><![CDATA[.exe -name icons resources\icons.qrc -o release\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(QTDIR)\bin\rcc]]></Span><Span><![CDATA[.exe -name icons resources\icons.qrc -o release\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">resources\icons.qrc;]]></Span><Span Background="LightCoral"><![CDATA[D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc]]></Span><Span><![CDATA[.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">resources\icons.qrc;]]></Span><Span Background="LightGreen"><![CDATA[$(QTDIR)\bin\rcc]]></Span><Span><![CDATA[.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">D:\Qt\Qt5.14.2\5.14.2\msvc2017_64\bin\rcc]]></Span><Span><![CDATA[.exe -name icons resources\icons.qrc -o debug\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(QTDIR)\bin\rcc]]></Span><Span><![CDATA[.exe -name icons resources\icons.qrc -o debug\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Replacing paths with "."]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o release\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o release\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o debug\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o debug\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o release\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o release\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o debug\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o debug\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o release\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o release\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o debug\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o debug\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o release\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o release\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o debug\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o debug\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o release\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o release\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o debug\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o debug\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o release\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o release\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o debug\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o debug\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/release/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o release\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/release/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o release\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span Background="LightCoral"><![CDATA[ D:/Qt/source/OptimizeApplication/debug/moc_predefs]]></Span><Span><![CDATA[.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src -ID:/Qt/source/OptimizeApplication/src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-ID:/Qt/source/OptimizeApplication/src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o debug\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include]]></Span><Span><![CDATA[ .]]></Span><Span Background="LightGreen"><![CDATA[/debug/moc_predefs.]]></Span><Span><![CDATA[h -I$(QTDIR)/mkspecs/win32-msvc ]]></Span><Span Background="LightGreen"><![CDATA[-I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/ui]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/widgets]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/core]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[-I./src/utils]]></Span><Span><![CDATA[ -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o debug\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightCoral"><![CDATA[Include="D:\Qt\source\OptimizeApplication\OptimizeApplication_debug_resource]]></Span><Span><![CDATA[.rc">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightGreen"><![CDATA[Include=".\OptimizeApplication_debug_resource]]></Span><Span><![CDATA[.rc">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightCoral"><![CDATA[Include="D:\Qt\source\OptimizeApplication\OptimizeApplication_resource]]></Span><Span><![CDATA[.rc">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    <ResourceCompile ]]></Span><Span Background="LightGreen"><![CDATA[Include=".\OptimizeApplication_resource]]></Span><Span><![CDATA[.rc">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting custom build steps to Qt/MSBuild items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;]]></Span><Span><![CDATA[src;src\ui;src\widgets;src\core;src\utils;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;release]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[/include;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[src;src\ui;src\widgets;src\core;src\utils;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;release]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[/include;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>./$(Configuration)/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;./src;./src/ui;./src/widgets;./src/core;./src/utils;$(QTDIR)/include;$(QTDIR)/include/QtCharts;$(QTDIR)/include/QtWidgets;$(QTDIR)/include/QtGui;$(QTDIR)/include/QtANGLE;$(QTDIR)/include/QtCore;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtRcc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\qrc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InitFuncName>icons</InitFuncName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Compression>default</Compression>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtRcc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalIncludeDirectories>.;]]></Span><Span><![CDATA[src;src\ui;src\widgets;src\core;src\utils;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;debug]]></Span><Span Background="LightCoral"><![CDATA[;.;]]></Span><Span><![CDATA[/include;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;]]></Span><Span><![CDATA[src;src\ui;src\widgets;src\core;src\utils;$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;debug]]></Span><Span Background="LightGreen"><![CDATA[;]]></Span><Span><![CDATA[/include;$(QTDIR)\mkspecs\win32-msvc;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtRcc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Compression>default</Compression>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InitFuncName>icons</InitFuncName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\qrc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Rcc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtRcc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <CompilerFlavor>msvc</CompilerFlavor>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <Include>./$(Configuration)/moc_predefs.h</Include>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Moc'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <DynamicSource>output</DynamicSource>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;./src;./src/ui;./src/widgets;./src/core;./src/utils;$(QTDIR)/include;$(QTDIR)/include/QtCharts;$(QTDIR)/include/QtWidgets;$(QTDIR)/include/QtGui;$(QTDIR)/include/QtANGLE;$(QTDIR)/include/QtCore;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <ExecutionDescription>Uic'ing %(Identity)...</ExecutionDescription>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    </QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\core\ApplicationCore.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\core\ApplicationCore.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ApplicationCore]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="src\widgets\ChartWidget]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o release\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/core/ApplicationCore.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_ApplicationCore.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ApplicationCore.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ApplicationCore.h -o debug\moc_ApplicationCore.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/core/ApplicationCore.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_ApplicationCore.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="src\widgets\ChartWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ChartWidget.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o release\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/widgets/ChartWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_ChartWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ChartWidget.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ChartWidget.h -o debug\moc_ChartWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/widgets/ChartWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_ChartWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\core\ConfigManager.]]></Span><Span Background="LightCoral"><![CDATA[h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\core\ConfigManager.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalInputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\core\ConfigManager]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="src\widgets\CustomTreeWidget]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Command]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[-I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o release\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="src\utils\Logger]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h"]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[/>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Message]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[src/core/ConfigManager.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="src\ui\MainWindow.h"]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[/>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<Outputs]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightCoral"><![CDATA[Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_ConfigManager]]></Span><Span><![CDATA[.]]></Span><Span Background="LightCoral"><![CDATA[cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ ]]></Span><Span Background="LightGreen"><![CDATA[Include="src\widgets\ParamWidget]]></Span><Span><![CDATA[.]]></Span><Span Background="LightGreen"><![CDATA[h" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\core\ConfigManager.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\core\ConfigManager.h -o debug\moc_ConfigManager.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/core/ConfigManager.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_ConfigManager.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="src\widgets\CustomTreeWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\CustomTreeWidget.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o release\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/widgets/CustomTreeWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_CustomTreeWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\CustomTreeWidget.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\CustomTreeWidget.h -o debug\moc_CustomTreeWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/widgets/CustomTreeWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_CustomTreeWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="src\utils\Logger.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\utils\Logger.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o release\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/utils/Logger.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_Logger.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\utils\Logger.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\utils\Logger.h -o debug\moc_Logger.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/utils/Logger.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_Logger.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="src\ui\MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o release\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/ui/MainWindow.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_MainWindow.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\ui\MainWindow.h -o debug\moc_MainWindow.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/ui/MainWindow.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_MainWindow.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <CustomBuild Include="src\widgets\ParamWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\widgets\ParamWidget.h;release\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DQT_NO_DEBUG_OUTPUT -DNDEBUG -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./release/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o release\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">MOC src/widgets/ParamWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\moc_ParamWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\widgets\ParamWidget.h;debug\moc_predefs.h;$(QTDIR)\bin\moc.exe;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\moc.exe  -DUNICODE -D_UNICODE -DWIN32 -D_ENABLE_EXTENDED_ALIGNED_STORAGE -DWIN64 -DDEBUG_MODE -DQT_CHARTS_LIB -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB --compiler-flavor=msvc --include ./debug/moc_predefs.h -I$(QTDIR)/mkspecs/win32-msvc -I. -I./src -I./src/ui -I./src/widgets -I./src/core -I./src/utils -I$(QTDIR)/include -I$(QTDIR)/include/QtCharts -I$(QTDIR)/include/QtWidgets -I$(QTDIR)/include/QtGui -I$(QTDIR)/include/QtANGLE -I$(QTDIR)/include/QtCore -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include" -I"C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include" -I"C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt" -I"C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt" -I"C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um" src\widgets\ParamWidget.h -o debug\moc_ParamWidget.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">MOC src/widgets/ParamWidget.h</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\moc_ParamWidget.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ApplicationCore.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ApplicationCore.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ChartWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ChartWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ConfigManager.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ConfigManager.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_CustomTreeWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_CustomTreeWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_Logger.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_Logger.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_MainWindow.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_MainWindow.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ParamWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ParamWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -Zi -MDd -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightCoral"><![CDATA[debug\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -Zi -MDd -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightGreen"><![CDATA[$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Debug|x64'">debug\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Debug|x64'">$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -O2 -MD -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightCoral"><![CDATA[release\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">cl -Bx"$(QTDIR)\bin\qmake.exe" -nologo -Zc:wchar_t -FS -Zc:rvalueCast -Zc:inline -Zc:strictStrings -Zc:throwingNew -Zc:referenceBinding -Zc:__cplusplus -O2 -MD -std:c++14 -W3 -w34100 -w34189 -w44996 -w44456 -w44457 -w44458 -wd4577 -wd4467 -E $(QTDIR)\mkspecs\features\data\dummy.cpp 2&gt;NUL &gt;]]></Span><Span Background="LightGreen"><![CDATA[$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'">release\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">$(IntDir)\moc_predefs]]></Span><Span><![CDATA[.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\qrc_icons.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\qrc_icons.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_MainWindow.h" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.]]></Span><Span Background="LightCoral"><![CDATA[ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.]]></Span><Span Background="LightGreen"><![CDATA[ui" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <FileType>Document</FileType>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">src\ui\MainWindow.ui;$(QTDIR)\bin\uic.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\uic.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">UIC src/ui/MainWindow.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">ui_MainWindow.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">src\ui\MainWindow.ui;$(QTDIR)\bin\uic.exe;src\widgets\CustomTreeWidget.h;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\uic.exe src\ui\MainWindow.ui -o ui_MainWindow.h</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">UIC src/ui/MainWindow.ui</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">ui_MainWindow.h;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="resources\icons.]]></Span><Span Background="LightCoral"><![CDATA[qrc">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtRcc]]></Span><Span><![CDATA[ Include="resources\icons.]]></Span><Span Background="LightGreen"><![CDATA[qrc" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <FileType>Document</FileType>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">resources\icons.qrc;$(QTDIR)\bin\rcc.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(QTDIR)\bin\rcc.exe -name icons resources\icons.qrc -o release\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">RCC resources/icons.qrc</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">release\qrc_icons.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">resources\icons.qrc;$(QTDIR)\bin\rcc.exe;resources\icons\save.png;resources\icons\open.png;resources\icons\cut.png;resources\icons\copy.png;resources\icons\new.png;resources\icons\paste.png;%(AdditionalInputs)</AdditionalInputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(QTDIR)\bin\rcc.exe -name icons resources\icons.qrc -o debug\qrc_icons.cpp</Command>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">RCC resources/icons.qrc</Message>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">debug\qrc_icons.cpp;%(Outputs)</Outputs>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj.filters]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\core\ApplicationCore.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\core\ApplicationCore.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\widgets\ChartWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\widgets\ChartWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\core\ConfigManager.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\core\ConfigManager.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\widgets\CustomTreeWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\widgets\CustomTreeWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\utils\Logger.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\utils\Logger.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\widgets\ParamWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtMoc]]></Span><Span><![CDATA[ Include="src\widgets\ParamWidget.h">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtMoc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ApplicationCore.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ApplicationCore.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ChartWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ChartWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ConfigManager.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ConfigManager.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_CustomTreeWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_CustomTreeWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_Logger.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_Logger.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_MainWindow.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_MainWindow.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\moc_ParamWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\moc_ParamWidget.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="debug\qrc_icons.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClCompile Include="release\qrc_icons.cpp">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClCompile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    <ClInclude Include="ui_MainWindow.h">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Filter>Generated Files</Filter>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[    </ClInclude>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.ui">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtUic]]></Span><Span><![CDATA[ Include="src\ui\MainWindow.ui">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtUic>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<CustomBuild]]></Span><Span><![CDATA[ Include="resources\icons.qrc">]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtRcc]]></Span><Span><![CDATA[ Include="resources\icons.qrc">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[</CustomBuild>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[</QtRcc>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Enabling multi-processor compilation]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <MultiProcessorCompilation>true</MultiProcessorCompilation>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Project format version]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightCoral"><![CDATA[<Keyword>Qt4VSv1.0</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<Keyword>QtVS_v304</Keyword>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Fallback for QTMSBUILD environment variable]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtMsBuild Condition="'$(QtMsBuild)'=='' OR !Exists('$(QtMsBuild)\qt.targets')">$(MSBuildProjectDirectory)\QtMsBuild</QtMsBuild>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Default Qt properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt_defaults.props" Condition="Exists('$(QtMsBuild)\qt_defaults.props')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt build settings]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Warn if Qt/MSBuild is not found]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Target Name="QtMsBuildNotFound" BeforeTargets="CustomBuild;ClCompile" Condition="!Exists('$(QtMsBuild)\qt.targets') OR !Exists('$(QtMsBuild)\Qt.props')">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Message Importance="High" Text="QtMsBuild: could not locate qt.targets, qt.props; project may not build correctly." />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </Target>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt property sheet]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <Import Project="$(QtMsBuild)\Qt.props" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Qt targets]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <Import Project="$(QtMsBuild)\qt.targets" Condition="Exists('$(QtMsBuild)\qt.targets')" />]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Copying Qt build reference to QtInstall project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightCoral"><![CDATA['=='Release|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)]]></Span><Span Background="LightGreen"><![CDATA['=='Release|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[  ]]></Span><Span Background="LightCoral"><![CDATA[<PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[    ]]></Span><Span Background="LightGreen"><![CDATA[<QtInstall>Qt5.14.2</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  <PropertyGroup Label="QtSettings" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtInstall>Qt5.14.2</QtInstall>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[  </PropertyGroup>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;]]></Span><Span Background="LightCoral"><![CDATA[QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module include paths from compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;src;src\ui;src\widgets;src\core;src\utils;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;]]></Span><Span><![CDATA[release;/include]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;src;src\ui;src\widgets;src\core;src\utils;]]></Span><Span><![CDATA[release;/include]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;src;src\ui;src\widgets;src\core;src\utils;]]></Span><Span Background="LightCoral"><![CDATA[$(QTDIR)\include;$(QTDIR)\include\QtCharts;$(QTDIR)\include\QtWidgets;$(QTDIR)\include\QtGui;$(QTDIR)\include\QtANGLE;$(QTDIR)\include\QtCore;]]></Span><Span><![CDATA[debug;/include]]></Span><Span Background="LightCoral"><![CDATA[;$(QTDIR)\mkspecs\win32-msvc]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <AdditionalIncludeDirectories>GeneratedFiles\$(ConfigurationName);GeneratedFiles;.;src;src\ui;src\widgets;src\core;src\utils;]]></Span><Span><![CDATA[debug;/include]]></Span><Span><![CDATA[;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module libraries from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\Qt5Charts.lib;$(QTDIR)\lib\Qt5Widgets.lib;$(QTDIR)\lib\Qt5Gui.lib;$(QTDIR)\lib\Qt5Core.lib;$(QTDIR)\lib\qtmain.lib;shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalDependencies>$(QTDIR)\lib\Qt5Chartsd.lib;$(QTDIR)\lib\Qt5Widgetsd.lib;$(QTDIR)\lib\Qt5Guid.lib;$(QTDIR)\lib\Qt5Cored.lib;$(QTDIR)\lib\qtmaind.lib;shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalDependencies>shell32]]></Span><Span><![CDATA[.lib;%(AdditionalDependencies)</AdditionalDependencies>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt lib path from linker properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightCoral"><![CDATA[<AdditionalLibraryDirectories>$(QTDIR)\lib;C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      ]]></Span><Span Background="LightGreen"><![CDATA[<AdditionalLibraryDirectories>C:\openssl\lib]]></Span><Span><![CDATA[;C:\Utils\my_sql\mysql-5.7.25-winx64\lib;C:\Utils\postgresql\pgsql\lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing Qt module macros from resource compiler properties]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;]]></Span><Span Background="LightCoral"><![CDATA[QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;]]></Span><Span><![CDATA[%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;]]></Span><Span Background="LightCoral"><![CDATA[QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span><![CDATA[      <PreprocessorDefinitions>_WINDOWS;UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;]]></Span><Span><![CDATA[_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Adding Qt module names to QtModules project property]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;gui;widgets;charts</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[    <QtModules>core;gui;widgets;charts</QtModules>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Converting OutputFile to <tool>Dir and <tool>FileName]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(Configuration)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtRccDir>$(Configuration)</QtRccDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicDir>$(ProjectDir)</QtUicDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicFileName>ui_%(Filename).h</QtUicFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtRccDir>$(Configuration)</QtRccDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtRccFileName>qrc_%(Filename).cpp</QtRccFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocDir>$(Configuration)</QtMocDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtMocFileName>moc_%(Filename).cpp</QtMocFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicDir>$(ProjectDir)</QtUicDir>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightGreen"><![CDATA[      <QtUicFileName>ui_%(Filename).h</QtUicFileName>]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
    <Paragraph FontSize="20" FontWeight="Bold" Margin="12">
      <Span><![CDATA[🡺 Removing old properties from project items]]></Span>
    </Paragraph>
    <Table CellSpacing="0" BorderBrush="Gray" BorderThickness="0.5">
      <Table.Columns>
        <TableColumn />
        <TableColumn />
      </Table.Columns>
      <TableRowGroup>
        <TableRow Background="Orange">
          <TableCell ColumnSpan="2" BorderThickness="0.5" BorderBrush="Gray">
            <Paragraph Margin="10,5" FontWeight="Bold">
              <Span><![CDATA[D:\Qt\source\OptimizeApplication\OptimizeApplication.vcxproj]]></Span>
            </Paragraph>
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;QT_NO_DEBUG_OUTPUT;NDEBUG;QT_NO_DEBUG;QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;./src;./src/ui;./src/widgets;./src/core;./src/utils;$(QTDIR)/include;$(QTDIR)/include/QtCharts;$(QTDIR)/include/QtWidgets;$(QTDIR)/include/QtGui;$(QTDIR)/include/QtANGLE;$(QTDIR)/include/QtCore;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\qrc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\qrc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(Configuration)\moc_%(Filename).cpp</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <Define>UNICODE;_UNICODE;WIN32;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;DEBUG_MODE;QT_CHARTS_LIB;QT_WIDGETS_LIB;QT_GUI_LIB;QT_CORE_LIB</Define>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <IncludePath>GeneratedFiles\$(ConfigurationName);GeneratedFiles;$(QTDIR)/mkspecs/win32-msvc;.;./src;./src/ui;./src/widgets;./src/core;./src/utils;$(QTDIR)/include;$(QTDIR)/include/QtCharts;$(QTDIR)/include/QtWidgets;$(QTDIR)/include/QtGui;$(QTDIR)/include/QtANGLE;$(QTDIR)/include/QtCore;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\ATLMFC\include;C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\VS\include;C:\Program Files (x86)\Windows Kits\10\include\10.0.26100.0\ucrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\winrt;C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\cppwinrt;C:\Program Files (x86)\Windows Kits\NETFXSDK\4.7.2\include\um</IncludePath>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell ColumnSpan="2" Background="WhiteSmoke" BorderBrush="Gray" BorderThickness="0.5" />
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <QTDIR>$(QTDIR)</QTDIR>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <InputFile>%(FullPath)</InputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
        <TableRow>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0">
              <Span Background="LightCoral"><![CDATA[      <OutputFile>$(ProjectDir)\ui_%(Filename).h</OutputFile>]]></Span>
            </Paragraph>
          </TableCell>
          <TableCell BorderThickness="0, 0, 0.5, 0" BorderBrush="Gray">
            <Paragraph FontFamily="Consolas" Margin="4, 0" />
          </TableCell>
        </TableRow>
      </TableRowGroup>
    </Table>
  </Section>
  <Section>
    <Paragraph />
  </Section>
</FlowDocument>
<!--xF1yianamTTtKFhglocRrLYnUkNMZvX9nfQ3CjnShKU=-->
