#include "MainWindow.h"
#include "ui_MainWindow.h"
#include "../utils/Logger.h"
#include "../core/ConfigManager.h"
#include "../widgets/OptimizeWidget.h"
#include "../widgets/SensitivityWidget.h"
#include "../widgets/UQWidget.h"
#include "../widgets/InputWidget.h"
#include "../widgets/OutputWidget.h"
#include "../widgets/SolverOutputWidget.h"
#include <QMessageBox>
#include <QFileDialog>
#include <QStyleFactory>
#include <QApplication>
#include <QScreen>
#include <QToolBar>
#include <QMenu>
#include <QMenuBar>
#include <QStatusBar>
#include <QToolButton>
#include <QStyle>
#include <QSplitter>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QComboBox>
#include <QWidget>
#include <QDockWidget>
#include <QTextEdit>
#include <QWidgetAction>
#include <QDebug>
#include <QGroupBox>
#include <QLineEdit>
#include <QPushButton>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QCheckBox>
#include <QFont>
#include <QCloseEvent>
#include <QSettings>
#include <QMainWindow>
#include <QVariant>
#include <QObject>
#include <QString>
#include <QIcon>
#include <QAction>
#include <QLocale>
#include <QStandardPaths>
#include <QDateTime>
#include <QTextCursor>
#include <QFile>
#include <QTextStream>
#include <QInputDialog>
#include <QLineEdit>
#include <QDir>
#include <QCoreApplication>
#include <QXmlStreamWriter>
#include <QXmlStreamReader>

MainWindow::MainWindow(QWidget *parent)
    : SARibbonMainWindow(parent, true),
      m_ribbonBar(ribbonBar()),
      m_themeManager(ThemeManager::instance()),
      m_splitter(nullptr),
      m_workAreaSplitter(nullptr),
      m_textEdit(nullptr),
      m_logOutput(nullptr),
      m_treeWidget(nullptr),
      m_paramWidget(nullptr),
      m_stackedWidget(nullptr),
      m_clearLogButton(nullptr),
      m_saveLogButton(nullptr),
      m_currentProjectName(""),
      m_currentProjectPath(""),
      m_isModified(false),
      m_openFileAction(nullptr),
      m_saveFileAction(nullptr),
      m_inputFilePath("")
{
    // Set window properties
    setWindowTitle("Optimize Application - No Project");
    setWindowIcon(QIcon(":/icons/new.png")); // Use available icon as app icon
    resize(1400, 900);
    setMinimumSize(800, 600);

    // Restore previous window geometry and state
    QSettings settings("OptimizeSolutions", "OptimizeApplication");
    restoreGeometry(settings.value("windowGeometry").toByteArray());
    restoreState(settings.value("windowState").toByteArray());

    // Setup central widget
    setupCentralWidget();

    // Setup Ribbon interface
    setupRibbonInterface();

    // Setup status bar
    setupStatusBar();

    // Connect theme manager signals
    connect(m_themeManager, &ThemeManager::themeChanged, this, &MainWindow::onThemeChanged);
    
    // Add initial log messages
    appendLog("OptimizeApplication initialized successfully", "INFO");
    appendLog("Ribbon interface loaded", "INFO");
    appendLog("Ready for user operations", "INFO");
    
    // Update initial UI state (disable Open/Save when no project is open)
    updateActionStates();

    // Adjust font sizes for high DPI displays
    adjustFontSizesForDPI();
}

MainWindow::~MainWindow()
{
    // Cleanup is handled by Qt's parent-child mechanism
}

void MainWindow::setupCentralWidget()
{
    // Create central widget with splitter
    QWidget* centralWidget = new QWidget(this);
    setCentralWidget(centralWidget);
    
    QHBoxLayout* mainLayout = new QHBoxLayout(centralWidget);
    
    // Create main splitter with three panels
    m_splitter = new QSplitter(Qt::Horizontal, this);
    mainLayout->addWidget(m_splitter);
    
    // Left panel - Project Navigator only
    QWidget* leftPanel = new QWidget();
    leftPanel->setMinimumWidth(200);
    leftPanel->setMaximumWidth(300);
    
    QVBoxLayout* leftLayout = new QVBoxLayout(leftPanel);
    leftLayout->setContentsMargins(5, 5, 5, 5);
    leftLayout->setSpacing(5);
    
    // Create TreeWidget for navigation
    QLabel* treeLabel = new QLabel("Project Navigator");
    treeLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    leftLayout->addWidget(treeLabel);
    
    m_treeWidget = new CustomTreeWidget(leftPanel);
    m_treeWidget->setMinimumHeight(300);
    leftLayout->addWidget(m_treeWidget);
    
    // Middle panel - Parameters only
    QWidget* middlePanel = new QWidget();
    middlePanel->setMinimumWidth(200);
    middlePanel->setMaximumWidth(350);
    
    QVBoxLayout* middleLayout = new QVBoxLayout(middlePanel);
    middleLayout->setContentsMargins(5, 5, 5, 5);
    middleLayout->setSpacing(5);
    
    // Create ParamWidget for parameters
    QLabel* paramLabel = new QLabel("Parameters");
    paramLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    middleLayout->addWidget(paramLabel);
    
    m_paramWidget = new ParamWidget(middlePanel);
    m_paramWidget->setMinimumHeight(300);
    middleLayout->addWidget(m_paramWidget);
    
    // Right panel - Main work area with log output
    QWidget* workArea = new QWidget();
    QVBoxLayout* workLayout = new QVBoxLayout(workArea);
    workLayout->setContentsMargins(5, 5, 5, 5);
    
    // Create vertical splitter for work area and log output
    m_workAreaSplitter = new QSplitter(Qt::Vertical, workArea);
    workLayout->addWidget(m_workAreaSplitter);
    
    // Main work area (upper part)
    QWidget* mainWorkWidget = new QWidget();
    QVBoxLayout* mainWorkLayout = new QVBoxLayout(mainWorkWidget);
    mainWorkLayout->setContentsMargins(0, 0, 0, 0);
    
    QLabel* workLabel = new QLabel("Main Work Area");
    workLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    mainWorkLayout->addWidget(workLabel);
    
    m_textEdit = new QTextEdit();
    m_textEdit->setPlainText("No project loaded.\n\nUse 'New Project' to create a new project or 'Open Project' to load an existing one.\n\nOnce a project is loaded, select items in the Project Navigator to see their parameters in the Parameters panel.");
    mainWorkLayout->addWidget(m_textEdit);
    
    // Log output area (lower part)
    QWidget* logWidget = new QWidget();
    QVBoxLayout* logLayout = new QVBoxLayout(logWidget);
    logLayout->setContentsMargins(0, 0, 0, 0);
    
    // Log header with controls
    QWidget* logHeader = new QWidget();
    QHBoxLayout* logHeaderLayout = new QHBoxLayout(logHeader);
    logHeaderLayout->setContentsMargins(5, 5, 5, 5);
    
    QLabel* logLabel = new QLabel("Log Output");
    logLabel->setStyleSheet("font-weight: bold; padding: 5px;");
    logHeaderLayout->addWidget(logLabel);
    
    logHeaderLayout->addStretch();
    
    m_clearLogButton = new QPushButton("Clear");
    m_clearLogButton->setToolTip("Clear log output");
    m_clearLogButton->setMaximumWidth(60);
    logHeaderLayout->addWidget(m_clearLogButton);
    
    m_saveLogButton = new QPushButton("Save");
    m_saveLogButton->setToolTip("Save log to file");
    m_saveLogButton->setMaximumWidth(60);
    logHeaderLayout->addWidget(m_saveLogButton);
    
    logLayout->addWidget(logHeader);
    
    // Log text area
    m_logOutput = new QTextEdit();
    m_logOutput->setMaximumHeight(200);
    m_logOutput->setMinimumHeight(100);
    m_logOutput->setReadOnly(true);
    m_logOutput->setStyleSheet("QTextEdit { background-color: #f5f5f5; font-family: 'Consolas', 'Monaco', monospace; font-size: 9pt; }");
    m_logOutput->setPlainText("Application started successfully.\nReady for operations...");
    logLayout->addWidget(m_logOutput);
    
    // Add widgets to work area splitter
    m_workAreaSplitter->addWidget(mainWorkWidget);
    m_workAreaSplitter->addWidget(logWidget);
    m_workAreaSplitter->setStretchFactor(0, 1);  // Main work area expandable
    m_workAreaSplitter->setStretchFactor(1, 0);  // Log area fixed size
    m_workAreaSplitter->setSizes({400, 150});     // Initial sizes
    
    // Add all three panels to main splitter
    m_splitter->addWidget(leftPanel);      // Project Navigator
    m_splitter->addWidget(middlePanel);    // Parameters
    m_splitter->addWidget(workArea);       // Main Work Area
    
    // Set stretch factors
    m_splitter->setStretchFactor(0, 0);  // Left panel fixed size
    m_splitter->setStretchFactor(1, 0);  // Middle panel fixed size
    m_splitter->setStretchFactor(2, 1);  // Work area expandable
    
    // Set initial splitter sizes (Navigator: 250px, Parameters: 300px, WorkArea: remaining)
    m_splitter->setSizes({250, 300, 650});
    
    // Connect TreeWidget signals to ParamWidget slots
    connect(m_treeWidget, &CustomTreeWidget::nodeSelected, this, &MainWindow::onTreeItemChanged);
    connect(m_treeWidget, &CustomTreeWidget::nodeDoubleClicked, this, &MainWindow::onTreeItemDoubleClicked);
    connect(m_treeWidget, &CustomTreeWidget::moduleWidgetRequested, this, &MainWindow::onModuleWidgetRequested);
    
    // Connect log output signals
    connect(m_clearLogButton, &QPushButton::clicked, this, &MainWindow::onClearLog);
    connect(m_saveLogButton, &QPushButton::clicked, this, &MainWindow::onSaveLog);
}

void MainWindow::setupStatusBar()
{
    QStatusBar* status = statusBar();
    
    // Add permanent widgets to status bar
    QLabel* statusLabel = new QLabel("Ready");
    status->addWidget(statusLabel);
    
    QLabel* positionLabel = new QLabel("Position: 0, 0");
    status->addPermanentWidget(positionLabel);
    
    QLabel* zoomLabel = new QLabel("Zoom: 100%");
    status->addPermanentWidget(zoomLabel);
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    // Check for unsaved changes
    if (maybeSave()) {
        // Save window geometry and state
        QSettings settings("OptimizeSolutions", "OptimizeApplication");
        settings.setValue("windowGeometry", saveGeometry());
        settings.setValue("windowState", saveState());
        
        // Accept the close event
        event->accept();
    } else {
        // User canceled the close operation
        event->ignore();
    }
}

bool MainWindow::maybeSave()
{
    // Check if there are unsaved changes
    if (m_isModified) {
        QMessageBox::StandardButton ret = QMessageBox::warning(
            this, 
            "Unsaved Changes",
            "The project has been modified.\nDo you want to save your changes?",
            QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel
        );
        
        switch (ret) {
            case QMessageBox::Save:
                // Attempt to save the project
                onSaveFile();
                return true;
            case QMessageBox::Cancel:
                // User wants to cancel closing
                return false;
            default:
                // User wants to discard changes
                return true;
        }
    }
    
    // No unsaved changes
    return true;
}

void MainWindow::changeEvent(QEvent *event)
{
    if (event->type() == QEvent::LanguageChange) {
        // Retranslate UI elements when language changes
        setWindowTitle("Optimize Application");
        
        // Update ribbon tab names
        // This would require rebuilding the ribbon interface
        // setupRibbonInterface();
    }
    
    // Let the base class handle other change events
    SARibbonMainWindow::changeEvent(event);
}

void MainWindow::setupRibbonInterface()
{
    // Configure ribbon style
    setupRibbonStyle();

    // Create tabs
    createHomeTab();
    createEditTab();
    createViewTab();
    createToolsTab();
    createHelpTab();

    // Add contextual tabs
    addContextualTab("Image Processing");
    addContextualTab("Data Analysis");
}

void MainWindow::setupRibbonStyle()
{
    // Set ribbon style
    m_ribbonBar->setRibbonStyle(SARibbonBar::OfficeStyleTwoRow);
    
    // Hide application button (remove File button)
    m_ribbonBar->setApplicationButton(nullptr);
}

void MainWindow::createHomeTab()
{
    // Create Home tab
    SARibbonCategory* homeCategory = m_ribbonBar->addCategoryPage("Home");
    m_ribbonCategories["Home"] = homeCategory;
    
    // File Panel
    SARibbonPannel* filePannel = homeCategory->addPannel("File");
    
    QAction* newFileAction = new QAction(QIcon(":/icons/new.png"), "New", this);
    newFileAction->setShortcut(QKeySequence::New);
    newFileAction->setToolTip("Create new file (Ctrl+N)");
    
    m_openFileAction = new QAction(QIcon(":/icons/open.png"), "Open", this);
    m_openFileAction->setShortcut(QKeySequence::Open);
    m_openFileAction->setToolTip("Open file (Ctrl+O)");
    
    m_saveFileAction = new QAction(QIcon(":/icons/save.png"), "Save", this);
    m_saveFileAction->setShortcut(QKeySequence::Save);
    m_saveFileAction->setToolTip("Save file (Ctrl+S)");
    
    QAction* saveAsAction = new QAction(QIcon(":/icons/save.png"), "Save As", this);
    saveAsAction->setShortcut(QKeySequence::SaveAs);
    saveAsAction->setToolTip("Save As (Ctrl+Shift+S)");
    
    connect(newFileAction, &QAction::triggered, this, &MainWindow::onNewFile);
    connect(m_openFileAction, &QAction::triggered, this, &MainWindow::onOpenFile);
    connect(m_saveFileAction, &QAction::triggered, this, &MainWindow::onSaveFile);
    connect(saveAsAction, &QAction::triggered, this, &MainWindow::onSaveAs);
    
    filePannel->addLargeAction(newFileAction);
    filePannel->addLargeAction(m_openFileAction);
    filePannel->addLargeAction(m_saveFileAction);
    filePannel->addSmallAction(saveAsAction);
    
    // Project Panel
    SARibbonPannel* projectPannel = homeCategory->addPannel("Project");
    
    QAction* newProjectAction = new QAction(QIcon(":/icons/new.png"), "New Project", this);
    QAction* openProjectAction = new QAction(QIcon(":/icons/open.png"), "Open Project", this);
    QAction* closeProjectAction = new QAction(QIcon(":/icons/save.png"), "Close Project", this);
    
    connect(newProjectAction, &QAction::triggered, this, &MainWindow::onNewProject);
    connect(openProjectAction, &QAction::triggered, this, &MainWindow::onOpenProject);
    connect(closeProjectAction, &QAction::triggered, this, &MainWindow::onCloseProject);
    
    projectPannel->addLargeAction(newProjectAction);
    projectPannel->addSmallAction(openProjectAction);
    projectPannel->addSmallAction(closeProjectAction);
}

void MainWindow::createEditTab()
{
    // Create Edit tab
    SARibbonCategory* editCategory = m_ribbonBar->addCategoryPage("Edit");
    m_ribbonCategories["Edit"] = editCategory;
    
    // Clipboard Panel
    SARibbonPannel* clipboardPannel = editCategory->addPannel("Clipboard");
    
    QAction* cutAction = new QAction(QIcon(":/icons/cut.png"), "Cut", this);
    cutAction->setShortcut(QKeySequence::Cut);
    cutAction->setToolTip("Cut (Ctrl+X)");
    
    QAction* copyAction = new QAction(QIcon(":/icons/copy.png"), "Copy", this);
    copyAction->setShortcut(QKeySequence::Copy);
    copyAction->setToolTip("Copy (Ctrl+C)");
    
    QAction* pasteAction = new QAction(QIcon(":/icons/paste.png"), "Paste", this);
    pasteAction->setShortcut(QKeySequence::Paste);
    pasteAction->setToolTip("Paste (Ctrl+V)");
    
    connect(cutAction, &QAction::triggered, this, &MainWindow::onCut);
    connect(copyAction, &QAction::triggered, this, &MainWindow::onCopy);
    connect(pasteAction, &QAction::triggered, this, &MainWindow::onPaste);
    
    clipboardPannel->addLargeAction(pasteAction);
    clipboardPannel->addSmallAction(cutAction);
    clipboardPannel->addSmallAction(copyAction);
    
    // Undo/Redo Panel
    SARibbonPannel* undoPannel = editCategory->addPannel("Undo");
    
    QAction* undoAction = new QAction(QIcon(":/icons/cut.png"), "Undo", this);
    undoAction->setShortcut(QKeySequence::Undo);
    
    QAction* redoAction = new QAction(QIcon(":/icons/copy.png"), "Redo", this);
    redoAction->setShortcut(QKeySequence::Redo);
    
    connect(undoAction, &QAction::triggered, this, &MainWindow::onUndo);
    connect(redoAction, &QAction::triggered, this, &MainWindow::onRedo);
    
    undoPannel->addSmallAction(undoAction);
    undoPannel->addSmallAction(redoAction);
}

void MainWindow::createViewTab()
{
    // Create View tab
    SARibbonCategory* viewCategory = m_ribbonBar->addCategoryPage("View");
    m_ribbonCategories["View"] = viewCategory;
    
    // Zoom Panel
    SARibbonPannel* zoomPannel = viewCategory->addPannel("Zoom");
    
    QAction* zoomInAction = new QAction(QIcon(":/icons/new.png"), "Zoom In", this);
    zoomInAction->setShortcut(QKeySequence::ZoomIn);
    
    QAction* zoomOutAction = new QAction(QIcon(":/icons/open.png"), "Zoom Out", this);
    zoomOutAction->setShortcut(QKeySequence::ZoomOut);
    
    QAction* resetViewAction = new QAction(QIcon(":/icons/save.png"), "Fit Window", this);
    QAction* actualSizeAction = new QAction(QIcon(":/icons/copy.png"), "Actual Size", this);
    
    connect(zoomInAction, &QAction::triggered, this, &MainWindow::onZoomIn);
    connect(zoomOutAction, &QAction::triggered, this, &MainWindow::onZoomOut);
    connect(resetViewAction, &QAction::triggered, this, &MainWindow::onResetView);
    connect(actualSizeAction, &QAction::triggered, this, &MainWindow::onActualSize);
    
    zoomPannel->addSmallAction(zoomInAction);
    zoomPannel->addSmallAction(zoomOutAction);
    zoomPannel->addSmallAction(resetViewAction);
    zoomPannel->addSmallAction(actualSizeAction);
    
    // Layout Panel
    SARibbonPannel* layoutPannel = viewCategory->addPannel("Layout");
    
    QAction* showToolsAction = new QAction(QIcon(":/icons/cut.png"), "Tools Panel", this);
    showToolsAction->setCheckable(true);
    showToolsAction->setChecked(true);
    
    QAction* showPropertiesAction = new QAction(QIcon(":/icons/paste.png"), "Properties Panel", this);
    showPropertiesAction->setCheckable(true);
    showPropertiesAction->setChecked(true);
    
    QAction* showLogAction = new QAction(QIcon(":/icons/save.png"), "Log Panel", this);
    showLogAction->setCheckable(true);
    showLogAction->setChecked(true);
    showLogAction->setToolTip("Toggle log output panel");
    
    QAction* fullScreenAction = new QAction(QIcon(":/icons/new.png"), "Full Screen", this);
    fullScreenAction->setShortcut(QKeySequence::FullScreen);
    fullScreenAction->setCheckable(true);
    
    connect(showToolsAction, &QAction::toggled, this, &MainWindow::onToggleToolsPanel);
    connect(showPropertiesAction, &QAction::toggled, this, &MainWindow::onTogglePropertiesPanel);
    connect(showLogAction, &QAction::toggled, this, &MainWindow::onToggleLogPanel);
    connect(fullScreenAction, &QAction::toggled, this, &MainWindow::onToggleFullScreen);
    
    layoutPannel->addSmallAction(showToolsAction);
    layoutPannel->addSmallAction(showPropertiesAction);
    layoutPannel->addSmallAction(showLogAction);
    layoutPannel->addLargeAction(fullScreenAction);
}

void MainWindow::createToolsTab()
{
    // Create Tools tab
    SARibbonCategory* toolsCategory = m_ribbonBar->addCategoryPage("Tools");
    m_ribbonCategories["Tools"] = toolsCategory;
    
    // Image Processing Panel
    SARibbonPannel* processingPannel = toolsCategory->addPannel("Image Processing");
    
    QAction* filterAction = new QAction(QIcon(":/icons/new.png"), "Filter", this);
    QAction* adjustAction = new QAction(QIcon(":/icons/open.png"), "Adjust", this);
    QAction* effectsAction = new QAction(QIcon(":/icons/save.png"), "Effects", this);
    
    connect(filterAction, &QAction::triggered, this, &MainWindow::onApplyFilter);
    connect(adjustAction, &QAction::triggered, this, &MainWindow::onAdjustImage);
    connect(effectsAction, &QAction::triggered, this, &MainWindow::onApplyEffects);
    
    processingPannel->addLargeAction(filterAction);
    processingPannel->addSmallAction(adjustAction);
    processingPannel->addSmallAction(effectsAction);
    
    // Analysis Panel
    SARibbonPannel* analysisPannel = toolsCategory->addPannel("Analysis");
    
    QAction* histogramAction = new QAction(QIcon(":/icons/cut.png"), "Histogram", this);
    QAction* statisticsAction = new QAction(QIcon(":/icons/copy.png"), "Statistics", this);
    QAction* measureAction = new QAction(QIcon(":/icons/paste.png"), "Measure", this);
    
    connect(histogramAction, &QAction::triggered, this, &MainWindow::onShowHistogram);
    connect(statisticsAction, &QAction::triggered, this, &MainWindow::onShowStatistics);
    connect(measureAction, &QAction::triggered, this, &MainWindow::onMeasure);
    
    analysisPannel->addSmallAction(histogramAction);
    analysisPannel->addSmallAction(statisticsAction);
    analysisPannel->addSmallAction(measureAction);
}

void MainWindow::createHelpTab()
{
    // Create Help tab
    SARibbonCategory* helpCategory = m_ribbonBar->addCategoryPage("Help");
    m_ribbonCategories["Help"] = helpCategory;
    
    // Help Panel
    SARibbonPannel* helpPannel = helpCategory->addPannel("Support");
    
    QAction* showHelpAction = new QAction(QIcon(":/icons/open.png"), "Help Contents", this);
    showHelpAction->setShortcut(QKeySequence::HelpContents);
    
    QAction* aboutAction = new QAction(QIcon(":/icons/new.png"), "About", this);
    QAction* checkUpdatesAction = new QAction(QIcon(":/icons/save.png"), "Check Updates", this);
    
    connect(showHelpAction, &QAction::triggered, this, &MainWindow::onShowHelp);
    connect(aboutAction, &QAction::triggered, this, &MainWindow::onAbout);
    connect(checkUpdatesAction, &QAction::triggered, this, &MainWindow::onCheckUpdates);
    
    helpPannel->addLargeAction(showHelpAction);
    helpPannel->addSmallAction(aboutAction);
    helpPannel->addSmallAction(checkUpdatesAction);
    
    // Settings Panel
    SARibbonPannel* settingsPannel = helpCategory->addPannel("Settings");
    
    QAction* preferencesAction = new QAction(QIcon(":/icons/cut.png"), "Preferences", this);
    QAction* customizeAction = new QAction(QIcon(":/icons/copy.png"), "Customize", this);
    
    connect(preferencesAction, &QAction::triggered, this, &MainWindow::onPreferences);
    connect(customizeAction, &QAction::triggered, this, &MainWindow::onCustomize);
    
    settingsPannel->addLargeAction(preferencesAction);
    settingsPannel->addSmallAction(customizeAction);
}

void MainWindow::addContextualTab(const QString& name)
{
    // Create a context category instead of a regular category
    SARibbonContextCategory* contextCategory = m_ribbonBar->addContextCategory(name);
    // Store reference for later use
    m_contextCategories[name] = contextCategory;
    // Context categories are automatically managed by the ribbon bar
}

void MainWindow::toggleContextualTab(const QString& name, bool visible)
{
    // Find and toggle the visibility of a specific contextual tab
    if (m_contextCategories.contains(name)) {
        SARibbonContextCategory* contextCategory = m_contextCategories[name];
        m_ribbonBar->setContextCategoryVisible(contextCategory, visible);
    }
}

// Placeholder implementations for slot methods
void MainWindow::onNewFile() { /* Implement new file logic */ }
void MainWindow::onOpenFile() 
{
    // Check if we have a current project
    if (m_currentProjectName.isEmpty() || m_currentProjectPath.isEmpty()) {
        QMessageBox::warning(this, tr("No Project"), 
                           tr("Please create or open a project first."));
        return;
    }
    
    // Open file dialog to select .in file
    QString fileName = QFileDialog::getOpenFileName(
        this,
        tr("Open Input File"),
        QString(),
        tr("Input Files (*.i);;All Files (*)")
    );
    
    if (!fileName.isEmpty()) {
        QFileInfo sourceFileInfo(fileName);
        
        // Create target path in project directory
        QString targetFileName = sourceFileInfo.fileName();
        QString targetFilePath = QDir(m_currentProjectPath).absoluteFilePath(targetFileName);
        
        // Copy file to project directory
        if (QFile::exists(targetFilePath)) {
            int ret = QMessageBox::question(this, 
                                          tr("File Exists"),
                                          tr("A file with the same name already exists in the project directory. Do you want to overwrite it?"),
                                          QMessageBox::Yes | QMessageBox::No);
            if (ret != QMessageBox::Yes) {
                return;
            }
            // Remove existing file
            QFile::remove(targetFilePath);
        }
        
        // Copy the file
        if (!QFile::copy(fileName, targetFilePath)) {
            QMessageBox::warning(this, tr("Error"), 
                               tr("Failed to copy input file to project directory."));
            return;
        }
        
        // Store the relative path for XML saving
        m_inputFilePath = targetFileName;
        
        // Set the input file path in the tree widget and reload modules
        if (m_treeWidget) {
            m_treeWidget->setInputFilePath(targetFilePath);
        }
        
        // Update main work area to show file information
        if (m_textEdit) {
            QFileInfo targetFileInfo(targetFilePath);
            QString fileContent = QString(
                "Input File Loaded: %1\n\n"
                "Original Path: %2\n"
                "Project Path: %3\n"
                "File Size: %4 bytes\n"
                "Last Modified: %5\n\n"
                "The file has been copied to the project directory and parsed.\n"
                "Components are now available in the Solver node.\n"
                "Double-click on any component in the Project Navigator to edit its properties.\n\n"
                "Available component types:\n"
                "• Pipe Components - Flow path definitions\n"
                "• Junction Components - Connection points\n"
                "• Volume Components - Fluid volumes\n"
                "• Branch Components - Flow branches"
            ).arg(targetFileInfo.fileName())
             .arg(fileName)
             .arg(targetFilePath)
             .arg(targetFileInfo.size())
             .arg(targetFileInfo.lastModified().toString("yyyy-MM-dd hh:mm:ss"));
            
            m_textEdit->setPlainText(fileContent);
        }
        
        // Mark project as modified
        m_isModified = true;
        
        // Log the operation
        appendLog(QString("Opened input file: %1").arg(sourceFileInfo.fileName()), "INFO");
        appendLog(QString("File copied to project: %1").arg(targetFilePath), "INFO");
        appendLog("Solver node expanded and components loaded", "INFO");
        
        // Update status
        statusBar()->showMessage(QString("Loaded: %1").arg(sourceFileInfo.fileName()), 5000);
    }
}
void MainWindow::onSaveFile() 
{
    // Check if we have a current project
    if (m_currentProjectName.isEmpty() || m_currentProjectPath.isEmpty()) {
        QMessageBox::warning(this, tr("No Project"), 
                           tr("Please create or open a project first."));
        return;
    }
    
    // Generate project file path
    QString projectFileName = m_currentProjectName + ".xml";
    QString projectFilePath = QDir(m_currentProjectPath).absoluteFilePath(projectFileName);
    
    // Save project to XML
    if (saveProjectToXML(projectFilePath)) {
        m_currentFile = projectFilePath;
        m_isModified = false;
        
        // Update window title
        setWindowTitle(QString("Optimize Application - %1").arg(m_currentProjectName));
        
        // Log success
        appendLog(QString("Project saved: %1").arg(projectFileName), "INFO");
        statusBar()->showMessage(QString("Project saved: %1").arg(projectFileName), 3000);
        
        LOG_INFO(QString("Project saved to: %1").arg(projectFilePath), "MainWindow");
    } else {
        QMessageBox::warning(this, tr("Save Error"), 
                           tr("Failed to save project file."));
        appendLog("Failed to save project", "ERROR");
    }
}
void MainWindow::onSaveAs() { /* Implement save as logic */ }

void MainWindow::onCut() { /* Implement cut logic */ }
void MainWindow::onCopy() { /* Implement copy logic */ }
void MainWindow::onPaste() { /* Implement paste logic */ }

void MainWindow::onZoomIn() { /* Implement zoom in logic */ }
void MainWindow::onZoomOut() { /* Implement zoom out logic */ }
void MainWindow::onResetView() { /* Implement reset view logic */ }
void MainWindow::onActualSize() { /* Implement actual size logic */ }

void MainWindow::onShowHelp() { /* Implement help display logic */ }
void MainWindow::onAbout() 
{
    QMessageBox::about(this, "About",
        "Image Processing Application\n"
        "Version 1.0\n"
        "Built with Qt and SARibbonBar");
}

void MainWindow::onNewProject()
{
    // Check if there are unsaved changes
    if (!maybeSave()) {
        return;
    }
    
    // Get project name from user
    bool ok;
    QString projectName = QInputDialog::getText(this, 
                                              tr("New Project"),
                                              tr("Enter project name:"), 
                                              QLineEdit::Normal,
                                              tr("Untitled Project"), 
                                              &ok);
    
    if (!ok || projectName.isEmpty()) {
        return;
    }
    
    // Create project directory in exe directory
    QString exeDir = QCoreApplication::applicationDirPath();
    QString projectPath = QDir(exeDir).absoluteFilePath(projectName);
    
    // Check if directory already exists
    QDir projectDir(projectPath);
    if (projectDir.exists()) {
        int ret = QMessageBox::question(this, 
                                      tr("Project Exists"),
                                      tr("A project with this name already exists. Do you want to overwrite it?"),
                                      QMessageBox::Yes | QMessageBox::No);
        if (ret != QMessageBox::Yes) {
            return;
        }
        
        // Remove existing directory
        if (!projectDir.removeRecursively()) {
            QMessageBox::warning(this, tr("Error"), 
                                tr("Failed to remove existing project directory."));
            return;
        }
    }
    
    // Create new project directory
    if (!QDir().mkpath(projectPath)) {
        QMessageBox::warning(this, tr("Error"), 
                           tr("Failed to create project directory."));
        return;
    }
    
    // Update project information
    m_currentProjectName = projectName;
    m_currentProjectPath = projectPath;
    m_currentFile.clear();
    m_isModified = false;
    
    // Create new project in tree widget
    if (m_treeWidget) {
        m_treeWidget->createNewProject(projectName, projectPath);
    }
    
    // Update window title
    setWindowTitle(QString("Optimize Application - %1").arg(projectName));
    
    // Clear text editor
    if (m_textEdit) {
        m_textEdit->clear();
    }
    
    // Clear parameter widget
    if (m_paramWidget) {
        m_paramWidget->clearModuleWidget();
    }
    
    // Log the operation
    appendLog(QString("New project created: %1").arg(projectName), "INFO");
    appendLog(QString("Project directory: %1").arg(projectPath), "INFO");
    
    // Update status bar
    statusBar()->showMessage(QString("New project created: %1").arg(projectName), 5000);
    
    // Update action states after creating project
    updateActionStates();
    
    LOG_INFO(QString("New project created: %1 at %2").arg(projectName, projectPath), "MainWindow");
}

void MainWindow::onOpenProject()
{
    // Check if there are unsaved changes
    if (!maybeSave()) {
        return;
    }
    
    // Open file dialog to select project file
    QString filePath = QFileDialog::getOpenFileName(this,
                                                  tr("Open Project"),
                                                  QCoreApplication::applicationDirPath(),
                                                  tr("XML Project Files (*.xml);;All Files (*)"));
    
    if (filePath.isEmpty()) {
        return;
    }
    
    // Load project from XML
    if (loadProjectFromXML(filePath)) {
        // Log success
        appendLog(QString("Project opened: %1").arg(QFileInfo(filePath).fileName()), "INFO");
        statusBar()->showMessage(QString("Project opened: %1").arg(QFileInfo(filePath).fileName()), 3000);
        
        // Clear text editor and show project loaded message
        if (m_textEdit) {
            m_textEdit->clear();
            m_textEdit->setPlainText(QString("Project loaded: %1\n\nSelect items in the Project Navigator to view their parameters.").arg(m_currentProjectName));
        }
        
        LOG_INFO(QString("Project opened from: %1").arg(filePath), "MainWindow");
    } else {
        QMessageBox::warning(this, tr("Open Error"), 
                           tr("Failed to open project file."));
        appendLog("Failed to open project", "ERROR");
    }
}

void MainWindow::onCloseProject()
{
    // Check if there are unsaved changes
    if (!maybeSave()) {
        return;
    }
    
    // Clear project information
    m_currentProjectName.clear();
    m_currentProjectPath.clear();
    m_currentFile.clear();
    m_isModified = false;
    
    // Clear tree widget
    if (m_treeWidget) {
        m_treeWidget->clearProject();
    }
    
    // Update window title
    setWindowTitle("Optimize Application - No Project");
    
    // Clear text editor
    if (m_textEdit) {
        m_textEdit->clear();
        m_textEdit->setPlainText("No project loaded.\n\nUse 'New Project' to create a new project or 'Open Project' to load an existing one.");
    }
    
    // Clear parameter widget
    if (m_paramWidget) {
        m_paramWidget->clearModuleWidget();
    }
    
    // Log the operation
    appendLog("Project closed", "INFO");
    
    // Update status bar
    statusBar()->showMessage("Project closed", 3000);
    
    // Update action states after closing project
    updateActionStates();
    
    LOG_INFO("Project closed", "MainWindow");
}

void MainWindow::onImportData()
{
    // Implement data import logic
    LOG_INFO("Import data action triggered", "MainWindow");
}

void MainWindow::onExportData()
{
    // Implement data export logic
    LOG_INFO("Export data action triggered", "MainWindow");
}

void MainWindow::onSettings()
{
    // Implement settings dialog
    LOG_INFO("Settings action triggered", "MainWindow");
}

void MainWindow::onHelp()
{
    // Implement help dialog or documentation
    LOG_INFO("Help action triggered", "MainWindow");
}

void MainWindow::onRibbonCompactModeToggled(bool checked)
{
    // Toggle ribbon compact mode
    m_ribbonBar->setMinimumMode(checked);
}

void MainWindow::addCustomRibbonCategory(const QString& categoryName)
{
    SARibbonCategory* category = m_ribbonBar->addCategoryPage(categoryName);
    m_ribbonCategories[categoryName] = category;
    
    // Add a pannel to the category
    SARibbonPannel* pannel = category->addPannel("Context Controls");
    m_ribbonPannels[categoryName] = pannel;
}

void MainWindow::removeRibbonCategory(const QString& categoryName)
{
    if (m_ribbonCategories.contains(categoryName)) {
        m_ribbonBar->removeCategory(m_ribbonCategories[categoryName]);
        m_ribbonCategories.remove(categoryName);
        m_ribbonPannels.remove(categoryName);
    }
}

void MainWindow::addActionToPannel(const QString& categoryName, const QString& pannelName, QAction* action)
{
    if (m_ribbonCategories.contains(categoryName)) {
        SARibbonCategory* category = m_ribbonCategories[categoryName];
        SARibbonPannel* pannel = category->pannelByName(pannelName);
        
        if (pannel) {
            pannel->addAction(action);
        }
    }
}

void MainWindow::setRibbonContextCategory(const QString& categoryName, bool isVisible)
{
    if (m_ribbonCategories.contains(categoryName)) {
        SARibbonCategory* category = m_ribbonCategories[categoryName];
        category->setVisible(isVisible);
    }
}

void MainWindow::onContextualTabChanged(bool checked)
{
    // Example of dynamically showing/hiding a contextual tab
    if (m_ribbonCategories.contains("Context")) {
        setRibbonContextCategory("Context", checked);
    }
}

void MainWindow::onToggleToolsPanel(bool checked)
{
    // Implement toggle tools panel logic
    LOG_INFO(QString("Toggle tools panel: %s").arg(checked ? "true" : "false"), "MainWindow");
}

void MainWindow::onTogglePropertiesPanel(bool checked)
{
    // Implement toggle properties panel logic
    LOG_INFO(QString("Toggle properties panel: %s").arg(checked ? "true" : "false"), "MainWindow");
}

void MainWindow::onToggleFullScreen(bool checked)
{
    // Implement toggle full screen logic
    LOG_INFO(QString("Toggle full screen: %s").arg(checked ? "true" : "false"), "MainWindow");
}

void MainWindow::onApplyFilter()
{
    // Implement apply filter logic
    LOG_INFO("Apply filter action triggered", "MainWindow");
}

void MainWindow::onAdjustImage()
{
    // Implement adjust image logic
    LOG_INFO("Adjust image action triggered", "MainWindow");
}

void MainWindow::onApplyEffects()
{
    // Implement apply effects logic
    LOG_INFO("Apply effects action triggered", "MainWindow");
}

void MainWindow::onShowHistogram()
{
    // Implement show histogram logic
    LOG_INFO("Show histogram action triggered", "MainWindow");
}

void MainWindow::onShowStatistics()
{
    // Implement show statistics logic
    LOG_INFO("Show statistics action triggered", "MainWindow");
}

void MainWindow::onMeasure()
{
    // Implement measure logic
    LOG_INFO("Measure action triggered", "MainWindow");
}

void MainWindow::onPreferences()
{
    // Implement preferences logic
    LOG_INFO("Preferences action triggered", "MainWindow");
}

void MainWindow::onCustomize()
{
    // Implement customize logic
    LOG_INFO("Customize action triggered", "MainWindow");
}

void MainWindow::onCheckUpdates()
{
    // Implement check updates logic
    LOG_INFO("Check updates action triggered", "MainWindow");
}

void MainWindow::onUndo()
{
    // Implement undo logic
    LOG_INFO("Undo action triggered", "MainWindow");
}

void MainWindow::onRedo()
{
    // Implement redo logic
    LOG_INFO("Redo action triggered", "MainWindow");
}

void MainWindow::onThemeChanged()
{
    // Implement theme changed logic
    LOG_INFO("Theme changed", "MainWindow");
}

void MainWindow::onTreeItemChanged()
{
    if (!m_treeWidget || !m_paramWidget) {
        return;
    }
    
    // Save parameters from the previously selected node
    static QTreeWidgetItem* previousItem = nullptr;
    if (previousItem) {
        NodeType prevType = m_treeWidget->getNodeType(previousItem);
        if (prevType == NodeType::Optimize && m_paramWidget->getOptimizeWidget()) {
            saveOptimizationParametersToNode(previousItem, m_paramWidget->getOptimizeWidget());
        } else if (prevType == NodeType::Sensitivity && m_paramWidget->getSensitivityWidget()) {
            saveSensitivityParametersToNode(previousItem, m_paramWidget->getSensitivityWidget());
        } else if (prevType == NodeType::UQ && m_paramWidget->getUQWidget()) {
            saveUQParametersToNode(previousItem, m_paramWidget->getUQWidget());
        } else if (prevType == NodeType::Input && m_paramWidget->getInputWidget()) {
            saveInputParametersToNode(previousItem, m_paramWidget->getInputWidget());
        } else if (prevType == NodeType::Output) {
            // Check if the previous Output node was under Solver
            QTreeWidgetItem* prevParent = previousItem->parent();
            if (prevParent && m_treeWidget->getNodeType(prevParent) == NodeType::Solver) {
                // Save Solver Output parameters
                if (m_paramWidget->getSolverOutputWidget()) {
                    saveSolverOutputParametersToNode(previousItem, m_paramWidget->getSolverOutputWidget());
                }
            } else {
                // Save regular Output parameters
                if (m_paramWidget->getOutputWidget()) {
                    saveOutputParametersToNode(previousItem, m_paramWidget->getOutputWidget());
                }
            }
        }
    }
    
    QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
    if (!currentItem) {
        return;
    }
    
    // Update previous item reference
    previousItem = currentItem;
    
    // Get node type from the tree widget
    NodeType nodeType = m_treeWidget->getNodeType(currentItem);
    QString itemName = currentItem->text(0);
    
    // Check if this is a component under Solver node
    QTreeWidgetItem* solverItem = nullptr;
    for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
        QTreeWidgetItem* topItem = m_treeWidget->topLevelItem(i);
        if (m_treeWidget->getNodeType(topItem) == NodeType::Solver) {
            solverItem = topItem;
            break;
        }
    }
    
    // Check if this is a component under Solver/Input node or its categories
    bool isComponentItem = false;
    QTreeWidgetItem* parentItem = currentItem->parent();
    
    // Find the Solver/Input node
    QTreeWidgetItem* solverInputItem = nullptr;
    if (solverItem) {
        for (int i = 0; i < solverItem->childCount(); ++i) {
            QTreeWidgetItem* child = solverItem->child(i);
            if (m_treeWidget->getNodeType(child) == NodeType::Input) {
                solverInputItem = child;
                break;
            }
        }
    }
    
    if (parentItem == solverInputItem) {
        // This might be a category node (Pipes, Junctions, etc.), not a component
        isComponentItem = false;
    } else if (parentItem && parentItem->parent() == solverInputItem) {
        // This is a component under a category under Solver/Input
        isComponentItem = true;
    }
    
    if (isComponentItem) {
        // Clear any module widget and show component parameters in Parameters area
        m_paramWidget->clearModuleWidget();
        showComponentParameters(currentItem);
        return;
    }
    
    // Clear any module widget first
    m_paramWidget->clearModuleWidget();
    
    // Update parameter widget based on node type and load node-specific parameters
    switch (nodeType) {
        case NodeType::Optimize:
            m_paramWidget->showOptimizationParams();
            // Load this node's optimization parameters
            if (m_paramWidget->getOptimizeWidget()) {
                loadOptimizationParametersToWidget(currentItem, m_paramWidget->getOptimizeWidget());
            }
            break;
        case NodeType::Sensitivity:
            m_paramWidget->showSensitivityParams();
            // Load this node's sensitivity parameters
            if (m_paramWidget->getSensitivityWidget()) {
                loadSensitivityParametersToWidget(currentItem, m_paramWidget->getSensitivityWidget());
            }
            break;
        case NodeType::UQ:
            m_paramWidget->showUQParams();
            // Load this node's UQ parameters
            if (m_paramWidget->getUQWidget()) {
                loadUQParametersToWidget(currentItem, m_paramWidget->getUQWidget());
            }
            break;
        case NodeType::Input:
            m_paramWidget->showInputParams();
            // Load this node's input parameters
            if (m_paramWidget->getInputWidget()) {
                loadInputParametersToWidget(currentItem, m_paramWidget->getInputWidget());
            }
            break;
        case NodeType::Output:
            // Check if this Output node is under Solver node
            if (parentItem && m_treeWidget->getNodeType(parentItem) == NodeType::Solver) {
                // This is Solver/Output, show SolverOutputWidget
                m_paramWidget->showSolverOutputParams();
                // Load this node's solver output parameters
                if (m_paramWidget->getSolverOutputWidget()) {
                    loadSolverOutputParametersToWidget(currentItem, m_paramWidget->getSolverOutputWidget());
                }
            } else {
                // This is regular Output node, show OutputWidget
                m_paramWidget->showOutputParams();
                // Load this node's output parameters
                if (m_paramWidget->getOutputWidget()) {
                    loadOutputParametersToWidget(currentItem, m_paramWidget->getOutputWidget());
                }
            }
            break;
        default:
            // For other node types, show optimization parameters as default
            m_paramWidget->showOptimizationParams();
            break;
    }
}

void MainWindow::onTreeItemDoubleClicked(QTreeWidgetItem* item, NodeType type)
{
    if (!item || !m_textEdit) {
        return;
    }
    
    QString itemName = item->text(0);
    QString content;
    
    switch (type) {
        case NodeType::Root:
            content = QString("Project: %1\n\nThis is the root project node. "
                            "It contains all optimization settings, input parameters, and output configurations.\n\n"
                            "Double-click on child nodes to view their specific settings.").arg(itemName);
            break;
        case NodeType::Optimize:
            content = QString("Optimization Settings: %1\n\n"
                            "Configure optimization algorithms, population size, generations, mutation rates, "
                            "and convergence criteria here.\n\n"
                            "Current algorithm: Genetic Algorithm\n"
                            "Population size: 100\n"
                            "Max generations: 500\n"
                            "Mutation rate: 0.1").arg(itemName);
            break;
        case NodeType::Sensitivity:
            content = QString("Sensitivity Analysis: %1\n\n"
                            "Configure sensitivity analysis methods including sampling, polynomial chaos, "
                            "and variance-based decomposition.\n\n"
                            "Available methods:\n"
                            "• Sampling (Monte Carlo, LHS)\n"
                            "• Local Reliability (FORM/SORM)\n"
                            "• Polynomial Chaos Expansion\n"
                            "• Stochastic Collocation").arg(itemName);
            break;
        case NodeType::UQ:
            content = QString("Uncertainty Quantification: %1\n\n"
                            "Configure uncertainty quantification methods for propagating input uncertainties "
                            "through the model.\n\n"
                            "Available methods:\n"
                            "• Monte Carlo Sampling\n"
                            "• Polynomial Chaos Expansion\n"
                            "• Stochastic Collocation\n"
                            "• Reliability Analysis\n"
                            "• Importance Sampling").arg(itemName);
            break;
        case NodeType::Input:
            content = QString("Input Parameters: %1\n\n"
                            "Define input files, parameter ranges, and constraints for the analysis process.\n\n"
                            "Parameter count: 5\n"
                            "Value range: 0.0 to 100.0\n"
                            "Constraints: Enabled").arg(itemName);
            break;
        case NodeType::Output:
            content = QString("Output Settings: %1\n\n"
                            "Configure output directory, file formats, and report generation options.\n\n"
                            "Output directory: ./results/\n"
                            "File name: analysis_results\n"
                            "Generate report: Yes\n"
                            "Include charts: Yes").arg(itemName);
            break;
        default:
            content = QString("Selected: %1\n\nNode type: Unknown").arg(itemName);
            break;
    }
    
    m_textEdit->setPlainText(content);
    appendLog(QString("Opened details for: %1").arg(itemName), "INFO");
}

void MainWindow::onStyleChanged()
{
    // Implement style changed logic
    LOG_INFO("Style changed", "MainWindow");
}

// Log output methods implementation
void MainWindow::appendLog(const QString& message, const QString& level)
{
    if (!m_logOutput) {
        return;
    }
    
    // Get current timestamp
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    
    // Format log message with timestamp and level
    QString formattedMessage = QString("[%1] [%2] %3").arg(timestamp).arg(level).arg(message);
    
    // Append to log output
    m_logOutput->append(formattedMessage);
    
    // Auto-scroll to bottom
    QTextCursor cursor = m_logOutput->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logOutput->setTextCursor(cursor);
    
    // Limit log size to prevent memory issues (keep last 1000 lines)
    QStringList lines = m_logOutput->toPlainText().split('\n');
    if (lines.size() > 1000) {
        lines = lines.mid(lines.size() - 1000);
        m_logOutput->setPlainText(lines.join('\n'));
        
        // Move cursor to end again
        cursor.movePosition(QTextCursor::End);
        m_logOutput->setTextCursor(cursor);
    }
}

void MainWindow::onClearLog()
{
    if (m_logOutput) {
        m_logOutput->clear();
        appendLog("Log cleared by user", "INFO");
    }
}

void MainWindow::onSaveLog()
{
    if (!m_logOutput) {
        return;
    }
    
    QString fileName = QFileDialog::getSaveFileName(this,
        tr("Save Log File"), 
        QString("log_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
        tr("Text Files (*.txt);;All Files (*)"));
    
    if (!fileName.isEmpty()) {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
            QTextStream out(&file);
            out << m_logOutput->toPlainText();
            file.close();
            
            appendLog(QString("Log saved to: %1").arg(fileName), "INFO");
            QMessageBox::information(this, "Save Log", 
                                   QString("Log file saved successfully:\n%1").arg(fileName));
        } else {
            appendLog(QString("Failed to save log to: %1").arg(fileName), "ERROR");
            QMessageBox::warning(this, "Save Log", 
                                QString("Failed to save log file:\n%1").arg(fileName));
        }
    }
}

void MainWindow::onToggleLogPanel(bool checked)
{
    if (m_workAreaSplitter) {
        QWidget* logWidget = m_workAreaSplitter->widget(1);
        if (logWidget) {
            logWidget->setVisible(checked);
            appendLog(QString("Log panel %1").arg(checked ? "shown" : "hidden"), "INFO");
        }
    }
}

void MainWindow::onModuleWidgetRequested(QWidget* widget, const QString& title)
{
    if (!widget || !m_paramWidget) {
        return;
    }
    
    // Display the module widget in the Parameters area
    m_paramWidget->setModuleWidget(widget, title);
    
    // Update window title to include module name
    QFileInfo fileInfo(m_currentFile);
    QString windowTitle = QString("Optimize Application - %1 - %2")
                        .arg(fileInfo.fileName())
                        .arg(title);
    setWindowTitle(windowTitle);
    
    // Log the operation
    appendLog(QString("Opened module widget in Parameters area: %1").arg(title), "INFO");
    
    // Update status bar
    statusBar()->showMessage(QString("Editing: %1").arg(title), 5000);
}

void MainWindow::showComponentParameters(QTreeWidgetItem* item)
{
    if (!item || !m_paramWidget) {
        return;
    }
    
    QString itemName = item->text(0);
    QString moduleType = item->data(0, Qt::UserRole + 2).toString();
    QVariant componentData = item->data(0, Qt::UserRole + 1);
    
    QString content;
    
    if (moduleType == "pipe" && componentData.canConvert<PipeComponent>()) {
        PipeComponent pipe = componentData.value<PipeComponent>();
        content = QString("Pipe Component Parameters\n")
                + QString("=" ).repeated(50) + "\n\n"
                + QString("Component ID: %1\n").arg(pipe.componentId)
                + QString("Component Name: %1\n").arg(pipe.componentName)
                + QString("Component Type: %1\n\n").arg(pipe.componentType)
                + QString("Basic Information:\n")
                + QString("- Number of Volumes: %1\n").arg(pipe.numberOfVolumes);
        
        if (!pipe.flags.isEmpty()) {
            content += QString("- Flags: ");
            for (int i = 0; i < pipe.flags.size() && i < 5; ++i) {
                content += QString("%1").arg(pipe.flags[i]);
                if (i < pipe.flags.size() - 1 && i < 4) content += ", ";
            }
            if (pipe.flags.size() > 5) {
                content += QString("... (%1 total)").arg(pipe.flags.size());
            }
            content += "\n";
        }
        
        content += QString("\nGeometry Data:\n");
        if (!pipe.volumes.isEmpty()) {
            content += QString("- Volumes: ");
            for (int i = 0; i < pipe.volumes.size() && i < 5; ++i) {
                content += QString("%.3f").arg(pipe.volumes[i]);
                if (i < pipe.volumes.size() - 1 && i < 4) content += ", ";
            }
            if (pipe.volumes.size() > 5) {
                content += QString("... (%1 total)").arg(pipe.volumes.size());
            }
            content += "\n";
        }
        
        if (!pipe.lengths.isEmpty()) {
            content += QString("- Lengths: ");
            for (int i = 0; i < pipe.lengths.size() && i < 5; ++i) {
                content += QString("%.3f").arg(pipe.lengths[i]);
                if (i < pipe.lengths.size() - 1 && i < 4) content += ", ";
            }
            if (pipe.lengths.size() > 5) {
                content += QString("... (%1 total)").arg(pipe.lengths.size());
            }
            content += "\n";
        }
        
        content += QString("\nInitial Conditions:\n");
        for (int i = 0; i < pipe.initialConditions.size() && i < 3; ++i) {
            const auto& ic = pipe.initialConditions[i];
            content += QString("- IC %1: Pressure=%.2f, Temperature=%.2f, Quality=%.3f\n")
                      .arg(i+1).arg(ic.pressure).arg(ic.temperature).arg(ic.quality);
        }
        
        if (pipe.initialConditions.size() > 3) {
            content += QString("- ... and %1 more initial conditions\n").arg(pipe.initialConditions.size() - 3);
        }
    }
    else if (moduleType == "sngljun" && componentData.canConvert<JunctionComponent>()) {
        JunctionComponent junction = componentData.value<JunctionComponent>();
        content = QString("Junction Component Parameters\n")
                + QString("=" ).repeated(50) + "\n\n"
                + QString("Component ID: %1\n").arg(junction.componentId)
                + QString("Component Name: %1\n").arg(junction.componentName)
                + QString("Component Type: %1\n\n").arg(junction.componentType)
                + QString("Connection Information:\n")
                + QString("- From Component: %1\n").arg(junction.fromComponent)
                + QString("- To Component: %1\n").arg(junction.toComponent)
                + QString("- Junction Area: %.6f m²\n").arg(junction.area)
                + QString("- Forward Loss Coefficient: %.3f\n").arg(junction.forwardLoss)
                + QString("- Reverse Loss Coefficient: %.3f\n").arg(junction.reverseLoss)
                + QString("- Flags: %1\n\n").arg(junction.flags)
                + QString("Initial Conditions:\n")
                + QString("- Velocity Flag: %1\n").arg(junction.velocityFlag)
                + QString("- Velocity: %.3f m/s\n").arg(junction.velocity)
                + QString("- Interface Velocity: %.3f m/s\n").arg(junction.interfaceVelocity)
                + QString("- Quality: %.3f\n").arg(junction.quality);
    }
    else if (componentData.canConvert<SingleVolumeComponent>()) {
        SingleVolumeComponent volume = componentData.value<SingleVolumeComponent>();
        content = QString("Volume Component Parameters\n")
                + QString("=" ).repeated(50) + "\n\n"
                + QString("Component ID: %1\n").arg(volume.componentId)
                + QString("Component Name: %1\n").arg(volume.componentName)
                + QString("Component Type: %1\n\n").arg(volume.componentType)
                + QString("Volume Properties:\n")
                + QString("- Volume: %.3f m³\n").arg(volume.volume)
                + QString("- Length: %.3f m\n").arg(volume.length)
                + QString("- Elevation: %.3f m\n").arg(volume.elevation)
                + QString("- Angle: %.3f degrees\n").arg(volume.angle)
                + QString("- Roughness: %.6f\n").arg(volume.roughness)
                + QString("- Hydraulic Diameter: %.6f m\n\n").arg(volume.hydraulicDiameter)
                + QString("Initial Conditions:\n")
                + QString("- Thermodynamic State: %1\n").arg(volume.thermodynamicState)
                + QString("- Pressure: %.2f Pa\n").arg(volume.pressure)
                + QString("- Temperature: %.2f K\n").arg(volume.temperature)
                + QString("- Quality: %.3f\n").arg(volume.quality);
    }
    else if (componentData.canConvert<BranchComponent>()) {
        BranchComponent branch = componentData.value<BranchComponent>();
        content = QString("Branch Component Parameters\n")
                + QString("=" ).repeated(50) + "\n\n"
                + QString("Component ID: %1\n").arg(branch.componentId)
                + QString("Component Name: %1\n").arg(branch.componentName)
                + QString("Component Type: %1\n\n").arg(branch.componentType)
                + QString("Branch Configuration:\n")
                + QString("- Number of Junctions: %1\n").arg(branch.numberOfJunctions)
                + QString("- Number of Volumes: %1\n").arg(branch.numberOfVolumes)
                + QString("- Volume: %.3f m³\n").arg(branch.volume)
                + QString("- Length: %.3f m\n").arg(branch.length)
                + QString("- Elevation: %.3f m\n").arg(branch.elevation)
                + QString("- Angle: %.3f degrees\n\n").arg(branch.angle)
                + QString("Junction Information:\n");
        
        for (int i = 0; i < branch.junctions.size() && i < 5; ++i) {
            const auto& junction = branch.junctions[i];
            content += QString("- Junction %1: From=%2, To=%3, Area=%.6f\n")
                      .arg(i+1).arg(junction.fromComponent).arg(junction.toComponent).arg(junction.area);
        }
        
        if (branch.junctions.size() > 5) {
            content += QString("- ... and %1 more junctions\n").arg(branch.junctions.size() - 5);
        }
    }
    else {
        content = QString("Component Parameters\n")
                + QString("=" ).repeated(50) + "\n\n"
                + QString("Component: %1\n").arg(itemName)
                + QString("Type: %1\n\n").arg(moduleType)
                + QString("No detailed parameter information available for this component type.\n")
                + QString("Double-click the component to open its dedicated editor widget.");
    }
    
    content += QString("\n\nNote: Double-click this component to open its dedicated editor for detailed editing.");
    
    // Create a text widget to display parameters in the Parameters area
    QTextEdit* paramTextWidget = new QTextEdit();
    paramTextWidget->setPlainText(content);
    paramTextWidget->setReadOnly(true);
    paramTextWidget->setStyleSheet("QTextEdit { background-color: #f9f9f9; font-family: 'Consolas', 'Monaco', monospace; font-size: 9pt; }");
    
    // Display in Parameters area
    QString title = QString("Parameters - %1").arg(itemName);
    m_paramWidget->setModuleWidget(paramTextWidget, title);
    
    appendLog(QString("Showing parameters for component: %1 (%2)").arg(itemName, moduleType), "INFO");
}

void MainWindow::showSolverOverview()
{
    if (!m_textEdit) {
        return;
    }
    
    QString content = QString("Solver Overview\n")
            + QString("=" ).repeated(50) + "\n\n"
            + QString("Input File: %1\n\n").arg(m_currentFile.isEmpty() ? "No file loaded" : QFileInfo(m_currentFile).fileName());
    
    if (m_treeWidget) {
        // Count components under Solver node
        QTreeWidgetItem* solverItem = nullptr;
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            QTreeWidgetItem* topItem = m_treeWidget->topLevelItem(i);
            if (m_treeWidget->getNodeType(topItem) == NodeType::Solver) {
                solverItem = topItem;
                break;
            }
        }
        
        if (solverItem) {
            int totalComponents = solverItem->childCount();
            int pipeCount = 0, junctionCount = 0, volumeCount = 0, branchCount = 0, otherCount = 0;
            
            for (int i = 0; i < totalComponents; ++i) {
                QTreeWidgetItem* child = solverItem->child(i);
                QString moduleType = child->data(0, Qt::UserRole + 2).toString();
                
                if (moduleType == "pipe") pipeCount++;
                else if (moduleType == "sngljun") junctionCount++;
                else if (moduleType.contains("vol")) volumeCount++;
                else if (moduleType.contains("branch")) branchCount++;
                else otherCount++;
            }
            
            content += QString("Component Summary:\n")
                    + QString("- Total Components: %1\n").arg(totalComponents)
                    + QString("- Pipe Components: %1\n").arg(pipeCount)
                    + QString("- Junction Components: %1\n").arg(junctionCount)
                    + QString("- Volume Components: %1\n").arg(volumeCount)
                    + QString("- Branch Components: %1\n").arg(branchCount);
            
            if (otherCount > 0) {
                content += QString("- Other Components: %1\n").arg(otherCount);
            }
            
            content += QString("\nComponent List:\n");
            for (int i = 0; i < totalComponents && i < 10; ++i) {
                QTreeWidgetItem* child = solverItem->child(i);
                QString moduleType = child->data(0, Qt::UserRole + 2).toString();
                content += QString("- %1 (%2)\n").arg(child->text(0), moduleType);
            }
            
            if (totalComponents > 10) {
                content += QString("- ... and %1 more components\n").arg(totalComponents - 10);
            }
        } else {
            content += QString("No components loaded.\n");
        }
    }
    
    content += QString("\nInstructions:\n")
            + QString("• Click on any component in the tree to view its parameters\n")
            + QString("• Double-click on any component to open its dedicated editor\n")
            + QString("• Use File > Open to load a different .i input file\n")
            + QString("• Components are automatically parsed and organized by type\n");
    
    m_textEdit->setPlainText(content);
    appendLog("Showing solver overview", "INFO");
}

bool MainWindow::saveProjectToXML(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        LOG_ERROR(QString("Failed to open file for writing: %1").arg(filePath), "MainWindow");
        return false;
    }
    
    QXmlStreamWriter writer(&file);
    writer.setAutoFormatting(true);
    writer.writeStartDocument();
    
    // Write root element
    writer.writeStartElement("OptimizeProject");
    writer.writeAttribute("version", "1.0");
    writer.writeAttribute("name", m_currentProjectName);
    writer.writeAttribute("created", QDateTime::currentDateTime().toString(Qt::ISODate));
    
    // Write project information
    writer.writeStartElement("ProjectInfo");
    writer.writeTextElement("Name", m_currentProjectName);
    writer.writeTextElement("Path", m_currentProjectPath);
    writer.writeTextElement("LastModified", QDateTime::currentDateTime().toString(Qt::ISODate));
    if (!m_inputFilePath.isEmpty()) {
        writer.writeTextElement("InputFilePath", m_inputFilePath);
    }
    writer.writeEndElement(); // ProjectInfo
    
    // Write tree structure
    writer.writeStartElement("TreeStructure");
    if (m_treeWidget) {
        for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
            QTreeWidgetItem* item = m_treeWidget->topLevelItem(i);
            saveTreeNodeToXML(writer, item);
        }
    }
    writer.writeEndElement(); // TreeStructure
    
    // Write parameters
    writer.writeStartElement("Parameters");
    saveParametersToXML(writer);
    writer.writeEndElement(); // Parameters
    
    writer.writeEndElement(); // OptimizeProject
    writer.writeEndDocument();
    
    file.close();
    
    LOG_INFO(QString("Project saved to XML: %1").arg(filePath), "MainWindow");
    return true;
}

void MainWindow::saveTreeNodeToXML(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    writer.writeStartElement("Node");
    writer.writeAttribute("text", item->text(0));
    writer.writeAttribute("type", QString::number(static_cast<int>(m_treeWidget->getNodeType(item))));
    writer.writeAttribute("expanded", item->isExpanded() ? "true" : "false");
    
    // Save node-specific data
    QVariant userData = item->data(0, Qt::UserRole + 1);
    QVariant moduleType = item->data(0, Qt::UserRole + 2);
    
    if (!moduleType.toString().isEmpty()) {
        writer.writeAttribute("moduleType", moduleType.toString());
    }
    
    // Save component data if available
    if (userData.isValid()) {
        writer.writeStartElement("ComponentData");
        
        QString moduleTypeStr = moduleType.toString();
        if (moduleTypeStr == "pipe" && userData.canConvert<PipeComponent>()) {
            PipeComponent pipe = userData.value<PipeComponent>();
            writer.writeTextElement("ComponentID", pipe.componentId);
            writer.writeTextElement("ComponentName", pipe.componentName);
            writer.writeTextElement("ComponentType", pipe.componentType);
            writer.writeTextElement("NumberOfVolumes", QString::number(pipe.numberOfVolumes));
            
            // Save volumes
            if (!pipe.volumes.isEmpty()) {
                writer.writeStartElement("Volumes");
                for (double volume : pipe.volumes) {
                    writer.writeTextElement("Volume", QString::number(volume));
                }
                writer.writeEndElement();
            }
            
            // Save lengths
            if (!pipe.lengths.isEmpty()) {
                writer.writeStartElement("Lengths");
                for (double length : pipe.lengths) {
                    writer.writeTextElement("Length", QString::number(length));
                }
                writer.writeEndElement();
            }
        }
        else if (moduleTypeStr == "sngljun" && userData.canConvert<JunctionComponent>()) {
            JunctionComponent junction = userData.value<JunctionComponent>();
            writer.writeTextElement("ComponentID", junction.componentId);
            writer.writeTextElement("ComponentName", junction.componentName);
            writer.writeTextElement("ComponentType", junction.componentType);
            writer.writeTextElement("FromComponent", junction.fromComponent);
            writer.writeTextElement("ToComponent", junction.toComponent);
            writer.writeTextElement("Area", QString::number(junction.area));
            writer.writeTextElement("ForwardLoss", QString::number(junction.forwardLoss));
            writer.writeTextElement("ReverseLoss", QString::number(junction.reverseLoss));
        }
        
        writer.writeEndElement(); // ComponentData
    }
    
    // Save child nodes
    for (int i = 0; i < item->childCount(); ++i) {
        saveTreeNodeToXML(writer, item->child(i));
    }
    
    writer.writeEndElement(); // Node
}

void MainWindow::saveParametersToXML(QXmlStreamWriter& writer)
{
    if (!m_treeWidget || !m_paramWidget) return;
    
    // Save parameters for each node in the tree
    writer.writeStartElement("NodeParameters");
    
    // Iterate through all top-level items
    for (int i = 0; i < m_treeWidget->topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = m_treeWidget->topLevelItem(i);
        saveNodeParametersToXML(writer, item);
    }
    
    writer.writeEndElement(); // NodeParameters
}

void MainWindow::saveNodeParametersToXML(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    NodeType nodeType = m_treeWidget->getNodeType(item);
    QString nodeName = item->text(0);
    
    // Save parameters for analysis nodes and input/output nodes
    if (nodeType == NodeType::Optimize || nodeType == NodeType::Sensitivity || nodeType == NodeType::UQ || 
        nodeType == NodeType::Input || nodeType == NodeType::Output) {
        writer.writeStartElement("Node");
        writer.writeAttribute("name", nodeName);
        writer.writeAttribute("type", QString::number(static_cast<int>(nodeType)));
        
        // Get parameters from the node's stored data or current widget if this is the selected node
        QTreeWidgetItem* currentItem = m_treeWidget->currentItem();
        bool isCurrentNode = (item == currentItem);
        
        if (nodeType == NodeType::Optimize) {
            writer.writeStartElement("OptimizationParameters");
            if (isCurrentNode && m_paramWidget->getOptimizeWidget()) {
                // Save current widget parameters and store them in the node
                auto* optWidget = m_paramWidget->getOptimizeWidget();
                saveOptimizationParametersToNode(item, optWidget);
            }
            // Load parameters from node data
            loadOptimizationParametersFromNode(writer, item);
            writer.writeEndElement(); // OptimizationParameters
        }
        else if (nodeType == NodeType::Sensitivity) {
            writer.writeStartElement("SensitivityParameters");
            if (isCurrentNode && m_paramWidget->getSensitivityWidget()) {
                // Save current widget parameters and store them in the node
                auto* sensWidget = m_paramWidget->getSensitivityWidget();
                saveSensitivityParametersToNode(item, sensWidget);
            }
            // Load parameters from node data
            loadSensitivityParametersFromNode(writer, item);
            writer.writeEndElement(); // SensitivityParameters
        }
        else if (nodeType == NodeType::UQ) {
            writer.writeStartElement("UQParameters");
            if (isCurrentNode && m_paramWidget->getUQWidget()) {
                // Save current widget parameters and store them in the node
                auto* uqWidget = m_paramWidget->getUQWidget();
                saveUQParametersToNode(item, uqWidget);
            }
            // Load parameters from node data
            loadUQParametersFromNode(writer, item);
            writer.writeEndElement(); // UQParameters
        }
        else if (nodeType == NodeType::Input) {
            writer.writeStartElement("InputParameters");
            if (isCurrentNode && m_paramWidget->getInputWidget()) {
                // Save current widget parameters and store them in the node
                auto* inputWidget = m_paramWidget->getInputWidget();
                saveInputParametersToNode(item, inputWidget);
            }
            // Load parameters from node data
            loadInputParametersFromNode(writer, item);
            writer.writeEndElement(); // InputParameters
        }
        else if (nodeType == NodeType::Output) {
            writer.writeStartElement("OutputParameters");
            if (isCurrentNode && m_paramWidget->getOutputWidget()) {
                // Save current widget parameters and store them in the node
                auto* outputWidget = m_paramWidget->getOutputWidget();
                saveOutputParametersToNode(item, outputWidget);
            }
            // Load parameters from node data
            loadOutputParametersFromNode(writer, item);
            writer.writeEndElement(); // OutputParameters
        }
        
        writer.writeEndElement(); // Node
    }
    
    // Recursively save child nodes
    for (int i = 0; i < item->childCount(); ++i) {
        saveNodeParametersToXML(writer, item->child(i));
    }
}

void MainWindow::adjustFontSizesForDPI()
{
    QScreen* screen = QApplication::primaryScreen();
        if (!screen) return;

        qreal dpi = screen->logicalDotsPerInch();
        qreal scaleFactor = dpi / 96.0; // 96 DPI is standard

        // Adjust application font
        QFont appFont = QApplication::font();
        int baseFontSize = 9; // Base font size
        appFont.setPointSize(qMax(8, static_cast<int>(baseFontSize * scaleFactor)));
        QApplication::setFont(appFont);

        // Adjust log output font specifically
        if (m_logOutput) {
            QFont logFont("Consolas", qMax(8, static_cast<int>(9 * scaleFactor)));
            m_logOutput->setFont(logFont);
        }
}

bool MainWindow::loadProjectFromXML(const QString& filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        LOG_ERROR(QString("Failed to open file for reading: %1").arg(filePath), "MainWindow");
        return false;
    }

    QXmlStreamReader reader(&file);
    if (reader.readNextStartElement() && reader.name() == "OptimizeProject") {
        // Read project information
        if (reader.readNextStartElement() && reader.name() == "ProjectInfo") {
            while (reader.readNextStartElement()) {
                QString name = reader.name().toString();
                if (name == "Name") {
                    m_currentProjectName = reader.readElementText();
                } else if (name == "Path") {
                    m_currentProjectPath = reader.readElementText();
                } else if (name == "LastModified") {
                    m_lastModified = QDateTime::fromString(reader.readElementText(), Qt::ISODate);
                } else if (name == "InputFilePath") {
                    m_inputFilePath = reader.readElementText();
                }
            }
            reader.skipCurrentElement();
        }

        // Read tree structure
        if (reader.readNextStartElement() && reader.name() == "TreeStructure") {
            m_treeWidget->clear();
            while (reader.readNextStartElement() && reader.name() == "Node") {
                loadTreeNodeFromXML(reader, m_treeWidget);
            }
            reader.skipCurrentElement();
        }

        // Read parameters
        if (reader.readNextStartElement() && reader.name() == "Parameters") {
            while (reader.readNextStartElement()) {
                QString nodeName = reader.name().toString();
                // Skip parameter loading for now - this needs proper implementation
                reader.skipCurrentElement();
            }
            reader.skipCurrentElement();
        }
    }

    file.close();
    m_currentFile = filePath;
    m_isModified = false;

    // Adjust font sizes for high DPI displays
    adjustFontSizesForDPI();

    return true;
}

QTreeWidgetItem* MainWindow::loadTreeNodeFromXML(QXmlStreamReader& reader, QTreeWidgetItem* parent)
{
    QString text = reader.attributes().value("text").toString();
    int typeInt = reader.attributes().value("type").toInt();
    bool expanded = reader.attributes().value("expanded").toString() == "true";
    QString moduleType = reader.attributes().value("moduleType").toString();
    
    QTreeWidgetItem* item;
    if (parent) {
        item = new QTreeWidgetItem(parent);
    } else {
        item = new QTreeWidgetItem(m_treeWidget);
    }
    
    item->setText(0, text);
    item->setExpanded(expanded);
    
    // Set node type
    NodeType nodeType = static_cast<NodeType>(typeInt);
    m_treeWidget->setNodeType(item, nodeType);
    item->setIcon(0, m_treeWidget->getNodeIcon(nodeType));
    
    // Set module type if available
    if (!moduleType.isEmpty()) {
        item->setData(0, Qt::UserRole + 2, moduleType);
    }
    
    // Update member pointers for special nodes
    if (nodeType == NodeType::Solver) {
        m_treeWidget->setSolverItem(item);
    } else if (nodeType == NodeType::Optimize) {
        m_treeWidget->setOptimizeItem(item);
    } else if (nodeType == NodeType::Sensitivity) {
        m_treeWidget->setSensitivityItem(item);
    } else if (nodeType == NodeType::UQ) {
        m_treeWidget->setUQItem(item);
    } else if (nodeType == NodeType::Input && parent && m_treeWidget->getNodeType(parent) == NodeType::Solver) {
        m_treeWidget->setInputItem(item);
    } else if (nodeType == NodeType::Output && parent && m_treeWidget->getNodeType(parent) == NodeType::Solver) {
        m_treeWidget->setOutputItem(item);
    }
    
    while (reader.readNextStartElement()) {
        if (reader.name() == "ComponentData") {
            // Load component data - simplified for now
            reader.skipCurrentElement();
        } else if (reader.name() == "Node") {
            loadTreeNodeFromXML(reader, item);
        } else {
            reader.skipCurrentElement();
        }
    }
    
    return item;
}

void MainWindow::loadParametersFromXML(QXmlStreamReader& reader)
{
    while (reader.readNextStartElement()) {
        if (reader.name() == "NodeParameters") {
            // Load parameters for each node
            while (reader.readNextStartElement()) {
                if (reader.name() == "Node") {
                    loadNodeParametersFromXML(reader);
                } else {
                    reader.skipCurrentElement();
                }
            }
        } else {
            reader.skipCurrentElement();
        }
    }
}

void MainWindow::loadNodeParametersFromXML(QXmlStreamReader& reader)
{
    QString nodeName = reader.attributes().value("name").toString();
    int nodeTypeInt = reader.attributes().value("type").toInt();
    NodeType nodeType = static_cast<NodeType>(nodeTypeInt);
    
    // Find the corresponding tree node
    QTreeWidgetItem* targetNode = findTreeNodeByNameAndType(nodeName, nodeType);
    if (!targetNode) {
        reader.skipCurrentElement();
        return;
    }
    
    while (reader.readNextStartElement()) {
        if (reader.name() == "OptimizationParameters") {
            loadOptimizationParametersFromXML(reader, targetNode);
        } else if (reader.name() == "SensitivityParameters") {
            loadSensitivityParametersFromXML(reader, targetNode);
        } else if (reader.name() == "UQParameters") {
            loadUQParametersFromXML(reader, targetNode);
        } else if (reader.name() == "InputParameters") {
            loadInputParametersFromXML(reader, targetNode);
        } else if (reader.name() == "OutputParameters") {
            loadOutputParametersFromXML(reader, targetNode);
        } else {
            reader.skipCurrentElement();
        }
    }
}

QTreeWidgetItem* MainWindow::findTreeNodeByNameAndType(const QString& name, NodeType type)
{
    if (!m_treeWidget) return nullptr;
    
    // Search through all items in the tree
    QTreeWidgetItemIterator it(m_treeWidget);
    while (*it) {
        QTreeWidgetItem* item = *it;
        if (item->text(0) == name && m_treeWidget->getNodeType(item) == type) {
            return item;
        }
        ++it;
    }
    return nullptr;
}

void MainWindow::loadOptimizationParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item)
{
    if (!item) {
        reader.skipCurrentElement();
        return;
    }
    
    while (reader.readNextStartElement()) {
        QString elementName = reader.name().toString();
        QString value = reader.readElementText();
        
        // Store parameters in the tree item's data
        if (elementName == "Method") {
            item->setData(0, Qt::UserRole + 10, value);
        } else if (elementName == "MaxIterations") {
            item->setData(0, Qt::UserRole + 11, value.toInt());
        } else if (elementName == "MaxFunctionEvaluations") {
            item->setData(0, Qt::UserRole + 12, value.toInt());
        } else if (elementName == "ConvergenceTolerance") {
            item->setData(0, Qt::UserRole + 13, value.toDouble());
        } else if (elementName == "PopulationSize") {
            item->setData(0, Qt::UserRole + 14, value.toInt());
        } else if (elementName == "MutationRate") {
            item->setData(0, Qt::UserRole + 15, value.toDouble());
        } else if (elementName == "CrossoverRate") {
            item->setData(0, Qt::UserRole + 16, value.toDouble());
        } else if (elementName == "ReplacementType") {
            item->setData(0, Qt::UserRole + 17, value);
        } else if (elementName == "MultiObjectiveWeightSets") {
            item->setData(0, Qt::UserRole + 18, value.toInt());
        } else if (elementName == "RandomWeightSetsEnabled") {
            item->setData(0, Qt::UserRole + 19, value == "true");
        } else if (elementName == "GradientTolerance") {
            item->setData(0, Qt::UserRole + 20, value.toDouble());
        } else if (elementName == "MaxStepSize") {
            item->setData(0, Qt::UserRole + 21, value.toDouble());
        } else if (elementName == "SpeculativeGradientEnabled") {
            item->setData(0, Qt::UserRole + 22, value == "true");
        } else if (elementName == "VerboseOutputEnabled") {
            item->setData(0, Qt::UserRole + 23, value == "true");
        } else if (elementName == "DebugOutputEnabled") {
            item->setData(0, Qt::UserRole + 24, value == "true");
        } else if (elementName == "QuietModeEnabled") {
            item->setData(0, Qt::UserRole + 25, value == "true");
        }
    }
}

void MainWindow::loadSensitivityParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item)
{
    if (!item) {
        reader.skipCurrentElement();
        return;
    }
    
    while (reader.readNextStartElement()) {
        QString elementName = reader.name().toString();
        QString value = reader.readElementText();
        
        // Store parameters in the tree item's data
        if (elementName == "Method") {
            item->setData(0, Qt::UserRole + 30, value);
        } else if (elementName == "Samples") {
            item->setData(0, Qt::UserRole + 31, value.toInt());
        } else if (elementName == "SamplingType") {
            item->setData(0, Qt::UserRole + 32, value);
        } else if (elementName == "VarianceBasedDecomp") {
            item->setData(0, Qt::UserRole + 33, value == "true");
        } else if (elementName == "ConvergenceTolerance") {
            item->setData(0, Qt::UserRole + 34, value.toDouble());
        } else if (elementName == "Seed") {
            item->setData(0, Qt::UserRole + 35, value.toInt());
        }
    }
}

void MainWindow::loadUQParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item)
{
    if (!item) {
        reader.skipCurrentElement();
        return;
    }
    
    while (reader.readNextStartElement()) {
        QString elementName = reader.name().toString();
        QString value = reader.readElementText();
        
        // Store parameters in the tree item's data
        if (elementName == "Method") {
            item->setData(0, Qt::UserRole + 40, value);
        } else if (elementName == "Samples") {
            item->setData(0, Qt::UserRole + 41, value.toInt());
        } else if (elementName == "SamplingType") {
            item->setData(0, Qt::UserRole + 42, value);
        } else if (elementName == "PolynomialOrder") {
            item->setData(0, Qt::UserRole + 43, value.toInt());
        } else if (elementName == "ExpansionType") {
            item->setData(0, Qt::UserRole + 44, value);
        } else if (elementName == "ConvergenceTolerance") {
            item->setData(0, Qt::UserRole + 45, value.toDouble());
        } else if (elementName == "Seed") {
            item->setData(0, Qt::UserRole + 46, value.toInt());
        } else if (elementName == "AdaptiveRefinement") {
            item->setData(0, Qt::UserRole + 47, value == "true");
        }
    }
} 

// Node parameter management methods
void MainWindow::saveOptimizationParametersToNode(QTreeWidgetItem* item, OptimizeWidget* widget)
{
    if (!item || !widget) return;
    
    // Store optimization parameters in the tree item's data
    item->setData(0, Qt::UserRole + 10, widget->getDakotaMethod());
    item->setData(0, Qt::UserRole + 11, widget->getMaxIterations());
    item->setData(0, Qt::UserRole + 12, widget->getMaxFunctionEvaluations());
    item->setData(0, Qt::UserRole + 13, widget->getConvergenceTolerance());
    item->setData(0, Qt::UserRole + 14, widget->getPopulationSize());
    item->setData(0, Qt::UserRole + 15, widget->getMutationRate());
    item->setData(0, Qt::UserRole + 16, widget->getCrossoverRate());
    item->setData(0, Qt::UserRole + 17, widget->getReplacementType());
    item->setData(0, Qt::UserRole + 18, widget->getMultiObjectiveWeightSets());
    item->setData(0, Qt::UserRole + 19, widget->isRandomWeightSetsEnabled());
    item->setData(0, Qt::UserRole + 20, widget->getGradientTolerance());
    item->setData(0, Qt::UserRole + 21, widget->getMaxStepSize());
    item->setData(0, Qt::UserRole + 22, widget->isSpeculativeGradientEnabled());
    item->setData(0, Qt::UserRole + 23, widget->isVerboseOutputEnabled());
    item->setData(0, Qt::UserRole + 24, widget->isDebugOutputEnabled());
    item->setData(0, Qt::UserRole + 25, widget->isQuietModeEnabled());
}

void MainWindow::saveSensitivityParametersToNode(QTreeWidgetItem* item, SensitivityWidget* widget)
{
    if (!item || !widget) return;
    
    // Store sensitivity parameters in the tree item's data
    item->setData(0, Qt::UserRole + 30, widget->getMethod());
    item->setData(0, Qt::UserRole + 31, widget->getSamples());
    item->setData(0, Qt::UserRole + 32, widget->getSamplingType());
    item->setData(0, Qt::UserRole + 33, widget->isVarianceBasedDecomp());
    item->setData(0, Qt::UserRole + 34, widget->getConvergenceTolerance());
    item->setData(0, Qt::UserRole + 35, widget->getSeed());
}

void MainWindow::saveUQParametersToNode(QTreeWidgetItem* item, UQWidget* widget)
{
    if (!item || !widget) return;
    
    // Store UQ parameters in the tree item's data
    item->setData(0, Qt::UserRole + 40, widget->getMethod());
    item->setData(0, Qt::UserRole + 41, widget->getSamples());
    item->setData(0, Qt::UserRole + 42, widget->getSamplingType());
    item->setData(0, Qt::UserRole + 43, widget->getPolynomialOrder());
    item->setData(0, Qt::UserRole + 44, widget->getExpansionType());
    item->setData(0, Qt::UserRole + 45, widget->getConvergenceTolerance());
    item->setData(0, Qt::UserRole + 46, widget->getSeed());
    item->setData(0, Qt::UserRole + 47, widget->isAdaptiveRefinement());
}

void MainWindow::loadOptimizationParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    // Write optimization parameters from node data to XML
    QVariant method = item->data(0, Qt::UserRole + 10);
    if (method.isValid()) {
        writer.writeTextElement("Method", method.toString());
        writer.writeTextElement("MaxIterations", item->data(0, Qt::UserRole + 11).toString());
        writer.writeTextElement("MaxFunctionEvaluations", item->data(0, Qt::UserRole + 12).toString());
        writer.writeTextElement("ConvergenceTolerance", item->data(0, Qt::UserRole + 13).toString());
        writer.writeTextElement("PopulationSize", item->data(0, Qt::UserRole + 14).toString());
        writer.writeTextElement("MutationRate", item->data(0, Qt::UserRole + 15).toString());
        writer.writeTextElement("CrossoverRate", item->data(0, Qt::UserRole + 16).toString());
        writer.writeTextElement("ReplacementType", item->data(0, Qt::UserRole + 17).toString());
        writer.writeTextElement("MultiObjectiveWeightSets", item->data(0, Qt::UserRole + 18).toString());
        writer.writeTextElement("RandomWeightSetsEnabled", item->data(0, Qt::UserRole + 19).toBool() ? "true" : "false");
        writer.writeTextElement("GradientTolerance", item->data(0, Qt::UserRole + 20).toString());
        writer.writeTextElement("MaxStepSize", item->data(0, Qt::UserRole + 21).toString());
        writer.writeTextElement("SpeculativeGradientEnabled", item->data(0, Qt::UserRole + 22).toBool() ? "true" : "false");
        writer.writeTextElement("VerboseOutputEnabled", item->data(0, Qt::UserRole + 23).toBool() ? "true" : "false");
        writer.writeTextElement("DebugOutputEnabled", item->data(0, Qt::UserRole + 24).toBool() ? "true" : "false");
        writer.writeTextElement("QuietModeEnabled", item->data(0, Qt::UserRole + 25).toBool() ? "true" : "false");
    }
}

void MainWindow::loadSensitivityParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    // Write sensitivity parameters from node data to XML
    QVariant method = item->data(0, Qt::UserRole + 30);
    if (method.isValid()) {
        writer.writeTextElement("Method", method.toString());
        writer.writeTextElement("Samples", item->data(0, Qt::UserRole + 31).toString());
        writer.writeTextElement("SamplingType", item->data(0, Qt::UserRole + 32).toString());
        writer.writeTextElement("VarianceBasedDecomp", item->data(0, Qt::UserRole + 33).toBool() ? "true" : "false");
        writer.writeTextElement("ConvergenceTolerance", item->data(0, Qt::UserRole + 34).toString());
        writer.writeTextElement("Seed", item->data(0, Qt::UserRole + 35).toString());
    }
}

void MainWindow::loadUQParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    // Write UQ parameters from node data to XML
    QVariant method = item->data(0, Qt::UserRole + 40);
    if (method.isValid()) {
        writer.writeTextElement("Method", method.toString());
        writer.writeTextElement("Samples", item->data(0, Qt::UserRole + 41).toString());
        writer.writeTextElement("SamplingType", item->data(0, Qt::UserRole + 42).toString());
        writer.writeTextElement("PolynomialOrder", item->data(0, Qt::UserRole + 43).toString());
        writer.writeTextElement("ExpansionType", item->data(0, Qt::UserRole + 44).toString());
        writer.writeTextElement("ConvergenceTolerance", item->data(0, Qt::UserRole + 45).toString());
        writer.writeTextElement("Seed", item->data(0, Qt::UserRole + 46).toString());
        writer.writeTextElement("AdaptiveRefinement", item->data(0, Qt::UserRole + 47).toBool() ? "true" : "false");
    }
}

void MainWindow::loadOptimizationParametersToWidget(QTreeWidgetItem* item, OptimizeWidget* widget)
{
    if (!item || !widget) return;
    
    // Load optimization parameters from node data to widget
    QVariant method = item->data(0, Qt::UserRole + 10);
    if (method.isValid()) {
        widget->setDakotaMethod(method.toString());
        widget->setMaxIterations(item->data(0, Qt::UserRole + 11).toInt());
        widget->setMaxFunctionEvaluations(item->data(0, Qt::UserRole + 12).toInt());
        widget->setConvergenceTolerance(item->data(0, Qt::UserRole + 13).toDouble());
        widget->setPopulationSize(item->data(0, Qt::UserRole + 14).toInt());
        widget->setMutationRate(item->data(0, Qt::UserRole + 15).toDouble());
        widget->setCrossoverRate(item->data(0, Qt::UserRole + 16).toDouble());
        widget->setReplacementType(item->data(0, Qt::UserRole + 17).toString());
        widget->setMultiObjectiveWeightSets(item->data(0, Qt::UserRole + 18).toInt());
        widget->setRandomWeightSetsEnabled(item->data(0, Qt::UserRole + 19).toBool());
        widget->setGradientTolerance(item->data(0, Qt::UserRole + 20).toDouble());
        widget->setMaxStepSize(item->data(0, Qt::UserRole + 21).toDouble());
        widget->setSpeculativeGradientEnabled(item->data(0, Qt::UserRole + 22).toBool());
        widget->setVerboseOutputEnabled(item->data(0, Qt::UserRole + 23).toBool());
        widget->setDebugOutputEnabled(item->data(0, Qt::UserRole + 24).toBool());
        widget->setQuietModeEnabled(item->data(0, Qt::UserRole + 25).toBool());
    }
}

void MainWindow::loadSensitivityParametersToWidget(QTreeWidgetItem* item, SensitivityWidget* widget)
{
    if (!item || !widget) return;
    
    // Load sensitivity parameters from node data to widget
    QVariant method = item->data(0, Qt::UserRole + 30);
    if (method.isValid()) {
        widget->setMethod(method.toString());
        widget->setSamples(item->data(0, Qt::UserRole + 31).toInt());
        widget->setSamplingType(item->data(0, Qt::UserRole + 32).toString());
        widget->setVarianceBasedDecomp(item->data(0, Qt::UserRole + 33).toBool());
        widget->setConvergenceTolerance(item->data(0, Qt::UserRole + 34).toDouble());
        widget->setSeed(item->data(0, Qt::UserRole + 35).toInt());
    }
}

void MainWindow::loadUQParametersToWidget(QTreeWidgetItem* item, UQWidget* widget)
{
    if (!item || !widget) return;
    
    // Load UQ parameters from node data to widget
    QVariant method = item->data(0, Qt::UserRole + 40);
    if (method.isValid()) {
        widget->setMethod(method.toString());
        widget->setSamples(item->data(0, Qt::UserRole + 41).toInt());
        widget->setSamplingType(item->data(0, Qt::UserRole + 42).toString());
        widget->setPolynomialOrder(item->data(0, Qt::UserRole + 43).toInt());
        widget->setExpansionType(item->data(0, Qt::UserRole + 44).toString());
        widget->setConvergenceTolerance(item->data(0, Qt::UserRole + 45).toDouble());
        widget->setSeed(item->data(0, Qt::UserRole + 46).toInt());
        widget->setAdaptiveRefinement(item->data(0, Qt::UserRole + 47).toBool());
    }
}

// Input parameter management methods
void MainWindow::saveInputParametersToNode(QTreeWidgetItem* item, InputWidget* widget)
{
    if (!item || !widget) return;
    
    // Save input parameters to node data (Qt::UserRole + 50-59)
    QList<ParamInfo> paramsList = widget->getParamsList();
    
    // Convert parameters list to QVariant for storage
    QVariantList paramsVariantList;
    for (const auto& param : paramsList) {
        QVariantMap paramMap;
        paramMap["name"] = param.paramName;
        paramMap["init"] = param.paramInit;
        paramMap["upper"] = param.paramUpper;
        paramMap["lower"] = param.paramLower;
        paramsVariantList.append(paramMap);
    }
    
    item->setData(0, Qt::UserRole + 50, paramsVariantList);
    item->setData(0, Qt::UserRole + 51, widget->getParameterCount());
}

void MainWindow::loadInputParametersToWidget(QTreeWidgetItem* item, InputWidget* widget)
{
    if (!item || !widget) return;
    
    // Load input parameters from node data (Qt::UserRole + 50-59)
    QVariantList paramsVariantList = item->data(0, Qt::UserRole + 50).toList();
    
    if (!paramsVariantList.isEmpty()) {
        QList<ParamInfo> paramsList;
        for (const auto& paramVariant : paramsVariantList) {
            QVariantMap paramMap = paramVariant.toMap();
            ParamInfo param;
            param.paramName = paramMap["name"].toString();
            param.paramInit = paramMap["init"].toString();
            param.paramUpper = paramMap["upper"].toString();
            param.paramLower = paramMap["lower"].toString();
            paramsList.append(param);
        }
        widget->setParamsList(paramsList);
    }
}

void MainWindow::loadInputParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    // Write input parameters from node data
    QVariantList paramsVariantList = item->data(0, Qt::UserRole + 50).toList();
    
    writer.writeStartElement("ParametersList");
    for (const auto& paramVariant : paramsVariantList) {
        QVariantMap paramMap = paramVariant.toMap();
        writer.writeStartElement("Parameter");
        writer.writeTextElement("Name", paramMap["name"].toString());
        writer.writeTextElement("InitialValue", paramMap["init"].toString());
        writer.writeTextElement("UpperBound", paramMap["upper"].toString());
        writer.writeTextElement("LowerBound", paramMap["lower"].toString());
        writer.writeEndElement(); // Parameter
    }
    writer.writeEndElement(); // ParametersList
    
    writer.writeTextElement("ParameterCount", QString::number(item->data(0, Qt::UserRole + 51).toInt()));
}

// Output parameter management methods
void MainWindow::saveOutputParametersToNode(QTreeWidgetItem* item, OutputWidget* widget)
{
    if (!item || !widget) return;
    
    // Save output parameters to node data (Qt::UserRole + 60-79)
    item->setData(0, Qt::UserRole + 60, widget->getOutputDirectory());
    item->setData(0, Qt::UserRole + 61, widget->getFileNamePattern());
    item->setData(0, Qt::UserRole + 62, widget->getOutputFormat());
    item->setData(0, Qt::UserRole + 63, widget->isAutoSaveEnabled());
    item->setData(0, Qt::UserRole + 64, widget->getAutoSaveInterval());
    item->setData(0, Qt::UserRole + 65, widget->isCompressionEnabled());
    item->setData(0, Qt::UserRole + 66, widget->getCompressionLevel());
    item->setData(0, Qt::UserRole + 67, widget->isReportEnabled());
    item->setData(0, Qt::UserRole + 68, widget->getReportFormat());
    item->setData(0, Qt::UserRole + 69, widget->includeCharts());
    item->setData(0, Qt::UserRole + 70, widget->includeStatistics());
}

void MainWindow::loadOutputParametersToWidget(QTreeWidgetItem* item, OutputWidget* widget)
{
    if (!item || !widget) return;
    
    // Load output parameters from node data (Qt::UserRole + 60-79)
    QString outputDir = item->data(0, Qt::UserRole + 60).toString();
    if (!outputDir.isEmpty()) {
        widget->setOutputDirectory(outputDir);
    }
    
    QString filePattern = item->data(0, Qt::UserRole + 61).toString();
    if (!filePattern.isEmpty()) {
        widget->setFileNamePattern(filePattern);
    }
    
    QString outputFormat = item->data(0, Qt::UserRole + 62).toString();
    if (!outputFormat.isEmpty()) {
        widget->setOutputFormat(outputFormat);
    }
    
    widget->setAutoSaveEnabled(item->data(0, Qt::UserRole + 63).toBool());
    widget->setAutoSaveInterval(item->data(0, Qt::UserRole + 64).toInt());
    widget->setCompressionEnabled(item->data(0, Qt::UserRole + 65).toBool());
    
    QString compressionLevel = item->data(0, Qt::UserRole + 66).toString();
    if (!compressionLevel.isEmpty()) {
        widget->setCompressionLevel(compressionLevel);
    }
    
    widget->setReportEnabled(item->data(0, Qt::UserRole + 67).toBool());
    
    QString reportFormat = item->data(0, Qt::UserRole + 68).toString();
    if (!reportFormat.isEmpty()) {
        widget->setReportFormat(reportFormat);
    }
    
    widget->setIncludeCharts(item->data(0, Qt::UserRole + 69).toBool());
    widget->setIncludeStatistics(item->data(0, Qt::UserRole + 70).toBool());
}

void MainWindow::loadOutputParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item)
{
    if (!item) return;
    
    // Write output parameters from node data
    writer.writeTextElement("OutputDirectory", item->data(0, Qt::UserRole + 60).toString());
    writer.writeTextElement("FileNamePattern", item->data(0, Qt::UserRole + 61).toString());
    writer.writeTextElement("OutputFormat", item->data(0, Qt::UserRole + 62).toString());
    writer.writeTextElement("AutoSaveEnabled", item->data(0, Qt::UserRole + 63).toBool() ? "true" : "false");
    writer.writeTextElement("AutoSaveInterval", QString::number(item->data(0, Qt::UserRole + 64).toInt()));
    writer.writeTextElement("CompressionEnabled", item->data(0, Qt::UserRole + 65).toBool() ? "true" : "false");
    writer.writeTextElement("CompressionLevel", item->data(0, Qt::UserRole + 66).toString());
    writer.writeTextElement("ReportEnabled", item->data(0, Qt::UserRole + 67).toBool() ? "true" : "false");
    writer.writeTextElement("ReportFormat", item->data(0, Qt::UserRole + 68).toString());
    writer.writeTextElement("IncludeCharts", item->data(0, Qt::UserRole + 69).toBool() ? "true" : "false");
    writer.writeTextElement("IncludeStatistics", item->data(0, Qt::UserRole + 70).toBool() ? "true" : "false");
}

// Input parameter XML loading helper
void MainWindow::loadInputParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item)
{
    if (!item) {
        reader.skipCurrentElement();
        return;
    }
    
    QVariantList paramsVariantList;
    int parameterCount = 0;
    
    while (reader.readNextStartElement()) {
        QString elementName = reader.name().toString();
        
        if (elementName == "ParametersList") {
            while (reader.readNextStartElement()) {
                if (reader.name().toString() == "Parameter") {
                    QVariantMap paramMap;
                    while (reader.readNextStartElement()) {
                        QString paramElementName = reader.name().toString();
                        QString value = reader.readElementText();
                        
                        if (paramElementName == "Name") {
                            paramMap["name"] = value;
                        } else if (paramElementName == "InitialValue") {
                            paramMap["init"] = value;
                        } else if (paramElementName == "UpperBound") {
                            paramMap["upper"] = value;
                        } else if (paramElementName == "LowerBound") {
                            paramMap["lower"] = value;
                        }
                    }
                    paramsVariantList.append(paramMap);
                } else {
                    reader.skipCurrentElement();
                }
            }
        } else if (elementName == "ParameterCount") {
            parameterCount = reader.readElementText().toInt();
        } else {
            reader.skipCurrentElement();
        }
    }
    
    // Store parameters in the tree item's data
    item->setData(0, Qt::UserRole + 50, paramsVariantList);
    item->setData(0, Qt::UserRole + 51, parameterCount);
}

// Output parameter XML loading helper
void MainWindow::loadOutputParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item)
{
    if (!item) {
        reader.skipCurrentElement();
        return;
    }
    
    while (reader.readNextStartElement()) {
        QString elementName = reader.name().toString();
        QString value = reader.readElementText();
        
        // Store parameters in the tree item's data
        if (elementName == "OutputDirectory") {
            item->setData(0, Qt::UserRole + 60, value);
        } else if (elementName == "FileNamePattern") {
            item->setData(0, Qt::UserRole + 61, value);
        } else if (elementName == "OutputFormat") {
            item->setData(0, Qt::UserRole + 62, value);
        } else if (elementName == "AutoSaveEnabled") {
            item->setData(0, Qt::UserRole + 63, value == "true");
        } else if (elementName == "AutoSaveInterval") {
            item->setData(0, Qt::UserRole + 64, value.toInt());
        } else if (elementName == "CompressionEnabled") {
            item->setData(0, Qt::UserRole + 65, value == "true");
        } else if (elementName == "CompressionLevel") {
            item->setData(0, Qt::UserRole + 66, value);
        } else if (elementName == "ReportEnabled") {
            item->setData(0, Qt::UserRole + 67, value == "true");
        } else if (elementName == "ReportFormat") {
            item->setData(0, Qt::UserRole + 68, value);
        } else if (elementName == "IncludeCharts") {
            item->setData(0, Qt::UserRole + 69, value == "true");
        } else if (elementName == "IncludeStatistics") {
            item->setData(0, Qt::UserRole + 70, value == "true");
        }
    }
}

void MainWindow::updateActionStates()
{
    // Check if we have a current project
    bool projectOpen = !m_currentProjectName.isEmpty() && !m_currentProjectPath.isEmpty();
    
    // Enable/disable Open and Save buttons based on project state
    if (m_openFileAction) {
        m_openFileAction->setEnabled(projectOpen);
    }
    
    if (m_saveFileAction) {
        m_saveFileAction->setEnabled(projectOpen);
    }
}

// Solver Output parameter management methods
void MainWindow::saveSolverOutputParametersToNode(QTreeWidgetItem* item, SolverOutputWidget* widget)
{
    if (!item || !widget) return;
    
    // Save solver output parameters to node data (Qt::UserRole + 80-99)
    item->setData(0, Qt::UserRole + 80, widget->getOutputDirectory());
    item->setData(0, Qt::UserRole + 81, widget->getFileNamePattern());
    item->setData(0, Qt::UserRole + 82, widget->getOutputFormat());
    item->setData(0, Qt::UserRole + 83, widget->isAutoSaveEnabled());
    item->setData(0, Qt::UserRole + 84, widget->getAutoSaveInterval());
    item->setData(0, Qt::UserRole + 85, widget->isCompressionEnabled());
    item->setData(0, Qt::UserRole + 86, widget->getCompressionLevel());
    item->setData(0, Qt::UserRole + 87, widget->isReportEnabled());
    item->setData(0, Qt::UserRole + 88, widget->getReportFormat());
    item->setData(0, Qt::UserRole + 89, widget->includeCharts());
    item->setData(0, Qt::UserRole + 90, widget->includeStatistics());
    
    // Save output variables list
    QStringList variableNames = widget->getOutputVariableNames();
    item->setData(0, Qt::UserRole + 91, variableNames);
    item->setData(0, Qt::UserRole + 92, widget->getOutputVariableCount());
}

void MainWindow::loadSolverOutputParametersToWidget(QTreeWidgetItem* item, SolverOutputWidget* widget)
{
    if (!item || !widget) return;
    
    // Load solver output parameters from node data (Qt::UserRole + 80-99)
    QString outputDir = item->data(0, Qt::UserRole + 80).toString();
    if (!outputDir.isEmpty()) {
        widget->setOutputDirectory(outputDir);
    }
    
    QString filePattern = item->data(0, Qt::UserRole + 81).toString();
    if (!filePattern.isEmpty()) {
        widget->setFileNamePattern(filePattern);
    }
    
    QString outputFormat = item->data(0, Qt::UserRole + 82).toString();
    if (!outputFormat.isEmpty()) {
        widget->setOutputFormat(outputFormat);
    }
    
    widget->setAutoSaveEnabled(item->data(0, Qt::UserRole + 83).toBool());
    widget->setAutoSaveInterval(item->data(0, Qt::UserRole + 84).toInt());
    widget->setCompressionEnabled(item->data(0, Qt::UserRole + 85).toBool());
    
    QString compressionLevel = item->data(0, Qt::UserRole + 86).toString();
    if (!compressionLevel.isEmpty()) {
        widget->setCompressionLevel(compressionLevel);
    }
    
    widget->setReportEnabled(item->data(0, Qt::UserRole + 87).toBool());
    
    QString reportFormat = item->data(0, Qt::UserRole + 88).toString();
    if (!reportFormat.isEmpty()) {
        widget->setReportFormat(reportFormat);
    }
    
    widget->setIncludeCharts(item->data(0, Qt::UserRole + 89).toBool());
    widget->setIncludeStatistics(item->data(0, Qt::UserRole + 90).toBool());
    
    // Load output variables (for now, just clear the table as we don't store detailed variable info)
    widget->clearOutputVariables();
}


