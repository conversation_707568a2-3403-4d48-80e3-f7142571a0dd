# Responses Module

This document contains all responses related documentation organized by hierarchy.

---

## responses

# responses

Description of the model output data returned to Dakota upon evaluation of an interface.

**Topics**

block

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_responses](responses-id_responses.html) | Name the responses block; helpful when there are multiple  
Optional | [descriptors](responses-descriptors.html) | Labels for the responses  
Required (Choose One) | Response Type | [objective_functions](responses-objective_functions.html) | Response type suitable for optimization  
[calibration_terms](responses-calibration_terms.html) | Response type suitable for calibration or least squares  
[response_functions](responses-response_functions.html) | Generic response type  
Required (Choose One) | Gradient Type | [no_gradients](responses-no_gradients.html) | Gradients will not be used  
[analytic_gradients](responses-analytic_gradients.html) | Analysis driver will return gradients  
[mixed_gradients](responses-mixed_gradients.html) | Gradients are needed and will be obtained from a mix of numerical and analytic sources  
[numerical_gradients](responses-numerical_gradients.html) | Gradients are needed and will be approximated by finite differences  
Required (Choose One) | Hessian Type | [no_hessians](responses-no_hessians.html) | Hessians will not be used  
[numerical_hessians](responses-numerical_hessians.html) | Hessians are needed and will be approximated by finite differences  
[quasi_hessians](responses-quasi_hessians.html) | Hessians are needed and will be approximated by secant updates (BFGS or SR1) from a series of gradient evaluations  
[analytic_hessians](responses-analytic_hessians.html) | Hessians are needed and are available directly from the analysis driver  
[mixed_hessians](responses-mixed_hessians.html) | Hessians are needed and will be obtained from a mix of numerical, analytic, and “quasi” sources  
Optional | [metadata](responses-metadata.html) | (Experimental) Labels for floating point response metadata  
  
**Description**

The `responses` specification in a Dakota input file indicates the types of data that can be returned by an interface when invoked during Dakota’s execution. The specification includes three groups and two optional keywords.

**The response type group** indicates the type and number of responses expected by Dakota. It must be one of three types:

  * Optimization: objective and constraint functions

  * Calibration: calibration (least squares) terms and constraint functions

  * Uncertainty Quantifiation: generic response functions

The response type specified should be consistent with the iterative technique called for in the method specification. Certain general-purpose iterative techniques, such as parameter studies and design of experiments methods, can be used with any of these response types.

_The gradient type group_ indicates the availability of first derivatives (gradient vectors) for the response functions.

The gradient specification also links back to the iterative method used. Gradients commonly are needed when the iterative study involves gradient-based optimization, local reliability analysis for uncertainty quantification, or local sensitivity analysis. They can optionally be used to build some types of surrogate models.

_The Hessian type group_ specifies the availability of second derivatives (Hessian matrices) for the response functions.

Hessian availability for the response functions is similar to the gradient availability specifications, with the addition of support for “quasi-Hessians”. The Hessian specification also links back to the iterative method in use; Hessians commonly would be used in gradient-based optimization by full Newton methods or in reliability analysis with second-order limit state approximations or second-order probability integrations.

**Examples**

Several examples follow. The first example shows an optimization data set containing an objective function and two nonlinear inequality constraints. These three functions have analytic gradient availability and no Hessian availability.

    responses
      objective_functions = 1
        nonlinear_inequality_constraints = 2
      analytic_gradients
      no_hessians

The next example shows a typical specification for a calibration data set. The six residual functions will have numerical gradients computed using the dakota finite differencing routine with central differences of 0.1% (plus/minus delta relative to current variables value = .001*value).

    responses
      calibration_terms = 6
      numerical_gradients
        method_source dakota
        interval_type central
        fd_gradient_step_size = .001
      no_hessians

The last example shows a generic specification that could be used with a nondeterministic sampling iterator. The three response functions have no gradient or Hessian availability; therefore, only function values will be used by the iterator.

    responses
      response_functions = 3
      no_gradients
      no_hessians

Parameter study and design of experiments iterators are not restricted in terms of the response data sets which may be catalogued; they may be used with any of the function specification examples shown above.

**Theory**

Responses specify the total data set that is available for use by the method over the course of iteration. This is distinguished from the data subset described by an active set vector (see Dakota File Data Formats in the Users Manual [Adams et al., 2010]) indicating the particular subset of the response data needed for a particular function evaluation. Thus, the responses specification is a broad description of the data to be used during a study whereas the active set vector indicates the subset currently needed.


---

### responses → analytic_gradients

# analytic_gradients

Analysis driver will return gradients

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `analytic_gradients` specification means that gradient information is available directly from the simulation (finite differencing is not required). The simulation must return the gradient data in the Dakota format (enclosed in single brackets; see [Dakota Results File Data Format](../inputfile/responses.html#responses-results)). for the case of file transfer of data. The `analytic_gradients` keyword is a complete specification for this case.


---

### responses → analytic_hessians

# analytic_hessians

Hessians are needed and are available directly from the analysis driver

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `analytic_hessians` specification means that Hessian information is available directly from the simulation. The simulation must return the Hessian data in the Dakota format (enclosed in double brackets; see [Dakota Results File Data Format](../inputfile/responses.html#responses-results)) for the case of file transfer of data. The `analytic_hessians` keyword is a complete specification for this case.


---

### responses → calibration_terms

# calibration_terms

Response type suitable for calibration or least squares

**Specification**

  * _Alias:_ least_squares_terms num_least_squares_terms

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [scalar_calibration_terms](responses-calibration_terms-scalar_calibration_terms.html) | Number of scalar calibration terms  
Optional | [field_calibration_terms](responses-calibration_terms-field_calibration_terms.html) | Number of field calibration terms  
Optional | [primary_scales](responses-calibration_terms-primary_scales.html) | Characteristic values to scale each calibration term  
Optional | [weights](responses-calibration_terms-weights.html) | Specify weights for each objective function  
Optional (Choose One) | Calibration Data | [calibration_data](responses-calibration_terms-calibration_data.html) | Supply field or mixed field/scalar calibration data  
[calibration_data_file](responses-calibration_terms-calibration_data_file.html) | Supply scalar calibration data only  
Optional | [simulation_variance](responses-calibration_terms-simulation_variance.html) | Variance applied to simulation responses  
Optional | [nonlinear_inequality_constraints](responses-calibration_terms-nonlinear_inequality_constraints.html) | Group to specify nonlinear inequality constraints  
Optional | [nonlinear_equality_constraints](responses-calibration_terms-nonlinear_equality_constraints.html) | Group to specify nonlinear equality constraints  
  
**Description**

Responses for a calibration study are specified using `calibration_terms` and optional keywords for weighting/scaling, data, and constraints. In general when calibrating, Dakota automatically tunes parameters \\(\theta\\) to minimize discrepancies or residuals between the model and the data:

\\[R_{i} = y^{Model}_i(\theta) - y^{Data}_{i}.\\]

Note that the problem specification affects what must be returned to Dakota in the `[results_file](../../usingdakota/reference/interface-analysis_drivers-fork-results_file.html)` :

  * If calibration data _is not specified_ , then each of the calibration terms returned to Dakota through the `[interface](../../usingdakota/reference/interface.html)` is a residual \\(R_{i}\\) to be driven toward zero.

  * If calibration data _is specified_ , then each of the calibration terms returned to Dakota must be a response :math:` y^{Model}_i(theta)` , which Dakota will difference with the data in the specified data file.

_Constraints_

(See general problem formulation at `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`.) The keywords `[nonlinear_inequality_constraints](../../usingdakota/reference/responses-calibration_terms-nonlinear_inequality_constraints.html)` and `[nonlinear_equality_constraints](../../usingdakota/reference/responses-calibration_terms-nonlinear_equality_constraints.html)` specify the number of nonlinear inequality constraints em g, and nonlinear equality constraints em h, respectively. When interfacing to external applications, the responses must be returned to Dakota in this order in the `[results_file](../../usingdakota/reference/interface-analysis_drivers-fork-results_file.html)` : 0\. calibration terms nonlinear inequality constraints nonlinear equality constraints

An optimization problem’s linear constraints are provided to the solver at startup only and do not need to be included in the data returned on every function evaluation. Linear constraints are therefore specified in the `[variables](../../usingdakota/reference/variables.html)` block through the `[linear_inequality_constraint_matrix](../../usingdakota/reference/variables-linear_inequality_constraint_matrix.html)` \\(A_i\\) and `[linear_equality_constraint_matrix](../../usingdakota/reference/variables-linear_equality_constraint_matrix.html)` \\(A_e\\) .

Lower and upper bounds on the design variables em x are also specified in the `[variables](../../usingdakota/reference/variables.html)` block.

_Problem Transformations_

Weighting or scaling calibration terms is often appropriate to account for measurement error or to condition the problem for easier solution. Weighting or scaling transformations are applied in the following order:

1\. When present, observation error variance \\(\sigma_i\\) or full covariance \\(\Sigma\\) , optionally specified through `experiment_variance_type`, is applied to residuals first: .. math:: R^{(1)}_i = frac{R_{i}}{sigma_{i}} = frac{y^{Model}_i(theta) - y^{Data}_{i}}{sigma_{i}} textrm{, or} .. math:: R^{(1)} = Sigma^{-1/2} R = Sigma^{-1/2} left(y^{Model}(theta) - y^{Data}right), resulting in the typical variance-weighted least squares formulation .. math:: textrm{min}_theta ; R(theta)^T Sigma^{-1} R(theta) 2\. Any active scaling transformations are applied next, e.g., for characteristic value scaling: .. math:: R^{(2)}_i = frac{R^{(1)}_i}{s_i} 3\. Finally the optional weights are applied in a way that preserves backward compatibility: .. math:: R^{(3)}_i = sqrt{w_i}{R^{(2)}_i} so the ultimate least squares formulation, e.g., in a scaled and weighted case would be .. math:: f = sum_{i=1}^{n} w_i left( frac{y^{Model}_i - y^{Data}_i}{s_i} right)^2 _Note that specifying observation error variance and weights are mutually exclusive in a calibration problem._

**Theory**

Dakota calibration terms are typically used to solve problems of parameter estimation, system identification, and model calibration/inversion. Local least squares calibration problems are most efficiently solved using special-purpose least squares solvers such as Gauss-Newton or Levenberg-Marquardt; however, they may also be solved using any general-purpose optimization algorithm in Dakota. While Dakota can solve these problems with either least squares or optimization algorithms, the response data sets to be returned from the simulator are different when using `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)` versus `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`.

Least squares calibration involves a set of residual functions, whereas optimization involves a single objective function (sum of the squares of the residuals), i.e., .. math:: f = sum_{i=1}^{n} R_i^2 = sum_{i=1}^{n} left(y^{Model}_i(theta) - y^{Data}_{i} right)^2 where _f_ is the objective function and the set of \\(R_i\\) are the residual functions, most commonly defined as the difference between a model response and data. Therefore, function values and derivative data in the least squares case involve the values and derivatives of the residual functions, whereas the optimization case involves values and derivatives of the sum of squares objective function. This means that in the least squares calibration case, the user must return each of `n` residuals separately as a separate calibration term. Switching between the two approaches sometimes requires different simulation interfaces capable of returning the different granularity of response data required, although Dakota supports automatic recasting of residuals into a sum of squares for presentation to an optimization method. Typically, the user must compute the difference between the model results and the observations when computing the residuals. However, the user has the option of specifying the observational data (e.g. from physical experiments or other sources) in a file.


---

#### responses → calibration_terms → calibration_data

# calibration_data

Supply field or mixed field/scalar calibration data

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [data_directory](responses-calibration_terms-calibration_data-data_directory.html) | Specify a directory containing the calibration field data files used with the mixed case specification  
Optional | [num_experiments](responses-calibration_terms-calibration_data-num_experiments.html) | Add context to data: number of different experiments  
Optional | [num_config_variables](responses-calibration_terms-calibration_data-num_config_variables.html) | Add context to data: number of configuration variables.  
Optional | [experiment_variance_type](responses-calibration_terms-calibration_data-experiment_variance_type.html) | Add context to data: specify the type of experimental error  
Optional | [scalar_data_file](responses-calibration_terms-calibration_data-scalar_data_file.html) | Specify a scalar data file to complement field data files (mixed case)  
Optional | [interpolate](responses-calibration_terms-calibration_data-interpolate.html) | Flag to indicate interpolation of simulation values.  
  
**Description**

`calibration_data` specifies a keyword block that indicates that Dakota should read in experimental data for calibration. This block is primarily to support the reading of field calibration data. For simpler, scalar-only response cases, see `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`. The user will typically specify the number of experiments, `num_experiments`. If this is not specified, it is assumed there is only one experiment.

Up to four types of data may be read. They are read from a collection of files, one per response descriptor, per experiment. In this discussion, DESC refers to the response descriptor and NUM to the experiment number.

  1. _Values:_ The scalar or field-valued response function values, e.g., temperature values, voltage levels. These are read from files named `DESC`.NUM.dat (one per response descriptor, per experiment), e.g, `NUM.datvolts`.1.dat, `1.dat,volts`.2.dat. Scalar files will contain a single value, while field files will each contain a column of field reponse values. Without `[interpolate](../../usingdakota/reference/responses-calibration_terms-calibration_data-interpolate.html)` enabled, the lengths of these files must match those specified using the responses-calibration_terms-field_calibration_terms-lengths keyword.

  2. _Coordinates:_ Field coordinates specify independent variables (e.g., spatial or temporal coordinates) upon which the field depends. For example, the voltage might be a function of time, so time is the field coordinate. These are read from files named `DESC`.NUM.coords, each containing `[num_coordinates_per_field](../../usingdakota/reference/responses-calibration_terms-field_calibration_terms-num_coordinates_per_field.html)` columns. The number of rows must be the same as in the values files described in the previous bullet.

  3. _Variances:_ If `[experiment_variance_type](../../usingdakota/reference/responses-calibration_terms-calibration_data-experiment_variance_type.html)` is specified, variance values are read from files `DESC`.NUM.sigma. Note that a single `NUM.sigma.experiment_variance_type` may be specified, or a unique `experiment_variance_type` per response descriptor (per scalar or field). If the `experiment_variance_type` is:

>   * ‘scalar’: a single variance value will be read from the file.
> 
>   * ‘diagonal’ (field responses only): a column vector of variance values (length equal to the number of experimental data points) will be read from the file. The values are the variances of each field value for this descriptor.
> 
>   * ‘matrix’ (field responses only): a matrix of covariance values (square with size the number of experimental values) will be read from the file. The matrix is a full covariance matrix for the components of this field response. While covariance among entries in a field response may be specified, covariance among experiments is not permitted.
> 
> 

  4. _Configuration variables:_ specify the conditions corresponding to different experiments. When responses-calibration_terms-calibration_data-num_config_variables is specified, the configuration variable values for each experiment should be placed in a file named `experiment`.NUM.config, where the number of items in that config file are the `NUM.config,num_config_variables`. These variables are used as auxilliary state variables for the simulation (for example) and are not calibrated. _Attention:_ In versions of Dakota prior to 6.14, string-valued configuration variables were specified in data files with 0-based indices into the admissible values. As of Dakota 6.14, strings must be specified by value. For example a string-valued configuration variable for an experimental condition might appear in the file as `low_pressure` vs. `high_pressure`.

_Aggregating scalar data:_ The above description is primarily relevant for field data (with files for field values, field coordinates, field variances). If the user also has scalar experimental data, it may be entered as described above, i.e., one file named DESC.NUM.dat per scalar response. However, an alternative is to provde the data for all scalar responses in aggregate in the simpler `[scalar_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data-scalar_data_file.html)` format, with the number of rows of that file equal to the number of experiments. The scalar data file may be used in combination with the the separate field files described above.

_Interpolation:_ One important feature of field data is the capability to interpolate between points in the field. For example, we may have simulation data at a set of responses \\(y\\) at time points \\(t\\) : ( \\(t_{s1}, y_{s1}\\) ), ( \\(t_{s2}, y_{s2}\\) ), etc. In this example, \\(t\\) is the independent coordinate for the simulation, and the simulation time and response points are denoted with subscripts \\(s1, s2, s3,\\) . If the user has experimental data that is taken at different time points: ( \\(t_{e1}, y_{e1}\\) ), ( \\(t_{e2}, y_{e2}\\) ), …, it is necessary to interpolate the simulation data to provide estimates of the simulation response at the experimental time points to construct the residual terms (model - experiment) at the experimental time points. Dakota can perform 1-D interpolation. The user must specify the keyword `interpolate`, and also provide the field coordinates as well as field values for the experiment data.

If the `interpolate` keyword is not specified, Dakota will assume that the simulation field data and the experiment field data is taken at the same set of independent coordinate values and simply construct the difference between these field terms to create the set of residuals for the sum-of-squares calculation. When `interpolate` is specified, the simulation coordinates are assumed fixed and the same for each simulation. These simulation coordinates are provided in DESC.coords. However, the experiment coordinates for each experiment can be different, and are provided in the files numbered by experiment with the file names given by DESC.NUM.coords, as indicated above.


---

##### responses → calibration_terms → calibration_data → data_directory

# data_directory

Specify a directory containing the calibration field data files used with the mixed case specification

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Description**


---

##### responses → calibration_terms → calibration_data → experiment_variance_type

# experiment_variance_type

Add context to data: specify the type of experimental error

**Specification**

  * _Alias:_ variance_type

  * _Arguments:_ STRINGLIST

  * _Default:_ none

**Description**

There are four options for specifying the experimental error (e.g. the measurement error in the data you provide for calibration purposes): ‘none’ (default), ‘scalar’, ‘diagonal’, or ‘matrix.’

If the user specifies scalar, they can provide a scalar variance per calibration term. Note that for scalar calibration terms, only ‘none’ or ‘scalar’ are options for the measurement variance. However, for field calibration terms, there are two additional options. ‘diagonal’ allows the user to provide a vector of measurement variances (one for each term in the calibration field). This vector corresponds to the diagonal of the full covariance matrix of measurement errors. If the user specifies ‘matrix’, they can provide a full covariance matrix (not just the diagonal terms), where each element (i,j) of the covariance matrix represents the covariance of the measurement error between the i-th and j-th field values.

_Usage Tips_

Variance information is specified on a per-response group (descriptor), per-experiment basis. Off-diagonal covariance between response groups or between experiments is not supported.

**Examples**

The figure below shows an observation vector with 5 responses; 2 scalar + 3 field (each field of length > 1). The corresponding covariance matrix has scalar variances \\(\sigma_1^2, \sigma_2^2\\) for each of the scalars \\(s1, s2\\) , diagonal covariance \\(D_3\\) for field \\(f3\\) , scalar covariance \\(\sigma_4^2\\) for field \\(f4\\) , and full matrix covariance \\(C_5\\) for field \\(f5\\) . In total, Dakota supports block diagonal covariance \\(\Sigma\\) across the responses, with blocks \\(\Sigma_i\\) , which could be fully dense within a given field response group. Covariance across the highest-level responses (off-diagonal blocks) is not supported, nor is covariance between experiments.

image html ObsErrorCovariance.png “An example of scalar and field response data, with associated block-diagonal observation error covariance.”


---

##### responses → calibration_terms → calibration_data → interpolate

# interpolate

Flag to indicate interpolation of simulation values.

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

If `interpolate` is specified, Dakota will interpolate between the simulation data and the experiment data to calculate the residuals for calibration methods. Specifically, the simulation data is interpolated onto the experimental data points. So, if the simulation data is a field of length 100 with one independent coordinate, and the experiment data is of length 5 with one independent coordinate, the interpolation is done between the 100 (t,f) simulation points (where t is the independent coordinate and f is the simulation field value) onto the five (t_e, f_e) points to obtain the residual differences between the simulation and experiment. See `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)`.


---

##### responses → calibration_terms → calibration_data → num_config_variables

# num_config_variables

Add context to data: number of configuration variables.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Description**

If there are multiple experiments, there can be different configuration variables (e.g. experimental settings, boundary conditions, etc.) per experiment. See `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)` or `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`.

During calibration, configuration variables are state variables which will be passed to the simulation, and are not treated as calibration parameters.


---

##### responses → calibration_terms → calibration_data → num_experiments

# num_experiments

Add context to data: number of different experiments

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 1

**Description**

The number of different experiments. Dakota will expand the total number of residual terms based on the number of calibration terms and the number of experiments. For example, if the number of calibration terms are five scalars, and there are three experiments, the total number of residuals in the least squares formulation will be 15. See `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)` or `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`.


---

##### responses → calibration_terms → calibration_data → scalar_data_file

# scalar_data_file

Specify a scalar data file to complement field data files (mixed case)

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](responses-calibration_terms-calibration_data-scalar_data_file-custom_annotated.html) | Selects custom-annotated tabular file format for experiment data  
[annotated](responses-calibration_terms-calibration_data-scalar_data_file-annotated.html) | Selects annotated tabular file format for experiment data  
[freeform](responses-calibration_terms-calibration_data-scalar_data_file-freeform.html) | Selects free-form tabular file format for experiment data  
  
**Description**

When calibrating both scalar and field calibration terms, to associated experimental data, the scalar data may be provided in the file named by `scalar_data_file`. This file follows the same format as: `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`.

_Default Behavior_

If `scalar_data_file` is omitted, all calibration data, including for scalar responses, will be read from the generic field `calibration_data` format.


---

###### responses → calibration_terms → calibration_data → scalar_data_file → annotated

# annotated

Selects annotated tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. For experiment data files, each subsequent row contains an experiment ID, followed by data for configuration variables, observations, and/or observation errors, depending on context.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import an annotated experimental data file containing a header row, leading exp_id column, and experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        annotated

Example data file with two measured quantities, three experiments:

    exp_id  velocity stress
    1  18.23  83.21
    2  34.14  93.24
    3  22.41  88.92


---

###### responses → calibration_terms → calibration_data → scalar_data_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](responses-calibration_terms-calibration_data-scalar_data_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [exp_id](responses-calibration_terms-calibration_data-scalar_data_file-custom_annotated-exp_id.html) | Enable experiment ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file containing experiment data, including configuration variables, observatiions, and/or observation errors, depending on context. For experiment import, custom-annotated allows user options for whether `header` row and `exp_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. The `custom_annotated` keyword, followed by options can be used to select other formats.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import an experimental data file containing a header row, no leading exp_id column, and experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        custom_annotated header

Example data file with two measured quantities, three experiments:

    % velocity stress
     18.23  83.21
     34.14  93.24
     22.41  88.92


---

###### responses → calibration_terms → calibration_data → scalar_data_file → custom_annotated → exp_id

# exp_id

Enable experiment ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no exp_id column

**Description**

See description of parent `custom_annotated`


---

###### responses → calibration_terms → calibration_data → scalar_data_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no header

**Description**

See description of parent `custom_annotated`


---

###### responses → calibration_terms → calibration_data → scalar_data_file → freeform

# freeform

Selects free-form tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. For experiment data files, each row contains data for configuration variables, observatiions, and/or observation errors, depending on context.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. Specify `freeform` to instead select this format.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import a free-form experimental data file containing raw experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        freeform

Example data file with two measured quantities, three experiments:

    18.23  83.21
    34.14  93.24
    22.41  88.92


---

#### responses → calibration_terms → calibration_data_file

# calibration_data_file

Supply scalar calibration data only

**Specification**

  * _Alias:_ least_squares_data_file

  * _Arguments:_ STRING

  * _Default:_ none

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](responses-calibration_terms-calibration_data_file-custom_annotated.html) | Selects custom-annotated tabular file format for experiment data  
[annotated](responses-calibration_terms-calibration_data_file-annotated.html) | Selects annotated tabular file format for experiment data  
[freeform](responses-calibration_terms-calibration_data_file-freeform.html) | Selects free-form tabular file format for experiment data  
Optional | [num_experiments](responses-calibration_terms-calibration_data_file-num_experiments.html) | Add context to data: number of different experiments  
Optional | [num_config_variables](responses-calibration_terms-calibration_data_file-num_config_variables.html) | Add context to data: number of configuration variables.  
Optional | [experiment_variance_type](responses-calibration_terms-calibration_data_file-experiment_variance_type.html) | Add context to data: specify the type of experimental error  
  
**Description**

Enables text file import of experimental observations for use in calibration, for scalar responses only, with optional scalar variance information. For more complex data import cases see `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)` Dakota will calibrate model variables to best match these data.

Key options include:

li format: whether the data file is in `annotated`,

`custom_annotated`, or `freeform` format

li content: where `num_experiments`,

`num_config_variables`, and `experiment_variance_type` indicate which columns appear in the data.

In the most general case, the content of the data file is described by the arguments of three optional parameters.

  * `num_experiments` ( \\(N_{exp}\\) ) Default: \\(N_{exp} = 1\\) This indicates that the data represent multiple experiments, where each experiment might be conducted with different values of configuration variables. An experiment can also be thought of as a replicate, where the experiments are run at the same values of the configuration variables.

  * `num_config_variables` ( \\(N_{cfg}\\) ) Configuration variables specify the values of experimental conditions at which data were collected. The variables in these columns must correspond to state variables in the calibration study. The simulation model will be run at each configuration and compared to the appropriate experiment data.

  * `experiment_variance_type` (‘none’ or ‘scalar’) This indicates if the data file contains variances for measurement error of the experimental data. The default is ‘none’.

While some components may be omitted, the most complete version of a an annotated calibration data file could include columns corresponding to experiment ID, configuration variables, function value observations, and variances (observation errors), shown here in annotated format:

    exp_id | configuration xvars | y data observations | y data variances
    1         7.8  7                21.9372  1.8687       0.25  0.04
    2         8.6  2                19.0779  4.8976       0.25  0.04
    3         8.4  8                38.2758  4.4559       0.25  0.04
    4         4.2  1                39.7600  6.4631       0.25  0.04

Each row in the file corresponds to an experiment or replicate observation of an experiment to be compared to the model output. This example shows 4 experiments, governed by two configuration variables (one real-valued and one integer-valued), two responses (QOIs), and corresponding observation errors with standard deviation 0.5 and 0.2.

_Usage Tips_

  * The `calibration_data_file` keyword is used when em only scalar calibration terms are present. If there are field calibration terms, instead use `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)`. For mixed scalar and field calibration terms, one may use the `[scalar_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data-scalar_data_file.html)` specification, which uses the format described on this page.

  * _Attention:_ In versions of Dakota prior to 6.14, string-valued configuration variables were specified in data files with 0-based indices into the admissible values. As of Dakota 6.14, strings must be specified by value. For example a string-valued configuration variable for an experimental condition might appear in the file as `low_pressure` vs. `high_pressure`.

**Examples**

_Simple Case:_ In the simplest case, no data content descriptors are specified:

    responses
      calibration_terms = 2
        descriptors = 'volts' 'amps'
        calibration_data_file = 'circuit.dat'
          annotated

And the data file `circuit`.dat must contain only the \\(y^{Data}\\) observations which represent a single experimental observation. In this case, the data file should have \\(N_{terms} = 2\\) columns (for volts, amps) and 1 row, where \\(N_{terms}\\) is the value of `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`. The data file is shown here in annotated format:

    exp_id | y data observations
    1         21.9372  1.8687

For each function evaluation, Dakota will run the analysis driver, which must return \\(N_{terms} = 2\\) model responses. Then the residuals are computed as:

\\[R_{i} = y^{Model}_i - y^{Data}_{i}.\\]

These residuals can be weighted using `[weights](../../usingdakota/reference/responses-calibration_terms-weights.html)`.

_Multiple experiments:_ One might specify `num_experiments` \\(N_E\\) indicating that there are multiple experiments. When multiple experiments are present, Dakota will expand the number of residuals for the repeat measurement data and difference with the data accordingly. For example, if the user has \\(N_E = 4\\) experiments in the example above with 2 calibration terms, the input file would contain

    responses
      calibration_terms = 2
        descriptors = 'volts' 'amps'
        calibration_data_file = 'circuit.dat'
          annotated
          num_experiments = 4

And the `calibration_data_file` would need to contain 2 rows (one for each experiment), and each row should contain 2 experimental data values that will be differenced with respect to the appropriate model response:

    exp_id  | y data observations
    1          21.9372  1.8687
    2          19.0779  4.8976
    3          38.2758  4.4559
    4          39.7600  6.4631

To summarize, Dakota will calculate the sum of the squared residuals as:

\\[f = \sum_{i=1}^{N_E}R_{i}^2\\]

where the residuals now are calculated as:

\\[R_{i} = y^{Model}_i(\theta) - y^{Data}_{i}.\\]

_With experimental variances:_ If information is known about the measurement error and the uncertainty in the measurement, that can be specified by sending the measurement error variance to Dakota. In this case, the keyword `experiment_variance_type` is added, followed by a string of variance types of length one or of length \\(N_{terms}\\) , where \\(N_{terms}\\) is the value of `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`. The `experiment_variance_type` for each response can be ‘none’ or ‘scalar’. NOTE: you must specify the same `experiment_variance_type` for all scalar terms. That is, they will all be ‘none’ or all be ‘scalar.’

    responses
      calibration_terms = 2
        descriptors = 'volts' 'amps'
        calibration_data_file = 'circuit.dat'
          annotated
          experiment_variance_type 'scalar'

For each response that has a ‘scalar’ variance type, each row of the datafile will now have \\(N_{terms} = 2\\) of \\(y\\) data values (volts, amps) followed by \\(N_{terms} =2\\) columns that specify the measurement error (in units of variance, not standard deviation) for volts, amps. An example with two experiments in annotated format:

    exp_id | y data observations | y data variances
    1         21.9372  1.8687       0.25  0.04

Dakota will run the analysis driver, which must return \\(N_{terms}\\) responses. Then the residuals are computed as:

\\[R_{i} = \frac{y^{Model}_i - y^{Data}_{i}}{\sqrt{{var}_i}}\\]

for \\(i = 1 \dots N_{terms}\\) .

_Putting all the options together:_ Specifying all these options together might look like

    responses
      calibration_terms = 2
        descriptors = 'volts' 'amps'
        calibration_data_file = 'circuit.dat'
          annotated
          num_experiments = 4
          experiment_variance_type 'scalar'

Dakota will expect a data file

    exp_id | configuration xvars | y data observations | y data variances
    1         7.8  7                21.9372  1.8687       0.25  0.04
    2         8.6  2                19.0779  4.8976       0.25  0.04
    3         8.4  8                38.2758  4.4559       0.25  0.04
    4         4.2  1                39.7600  6.4631       0.25  0.04

To compute residuals for each experiment, e.g., exp_id = 4, Dakota will

  1. Evaluate the computational model at the specified configuration (state variables = [4.2, 1]).

  2. Difference the resulting 2 function values with the data [39.7600 volts, 6.4631 amps]

  3. Weight by the standard deviation = sqrt([0.25 0.04])


---

##### responses → calibration_terms → calibration_data_file → annotated

# annotated

Selects annotated tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. For experiment data files, each subsequent row contains an experiment ID, followed by data for configuration variables, observations, and/or observation errors, depending on context.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import an annotated experimental data file containing a header row, leading exp_id column, and experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        annotated

Example data file with two measured quantities, three experiments:

    exp_id  velocity stress
    1  18.23  83.21
    2  34.14  93.24
    3  22.41  88.92


---

##### responses → calibration_terms → calibration_data_file → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](responses-calibration_terms-calibration_data_file-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [exp_id](responses-calibration_terms-calibration_data_file-custom_annotated-exp_id.html) | Enable experiment ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file containing experiment data, including configuration variables, observatiions, and/or observation errors, depending on context. For experiment import, custom-annotated allows user options for whether `header` row and `exp_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. The `custom_annotated` keyword, followed by options can be used to select other formats.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import an experimental data file containing a header row, no leading exp_id column, and experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        custom_annotated header

Example data file with two measured quantities, three experiments:

    % velocity stress
     18.23  83.21
     34.14  93.24
     22.41  88.92


---

###### responses → calibration_terms → calibration_data_file → custom_annotated → exp_id

# exp_id

Enable experiment ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no exp_id column

**Description**

See description of parent `custom_annotated`


---

###### responses → calibration_terms → calibration_data_file → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no header

**Description**

See description of parent `custom_annotated`


---

##### responses → calibration_terms → calibration_data_file → experiment_variance_type

# experiment_variance_type

Add context to data: specify the type of experimental error

**Specification**

  * _Alias:_ variance_type

  * _Arguments:_ STRINGLIST

  * _Default:_ none

**Description**

There are four options for specifying the experimental error (e.g. the measurement error in the data you provide for calibration purposes): ‘none’ (default), ‘scalar’, ‘diagonal’, or ‘matrix.’

If the user specifies scalar, they can provide a scalar variance per calibration term. Note that for scalar calibration terms, only ‘none’ or ‘scalar’ are options for the measurement variance. However, for field calibration terms, there are two additional options. ‘diagonal’ allows the user to provide a vector of measurement variances (one for each term in the calibration field). This vector corresponds to the diagonal of the full covariance matrix of measurement errors. If the user specifies ‘matrix’, they can provide a full covariance matrix (not just the diagonal terms), where each element (i,j) of the covariance matrix represents the covariance of the measurement error between the i-th and j-th field values.

_Usage Tips_

Variance information is specified on a per-response group (descriptor), per-experiment basis. Off-diagonal covariance between response groups or between experiments is not supported.

**Examples**

The figure below shows an observation vector with 5 responses; 2 scalar + 3 field (each field of length > 1). The corresponding covariance matrix has scalar variances \\(\sigma_1^2, \sigma_2^2\\) for each of the scalars \\(s1, s2\\) , diagonal covariance \\(D_3\\) for field \\(f3\\) , scalar covariance \\(\sigma_4^2\\) for field \\(f4\\) , and full matrix covariance \\(C_5\\) for field \\(f5\\) . In total, Dakota supports block diagonal covariance \\(\Sigma\\) across the responses, with blocks \\(\Sigma_i\\) , which could be fully dense within a given field response group. Covariance across the highest-level responses (off-diagonal blocks) is not supported, nor is covariance between experiments.

image html ObsErrorCovariance.png “An example of scalar and field response data, with associated block-diagonal observation error covariance.”


---

##### responses → calibration_terms → calibration_data_file → freeform

# freeform

Selects free-form tabular file format for experiment data

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. For experiment data files, each row contains data for configuration variables, observatiions, and/or observation errors, depending on context.

_Default Behavior_

By default, Dakota imports tabular experiment data files in annotated format. Specify `freeform` to instead select this format.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

**Examples**

Import a free-form experimental data file containing raw experiment data in a calibration study

    responses
      ...
      scalar_data_file 'shock_experiment.dat'
        freeform

Example data file with two measured quantities, three experiments:

    18.23  83.21
    34.14  93.24
    22.41  88.92


---

##### responses → calibration_terms → calibration_data_file → num_config_variables

# num_config_variables

Add context to data: number of configuration variables.

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Description**

If there are multiple experiments, there can be different configuration variables (e.g. experimental settings, boundary conditions, etc.) per experiment. See `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)` or `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`.

During calibration, configuration variables are state variables which will be passed to the simulation, and are not treated as calibration parameters.


---

##### responses → calibration_terms → calibration_data_file → num_experiments

# num_experiments

Add context to data: number of different experiments

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 1

**Description**

The number of different experiments. Dakota will expand the total number of residual terms based on the number of calibration terms and the number of experiments. For example, if the number of calibration terms are five scalars, and there are three experiments, the total number of residuals in the least squares formulation will be 15. See `[calibration_data](../../usingdakota/reference/responses-calibration_terms-calibration_data.html)` or `[calibration_data_file](../../usingdakota/reference/responses-calibration_terms-calibration_data_file.html)`.


---

#### responses → calibration_terms → field_calibration_terms

# field_calibration_terms

Number of field calibration terms

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lengths](responses-calibration_terms-field_calibration_terms-lengths.html) | Lengths of field responses  
Optional | [num_coordinates_per_field](responses-calibration_terms-field_calibration_terms-num_coordinates_per_field.html) | Number of independent coordinates for field responses  
Optional | [read_field_coordinates](responses-calibration_terms-field_calibration_terms-read_field_coordinates.html) | Add context to data: flag to indicate that field coordinates should be read  
  
**Description**

This keyword describes the number of field calibration terms. A set of field calibration terms is a set of related response values collected over a range of independent coordinate values which may or may not be specified by the user. For example, voltage over time would be a field function, where voltage is the `field_objective` and time is the independent coordinate. Similarly, temperature over time and space would be a field response, where the independent coordinates would be both time and spatial coordinates such as (x,y) or (x,y,z), depending on the application. The main difference between scalar calibration terms and field calibration terms is that for field data, we plan to implement methods that take advantage of the correlation or relationship between the field values. For example, with calibration, if we want to calibrate parameters that result in a good model fit to a time-temperature curve, we may have to do some interpolation between the experimental data and the simulation data. That capability requires knowledge of the independent coordinates.

Note that if there is one `field_calibration_terms`, and it has length 100 (meaning 100 values), then the user’s simulation code must return 100 values. Also, if there are both scalar and field calibration, the user should specify the number of scalar terms as `scalar_calibration_terms`. If there are only field calibration terms, it still is necessary to specify both `field_calibration_terms` = NN and `calibration_terms` = NN, where NN is the number of field calibration terms.

Calibration terms are responses that are used with calibration methods in Dakota, such as least squares optimizers. Currently, each scalar term is added to the total sum-of-squares error function presented to the optimizer. However, each individual field value is added as well. For example, if you have one field calibration term with length 100 (e.g. a time-temperature trace with 100 time points and 100 temperature points), the 100 temperature values will be added to create the overall sum-of-squares error function used in calibration. We have an initial capability to interpolate the field data from the user’s simulation to the experimental data. For example, if the user has thermocouple readings at 20 time points, it will be an experimental field response with 20 time points and 20 temperature values. Dakota takes the 100 simulation time-temperature values (from the example above) and interpolates those to the 20 experimental points, to create 20 residual terms (simulation minus experimental data points) that will be used in calibration.


---

##### responses → calibration_terms → field_calibration_terms → lengths

# lengths

Lengths of field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the lengths of each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `lengths` = 50 200, indicating that the first field response has 50 field elements but the second one has 200. The coordinate values (e.g. the independent variables) corresponding to these field responses are read in files labeled response_descriptor.coords.


---

##### responses → calibration_terms → field_calibration_terms → num_coordinates_per_field

# num_coordinates_per_field

Number of independent coordinates for field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the number of independent coordinates for each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `num_coordinates_per_field` = 2 1 means that the first field response has two sets of independent coordinates (perhaps x, y locations), but the second response only has one (for example, time where the field response is only dependent upon time). The actual coordinate values (e.g. the independent variables) corresponding to these field responses are defined in a file call response_descriptor.coords, where response_descriptor is the name of the individual field.


---

##### responses → calibration_terms → field_calibration_terms → read_field_coordinates

# read_field_coordinates

Add context to data: flag to indicate that field coordinates should be read

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Field coordinates specify independent variables (e.g. spatial or temporal coordinates) upon which

the field depends. For example, the voltage level above might be a function of time, so time is the field coordinate. If the user has field coordinates to read, they need to specify `read_field_coordinates`. The field coordinates will then be read from a file named response_descriptor.coords, where response_descriptor is the user-provided descriptor for the field response. The number of columns in the coords file should be equal to the number of field coordinates.


---

#### responses → calibration_terms → nonlinear_equality_constraints

# nonlinear_equality_constraints

Group to specify nonlinear equality constraints

**Topics**

nonlinear_constraints

**Specification**

  * _Alias:_ num_nonlinear_equality_constraints

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [targets](responses-calibration_terms-nonlinear_equality_constraints-targets.html) | Target values for the nonlinear equality constraint  
Optional | [scale_types](responses-calibration_terms-nonlinear_equality_constraints-scale_types.html) | How to scale each nonlinear constraint  
Optional | [scales](responses-calibration_terms-nonlinear_equality_constraints-scales.html) | Characteristic values to scale each nonlinear constraint  
  
**Description**

Specifies the number of nonlinear equality constraint functions returned by the interface.

The `targets` specification provides the targets for nonlinear equalities of the form

\\[h(x) = h_t\\]

and the defaults for the equality targets enforce a value of `0`. for each constraint

\\[h(x) = 0.0\\]

The `scale_types` and `scales` keywords are related to scaling of \\(h \left( x \right)\\) . See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_equality_constraints → scale_types

# scale_types

How to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_equality_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ no scaling

**Description**

Each string in `scale_types` indicates the scaling type for each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * `value` \- characteristic value by which nonlinear constraint values will be divided. If this is chosen, then `scales` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * `auto` \- automatic scaling based on bounds (inequalities) or targets (equalities)

  * `log` \- logarithmic scaling (can be used together with `scales`, which can be helpfully used to negate values prior to log transformation)

If a single string is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a string must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_equality_constraints → scales

# scales

Characteristic values to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_equality_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `scales` is a nonzero characteristic value to be used in scaling each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `scale_types` of `value` and optional for `log`. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative response functions.

If a single scale value is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a value must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

When `scales` are specified, but not `scale_types`, the scaling type is assumed to be ‘value’ for this set of nonlinear inequality (equality) constraints.

Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_equality_constraints → targets

# targets

Target values for the nonlinear equality constraint

**Specification**

  * _Alias:_ nonlinear_equality_targets

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

The `targets` specification provides the targets for nonlinear equalities of the form

\\[g(x) = g_t\\]

and the defaults for the equality targets enforce a value of `0`.0 for each constraint:

\\[g(x) = 0.0\\]


---

#### responses → calibration_terms → nonlinear_inequality_constraints

# nonlinear_inequality_constraints

Group to specify nonlinear inequality constraints

**Topics**

nonlinear_constraints

**Specification**

  * _Alias:_ num_nonlinear_inequality_constraints

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [lower_bounds](responses-calibration_terms-nonlinear_inequality_constraints-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](responses-calibration_terms-nonlinear_inequality_constraints-upper_bounds.html) | Specify maximium values  
Optional | [scale_types](responses-calibration_terms-nonlinear_inequality_constraints-scale_types.html) | How to scale each nonlinear constraint  
Optional | [scales](responses-calibration_terms-nonlinear_inequality_constraints-scales.html) | Characteristic values to scale each nonlinear constraint  
  
**Description**

Specifies the number of nonlinear inequality constraint functions returned by the interface.

The `lower_bounds` and `upper_bounds` specifications provide the lower and upper bounds for 2-sided nonlinear inequalities of the form

\\[g_l \leq g(x) \leq g_u\\]

When constraint bounds are not specified, the problem is assumed to have one-sided inequalities bounded above by zero:

\\[g(x) \leq 0.0.\\]

This provides backwards compatibility with previous Dakota versions.

In a user bounds specification, any upper bound values greater than ```+bigRealBoundSize (1.e+30, as defined in Minimizer) are treated as +infinity and any lower bound values less than ``-bigRealBoundSize` are treated as -infinity. This feature is commonly used to drop one of the bounds in order to specify a 1-sided constraint (just as the default lower bounds drop out since `-DBL_MAX` <

`-bigRealBoundSize`). The same approach is used for nonexistent linearinequality bounds and for nonexistent design variable bounds.

The `scale_types` and `scales` keywords are related to scaling of \\(g \left( x \right)\\) . See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_inequality_constraints → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ nonlinear_inequality_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ vector values = -infinity

**Description**

Specify minimum values


---

##### responses → calibration_terms → nonlinear_inequality_constraints → scale_types

# scale_types

How to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_inequality_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ no scaling

**Description**

Each string in `scale_types` indicates the scaling type for each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * `value` \- characteristic value by which nonlinear constraint values will be divided. If this is chosen, then `scales` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * `auto` \- automatic scaling based on bounds (inequalities) or targets (equalities)

  * `log` \- logarithmic scaling (can be used together with `scales`, which can be helpfully used to negate values prior to log transformation)

If a single string is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a string must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_inequality_constraints → scales

# scales

Characteristic values to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_inequality_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `scales` is a nonzero characteristic value to be used in scaling each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `scale_types` of `value` and optional for `log`. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative response functions.

If a single scale value is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a value must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

When `scales` are specified, but not `scale_types`, the scaling type is assumed to be ‘value’ for this set of nonlinear inequality (equality) constraints.

Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → calibration_terms → nonlinear_inequality_constraints → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ nonlinear_inequality_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

Specify maximium values


---

#### responses → calibration_terms → primary_scales

# primary_scales

Characteristic values to scale each calibration term

**Specification**

  * _Alias:_ calibration_term_scales least_squares_term_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `primary_scales` is a nonzero characteristic value to be used in scaling each calibration term. They only have effect when the associated method specifies `scaling`.

Length: When specified, `primary_scales` must have length one of:

  * One (the single value will be applied to each primary response); or

  * Number of response groups ( `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`), so the number of scalars plus the number of fields when fields are present; or

  * Total number of response elements, so the number of scalar responses plus the sum of the lengths of the fields.

_Usage Tips:_

Calibration only supports value-based scaling. Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

Weights are applied as multipliers, scales as charateristic values / divisors.

When scaling is active, it is applied to calibration terms after any residual formation (accounting for experimental data and optionally measurement error covariance), and before any weights. See the equations in `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`.

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

#### responses → calibration_terms → scalar_calibration_terms

# scalar_calibration_terms

Number of scalar calibration terms

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

**Description**

This keyword describes the number of scalar calibration terms. It is meant to be used in conjunction with `field_calibration_terms`, which describes the number of field calibration terms. The total number of calibration terms, both scalar and field, is given by `calibration_terms`. If only scalar calibration terms are specified, it is not necessary to specify the number of scalar terms explicitly: one can simply say `calibration_terms` = 5 and get 5 scalar terms. However, if there are three scalar terms and 2 field terms, then `calibration_terms` = 5 but `scalar_calibration_terms` = 3 and `field_calibration_terms` = 2.

Calibration terms are responses that are used with calibration methods in Dakota, such as least squares optimizers. Currently, each scalar term is added to the total sum-of-squares error function presented to the optimizer. However, each individual field value is added as well. For example, if you have one field calibration term with length 100 (e.g. a time - temperature trace with 100 time points and 100 temperature points), the 100 temperature values will be added to create the overall sum-of-squares error function used in calibration.


---

#### responses → calibration_terms → simulation_variance

# simulation_variance

Variance applied to simulation responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ REALLIST

  * _Default:_ no variance

**Description**

The variance that is applied to simulations run by Dakota, i.e. using the `analysis_drivers` command. The user may supply a single variance or a vector of variances of length equal to the number of responses. In both cases, the values provided are treated as scalar variance types. If a single variance is provided, it is applied to all responses produced by the simulation code. If a vector is provided, each variance is applied to the corresponding response output produced by the simulation code.

It is important to note that the the variance defined by this keyword differs from that defined using `experiment_variance_type`. These two commands apply to user-provided calibration data, specified, for example, by `calibration_data` or `calibration_data_file`. However, `simulation_variance` applies to those responses produced by simulation code that is run by Dakota, as described above.

_Usage Tips_

Currently, this keyword is only in use as part of the algorithm implemented by `[experimental_design](../../usingdakota/reference/method-bayes_calibration-experimental_design.html)`. In this algorithm, two models (usually, one high-fidelity and one low-fidelity) are provided to Dakota, each with their own `responses` section of the input script, and each of which is allowed its own `simulation_variance`. The variance specified in the `responses` block belonging to the high-fidelity model is applied to any <i> new </i> high-fidelity data that is produced by Dakota running the high-fidelity model. In the `experimental_design` algorithm, low-fidelity model responses are used during the calibration of the model parameters, the calculation of the mutual information, and the calculation of any posterior statistics after the algorithm is complete. The `simulation_variance` is applied to the low-fidelity model responses that are used in the calculation of the mutual information. See the User’s and Theory Manuals for more information.

**Examples**

The example below shows two `responses` blocks, one for the low-fidelity model and one for the high-fidelity model. Both contain `simulation_variance` commands that will apply to the low- and high-fidelity model responses, respectively.

    responses,
      id_responses = 'low-fidelity'
     calibration_terms = 1
     simulation_variance = 0.5
    
    responses,
      id_responses = 'high-fidelity'
      calibration_terms = 1
     calibration_data_file = 'dakota_bayes_expdesign.dat'
       freeform
       num_config_variables = 1
       num_experiments = 1
       experiment_variance_type = 'none'
     simulation_variance = 1.2


---

#### responses → calibration_terms → weights

# weights

Specify weights for each objective function

**Specification**

  * _Alias:_ calibration_weights least_squares_weights

  * _Arguments:_ REALLIST

  * _Default:_ equal weights

**Description**

Specifies relative emphasis through weights (multipliers) w on residual elements:

\\[f = \sum_{i=1}^{n} w_i R_i^2 = \sum_{i=1}^{n} w_i (y^{Model}_i - y^{Data}_i)^2\\]

Length: The `weights` must have length equal to `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`. Thus, when scalar and/or field responses are specified, the number of `weights` must equal the number of scalars plus the number of fields, _not_ the total elements in the fields.

_Default Behavior_ If weights are not specified, then each residual is given equal weighting.

_Usage Tips:_

Weights are applied as multipliers, scales as charateristic values / divisors.

When scaling is active, it is applied to calibration terms after any residual formation (accounting for experimental data and optionally measurement error covariance), and before any weights. See the equations in `[calibration_terms](../../usingdakota/reference/responses-calibration_terms.html)`.


---

### responses → descriptors

# descriptors

Labels for the responses

**Specification**

  * _Alias:_ response_descriptors

  * _Arguments:_ STRINGLIST

  * _Default:_ root strings plus numeric identifiers

**Description**

A list of strings which identify the responses. These are used in console and tabular output. Response descriptors are ordered by primary response functions (objective, calibration, or response functions), followed by inequality, then equality constraints, if present.

_Default Behavior_

The default descriptor strings use a response type-dependent root string plus a one-based numeric identifier:

  * Objective functions: `obj_fn_i`

  * Calibration terms: `least_sq_term_i`

  * Nonlinear inequality constraints: `nln_ineq_con_i`

  * Nonlinear equality constraints: `nln_eq_con_i`

  * Response functions: `response_fn_`

_Expected Output_ Dakota will label the various response functions in console and tabular output.

_Usage Tips_ When specifying descriptors for scalar and/or field responses, include as many descriptors as top-level scalar + field responses, e.g.,

    responses
      descriptors 'scalar1' 'scalar2' 'scalar3' 'field1' 'field2'
      response_functions 5
        scalar_responses 3
        field_responses 2
          field_lengths 42 24

Dakota will append a numeric identifier for each field entry, for a total of 42 field1_j and 24 field2_k in this example.

**Examples**

    responses
      descriptors 'cost' 'impact' 'safety'
      objective_functions 2
      nonlinear_inequality_constraints 1


---

### responses → id_responses

# id_responses

Name the responses block; helpful when there are multiple

**Topics**

block_identifier

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ use of last responses parsed

**Description**

The optional `id_responses` keyword accepts a string that uniquely identifies this responses block. A model can then use these responses by specifying the same string in its `responses_pointer` specification.

_Default Behavior_

If the `id_responses` specification is omitted, a particular responses specification will be used by a model only if that model does not include an `responses_pointer` and the responses block was the last (or only) one parsed.

_Usage Tips_

  * It is a best practice to always use explicit responses IDs and pointers to avoid confusion.

  * If only one responses block exists, then `id_responses` can be safely omitted from the responses block (and `responses_pointer` omitted from the model specification(s)), since there is no ambiguity.

**Examples**

For example, a model specification including

    model
      responses_pointer = 'R1'

will link to a response set with

    id_responses = 'R1'


---

### responses → metadata

# metadata

(Experimental) Labels for floating point response metadata

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ no metadata

**Description**

Specify the labels (field names) for floating point-valued metadata responses to be returned from the interface.

Dakota’s emerging metadata responses are distinct from the response quantities of interest explored by a Dakota `[method](../../usingdakota/reference/method.html)`. Some, such as simulation cost might be used by a method (such as method-multifidelity_sampling and related methods) to make algorithmic decisions. Other metadata might just be tabulated for user convenience.

_Usage Tips_

In a `[results_file](../../usingdakota/reference/interface-analysis_drivers-fork-results_file.html)`, values and labels for metadata should follow any function values, gradients, or Hessians returned by the simulation.

**Examples**

A responses specification where the Dakota method will be applied to quantities of interest stress and displacement, while metadata simulation cost in minutes and number of dead elements will be tabulated for each evaluation:

    responses
      descriptors 'stress' 'displacement'
      response_functions 2
      no_gradients
      no_hessians
      metadata 'simulation_cost_minutes' 'number_dead_elements'


---

### responses → mixed_gradients

# mixed_gradients

Gradients are needed and will be obtained from a mix of numerical and analytic sources

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [id_numerical_gradients](responses-mixed_gradients-id_numerical_gradients.html) | Identify which numerical gradient corresponds to which response  
Required | [id_analytic_gradients](responses-mixed_gradients-id_analytic_gradients.html) | Identify which analytical gradient corresponds to which response  
Optional | [method_source](responses-mixed_gradients-method_source.html) | Specify which finite difference routine is used  
Optional (Choose One) | Gradient Source | [dakota](responses-mixed_gradients-dakota.html) | (Default) Use internal Dakota finite differences algorithm  
[vendor](responses-mixed_gradients-vendor.html) | Use non-Dakota fd algorithm  
Optional | [interval_type](responses-mixed_gradients-interval_type.html) | Specify how to compute gradients and hessians  
Optional (Choose One) | Finite Difference Type | [forward](responses-mixed_gradients-forward.html) | (Default) Use forward differences  
[central](responses-mixed_gradients-central.html) | Use central differences  
Optional | [fd_step_size](responses-mixed_gradients-fd_step_size.html) | Step size used when computing gradients and Hessians  
  
**Description**

The `mixed_gradients` specification means that some gradient information is available directly from the simulation (analytic) whereas the rest will have to be finite differenced (numerical). This specification allows the user to make use of as much analytic gradient information as is available and then finite difference for the rest.

The `method_source`, `interval_type`, and `fd_gradient_step_size` specifications pertain to those functions listed by the `id_numerical_gradients` list.

**Examples**

For example, the objective function may be a simple analytic function of the design variables (e.g., weight) whereas the constraints are nonlinear implicit functions of complex analyses (e.g., maximum stress).


---

#### responses → mixed_gradients → central

# central

Use central differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

#### responses → mixed_gradients → dakota

# dakota

(Default) Use internal Dakota finite differences algorithm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [ignore_bounds](responses-mixed_gradients-dakota-ignore_bounds.html) | Do not respect bounds when computing gradients or Hessians  
Optional (Choose One) | Step Scaling | [relative](responses-mixed_gradients-dakota-relative.html) | (Default) Scale step size by the parameter value  
[absolute](responses-mixed_gradients-dakota-absolute.html) | Do not scale step-size  
[bounds](responses-mixed_gradients-dakota-bounds.html) | Scale step-size by the domain of the parameter  
  
**Description**

The `dakota` routine is the default since it can execute in parallel and exploit the concurrency in finite difference evaluations (see the discussion on [exploiting parallelism](../advanced/parallelcomputing.html#parallel-overview-cat)).

When the `method_source` is `dakota`, the user may also specify the type of scaling desired when determining the finite difference step size. The choices are `absolute`, `bounds`, and `relative`. For `absolute`, the step size will be applied as is. For `bounds`, it will be scaled by the range of each parameter. For `relative`, it will be scaled by the parameter value.


---

##### responses → mixed_gradients → dakota → absolute

# absolute

Do not scale step-size

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Description**

Do not scale step-size


---

##### responses → mixed_gradients → dakota → bounds

# bounds

Scale step-size by the domain of the parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step-size by the domain of the parameter


---

##### responses → mixed_gradients → dakota → ignore_bounds

# ignore_bounds

Do not respect bounds when computing gradients or Hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ bounds respected

**Description**

When Dakota computes gradients or Hessians by finite differences and the variables in question have bounds, it by default chooses finite-differencing steps that keep the variables within their specified bounds. Older versions of Dakota generally ignored bounds when computing finite differences. To restore the older behavior, one can add keyword <tt>ignore_bounds</tt> to the <tt>response</tt> specification when <tt>method_source dakota</tt> (or just <tt>dakota</tt>) is also specified.

In forward difference or backward difference computations, honoring bounds is straightforward.

To honor bounds when approximating \\(\partial f / \partial x_i\\) , i.e., component \\(i\\) of the gradient of \\(f\\) , by central differences, Dakota chooses two steps \\(h_1\\) and \\(h_2\\) with \\(h_1 \ne h_2\\) , such that \\(x + h_1 e_i\\) and \\(x + h_2 e_i\\) both satisfy the bounds, and then computes

\\[\frac{\partial f}{\partial x_i} ong \frac{h_2^2(f_1 - f_0) - h_1^2(f_2 - f_0)}{h_1 h_2 (h_2 - h_1)} ,\\]

with \\(f_0 = f(x)\\) , \\(f_1 = f(x + h_1 e_i)\\) , and \\(f_2 = f(x + h_2 e_i)\\) .


---

##### responses → mixed_gradients → dakota → relative

# relative

(Default) Scale step size by the parameter value

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step size by the parameter value


---

#### responses → mixed_gradients → fd_step_size

# fd_step_size

Step size used when computing gradients and Hessians

**Specification**

  * _Alias:_ fd_gradient_step_size

  * _Arguments:_ REALLIST

  * _Default:_ 0.001

**Description**

`fd_step_size` specifies the relative finite difference step size to be used in the computations. Either a single value may be entered for use with all parameters, or a list of step sizes may be entered, one for each parameter.

The latter option of a list of step sizes is only valid for use with the Dakota finite differencing routine. For Dakota with an interval scaling type of `absolute`, the differencing interval will be `fd_step_size`.

For Dakota with and interval scaling type of `bounds`, the differencing intervals are computed by multiplying `fd_step_size` with the range of the parameter. For Dakota (with an interval scaling type of `relative`), DOT, CONMIN, and OPT++, the differencing intervals are computed by multiplying the `fd_step_size` with the current parameter value. In this case, a minimum absolute differencing interval is needed when the current parameter value is close to zero. This prevents finite difference intervals for the parameter which are too small to distinguish differences in the response quantities being computed. Dakota, DOT, CONMIN, and OPT++ all use <tt>.01*fd_step_size</tt> as their minimum absolute differencing interval. With a <tt>fd_step_size = .001</tt>, for example, Dakota, DOT, CONMIN, and OPT++ will use intervals of .001*current value with a minimum interval of 1.e-5. NPSOL and NLSSOL use a different formula for their finite difference intervals: <tt>fd_step_size*(1+|current parameter value|)</tt>. This definition has the advantage of eliminating the need for a minimum absolute differencing interval since the interval no longer goes to zero as the current parameter value goes to zero.

ROL’s finite difference step size can not be controlled via Dakota. Therefore, `fd_step_size` will be ignored when ROL’s finite differencing routines are used (vendor FD gradients are specified). ROL’s differencing intervals are computed by multiplying the current parameter value with the square root of machine precision.


---

#### responses → mixed_gradients → forward

# forward

(Default) Use forward differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

See parent page for usage notes.


---

#### responses → mixed_gradients → id_analytic_gradients

# id_analytic_gradients

Identify which analytical gradient corresponds to which response

**Topics**

objective_function_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `id_analytic_gradients` list specifies by number the functions which have analytic gradients, and the `id_numerical_gradients` list specifies by number the functions which must use numerical gradients. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_gradients` and `id_numerical_gradients` lists.


---

#### responses → mixed_gradients → id_numerical_gradients

# id_numerical_gradients

Identify which numerical gradient corresponds to which response

**Topics**

objective_function_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `id_analytic_gradients` list specifies by number the functions which have analytic gradients, and the `id_numerical_gradients` list specifies by number the functions which must use numerical gradients. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_gradients` and `id_numerical_gradients` lists.


---

#### responses → mixed_gradients → interval_type

# interval_type

Specify how to compute gradients and hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

The `interval_type` setting is used to select between `forward` and `central` differences in the numerical gradient calculations. The `dakota`, DOT `vendor`, and OPT++ `vendor` routines have both forward and central differences available, the CONMIN, NL2SOL and ROL `vendor` routines support forward differences only, and the NPSOL and NLSSOL `vendor` routines start with forward differences and automatically switch to central differences as the iteration progresses (the user has no control over this). The following forward difference expression

\\[\nabla f ({\bf x}) ong \frac{f ({\bf x} + h {\bf e}_i) - f ({\bf x})}{h}\\]

and the following central difference expression

\\[\nabla f ({\bf x}) ong \frac{f ({\bf x} + h {\bf e}_i) - f ({\bf x} - h {\bf e}_i)}{2h}\\]

are used to estimate the \\(i^{th}\\) component of the gradient vector.


---

#### responses → mixed_gradients → method_source

# method_source

Specify which finite difference routine is used

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ dakota

**Description**

The `method_source` setting specifies the source of the finite differencing routine that will be used to compute the numerical gradients:

  * `dakota` (default)

  * `vendor`

`dakota` denotes Dakota’s internal finite differencing algorithm and `vendor` denotes the finite differencing algorithm supplied by the iterator package in use (DOT, CONMIN, NPSOL, NL2SOL, NLSSOL, ROL, and OPT++ each have their own internal finite differencing routines). The `dakota` routine is the default since it can execute in parallel and exploit the concurrency in finite difference evaluations (see the discussion on [exploiting parallelism](../advanced/parallelcomputing.html#parallel-overview-cat)).

However, the `vendor` setting can be desirable in some cases since certain libraries will modify their algorithm when the finite differencing is performed internally. Since the selection of the `dakota` routine hides the use of finite differencing from the optimizers (the optimizers are configured to accept user-supplied gradients, which some algorithms assume to be of analytic accuracy), the potential exists for the `vendor` setting to trigger the use of an algorithm more optimized for the higher expense and/or lower accuracy of finite-differencing. For example, NPSOL uses gradients in its line search when in user-supplied gradient mode (since it assumes they are inexpensive), but uses a value-based line search procedure when internally finite differencing. The use of a value-based line search will often reduce total expense in serial operations. However, in parallel operations, the use of gradients in the NPSOL line search (user-supplied gradient mode) provides excellent load balancing without need to resort to speculative optimization approaches.

In summary, then, the `dakota` routine is preferred for parallel optimization, and the `vendor` routine may be preferred for serial optimization in special cases.


---

#### responses → mixed_gradients → vendor

# vendor

Use non-Dakota fd algorithm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

### responses → mixed_hessians

# mixed_hessians

Hessians are needed and will be obtained from a mix of numerical, analytic, and “quasi” sources

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [id_numerical_hessians](responses-mixed_hessians-id_numerical_hessians.html) | Identify which numerical-Hessian corresponds to which response  
Optional (Choose One) | Step Scaling | [relative](responses-mixed_hessians-relative.html) | (Default) Scale step size by the parameter value  
[absolute](responses-mixed_hessians-absolute.html) | Do not scale step-size  
[bounds](responses-mixed_hessians-bounds.html) | Scale step-size by the domain of the parameter  
Optional (Choose One) | Finite Difference Type | [forward](responses-mixed_hessians-forward.html) | (Default) Use forward differences  
[central](responses-mixed_hessians-central.html) | Use central differences  
Optional | [id_quasi_hessians](responses-mixed_hessians-id_quasi_hessians.html) | Identify which quasi-Hessian corresponds to which response  
Optional | [id_analytic_hessians](responses-mixed_hessians-id_analytic_hessians.html) | Identify which analytical Hessian corresponds to which response  
  
**Description**

The `mixed_hessians` specification means that some Hessian information is available directly from the simulation (analytic) whereas the rest will have to be estimated by finite differences (numerical) or approximated by secant updating. As for mixed gradients, this specification allows the user to make use of as much analytic information as is available and then estimate/approximate the rest.

The `id_analytic_hessians` list specifies by number the functions which have analytic Hessians, and the `id_numerical_hessians` and `id_quasi_hessians` lists specify by number the functions which must use numerical Hessians and secant Hessian updates, respectively. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_hessians`, `id_numerical_hessians`, and `id_quasi_hessians` lists.

The `fd_hessian_step_size` and `bfgs`, `damped` `bfgs`, or `sr1` secant update selections are as described previously in `[responses](../../usingdakota/reference/responses.html)` and pertain to those functions listed by the `id_numerical_hessians` and `id_quasi_hessians` lists.


---

#### responses → mixed_hessians → absolute

# absolute

Do not scale step-size

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Description**

Do not scale step-size


---

#### responses → mixed_hessians → bounds

# bounds

Scale step-size by the domain of the parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step-size by the domain of the parameter


---

#### responses → mixed_hessians → central

# central

Use central differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

#### responses → mixed_hessians → forward

# forward

(Default) Use forward differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

See parent page for usage notes.


---

#### responses → mixed_hessians → id_analytic_hessians

# id_analytic_hessians

Identify which analytical Hessian corresponds to which response

**Topics**

objective_function_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

The `id_analytic_hessians` list specifies by number the functions which have analytic Hessians, and the `id_numerical_hessians` and `id_quasi_hessians` lists specify by number the functions which must use numerical Hessians and secant Hessian updates, respectively. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_hessians`, `id_numerical_hessians`, and `id_quasi_hessians` lists.


---

#### responses → mixed_hessians → id_numerical_hessians

# id_numerical_hessians

Identify which numerical-Hessian corresponds to which response

**Topics**

objective_function_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [fd_step_size](responses-mixed_hessians-id_numerical_hessians-fd_step_size.html) | Step size used when computing gradients and Hessians  
  
**Description**

The `id_analytic_hessians` list specifies by number the functions which have analytic Hessians, and the `id_numerical_hessians` and `id_quasi_hessians` lists specify by number the functions which must use numerical Hessians and secant Hessian updates, respectively. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_hessians`, `id_numerical_hessians`, and `id_quasi_hessians` lists.


---

##### responses → mixed_hessians → id_numerical_hessians → fd_step_size

# fd_step_size

Step size used when computing gradients and Hessians

**Specification**

  * _Alias:_ fd_hessian_step_size

  * _Arguments:_ REALLIST

  * _Default:_ 0.001 (forward), 0.002 (central)

**Description**

`fd_step_size` specifies the relative finite difference step size to be used in the computations. Either a single value may be entered for use with all parameters, or a list of step sizes may be entered, one for each parameter.

The latter option of a list of step sizes is only valid for use with the Dakota finite differencing routine. For Dakota with an interval scaling type of `absolute`, the differencing interval will be `fd_step_size`.

For Dakota with and interval scaling type of `bounds`, the differencing intervals are computed by multiplying `fd_step_size` with the range of the parameter. For Dakota (with an interval scaling type of `relative`), DOT, CONMIN, and OPT++, the differencing intervals are computed by multiplying the `fd_step_size` with the current parameter value. In this case, a minimum absolute differencing interval is needed when the current parameter value is close to zero. This prevents finite difference intervals for the parameter which are too small to distinguish differences in the response quantities being computed. Dakota, DOT, CONMIN, and OPT++ all use <tt>.01*fd_step_size</tt> as their minimum absolute differencing interval. With a <tt>fd_step_size = .001</tt>, for example, Dakota, DOT, CONMIN, and OPT++ will use intervals of .001*current value with a minimum interval of 1.e-5. NPSOL and NLSSOL use a different formula for their finite difference intervals: <tt>fd_step_size*(1+|current parameter value|)</tt>. This definition has the advantage of eliminating the need for a minimum absolute differencing interval since the interval no longer goes to zero as the current parameter value goes to zero.

ROL’s finite difference step size can not be controlled via Dakota. Therefore, `fd_step_size` will be ignored when ROL’s finite differencing routines are used (vendor FD gradients are specified). ROL’s differencing intervals are computed by multiplying the current parameter value with the square root of machine precision.


---

#### responses → mixed_hessians → id_quasi_hessians

# id_quasi_hessians

Identify which quasi-Hessian corresponds to which response

**Topics**

objective_function_pointer

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Quasi-Hessian Approximation | [bfgs](responses-mixed_hessians-id_quasi_hessians-bfgs.html) | Use BFGS method to compute quasi-hessians  
[sr1](responses-mixed_hessians-id_quasi_hessians-sr1.html) | Use the Symmetric Rank 1 update method to compute quasi-Hessians  
  
**Description**

The `id_analytic_hessians` list specifies by number the functions which have analytic Hessians, and the `id_numerical_hessians` and `id_quasi_hessians` lists specify by number the functions which must use numerical Hessians and secant Hessian updates, respectively. Each function identifier, from 1 through the total number of functions, must appear once and only once within the union of the `id_analytic_hessians`, `id_numerical_hessians`, and `id_quasi_hessians` lists.


---

##### responses → mixed_hessians → id_quasi_hessians → bfgs

# bfgs

Use BFGS method to compute quasi-hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [damped](responses-mixed_hessians-id_quasi_hessians-bfgs-damped.html) | Numerical safeguarding for BFGS updates  
  
**Description**

Broyden-Fletcher-Goldfarb-Shanno (BFGS) update will be used to compute quasi-Hessians.

\\[B_{k+1} = B_{k} - \frac{B_k s_k s_k^T B_k}{s_k^T B_k s_k} + \frac{y_k y_k^T}{y_k^T s_k}\\]

where \\(B_k\\) is the \\(k^{th}\\) approximation to the Hessian, \\(s_k = x_{k+1} - x_k\\) is the step and \\(y_k = \nabla f_{k+1} - \nabla f_k\\) is the corresponding yield in the gradients.

_Notes_

  * Initial scaling of \\(\frac{y_k^T y_k}{y_k^T s_k} I\\) is used for \\(B_0\\) prior to the first update.

  * Numerical safeguarding is used to protect against numerically small denominators within the updates.

  * This safeguarding skips the update if \\(|y_k^T s_k| < 10^{-6} s_k^T B_k s_k\\)

  * Additional safeguarding can be added using the `damped` option, which utilizes an alternative damped BFGS update when the curvature condition \\(y_k^T s_k > 0\\) is nearly violated.


---

###### responses → mixed_hessians → id_quasi_hessians → bfgs → damped

# damped

Numerical safeguarding for BFGS updates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ undamped BFGS

**Description**

See parent page.


---

##### responses → mixed_hessians → id_quasi_hessians → sr1

# sr1

Use the Symmetric Rank 1 update method to compute quasi-Hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The Symmetric Rank 1 (SR1) update (specified with the keyword `sr1`) will be used to compute quasi-Hessians.

\\[B_{k+1} = B_k + \frac{(y_k - B_k s_k)(y_k - B_k s_k)^T}{(y_k - B_k s_k)^T s_k}\\]

where \\(B_k\\) is the \\(k^{th}\\) approximation to the Hessian, \\(s_k = x_{k+1} - x_k\\) is the step and \\(y_k = \nabla f_{k+1} - \nabla f_k\\) is the corresponding yield in the gradients.

_Notes_

  * Initial scaling of \\(\frac{y_k^T y_k}{y_k^T s_k} I\\) is used for \\(B_0\\) prior to the first update.

  * Numerical safeguarding is used to protect against numerically small denominators within the updates.

  * This safeguarding skips the update if \\(|(y_k - B_k s_k)^T s_k| < 10^{-6} ||s_k||_2 ||y_k - B_k s_k||_2\\)


---

#### responses → mixed_hessians → relative

# relative

(Default) Scale step size by the parameter value

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step size by the parameter value


---

### responses → no_gradients

# no_gradients

Gradients will not be used

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `no_gradients` specification means that gradient information is not needed in the study. Therefore, it will neither be retrieved from the simulation nor computed with finite differences. The `no_gradients` keyword is a complete specification for this case.


---

### responses → no_hessians

# no_hessians

Hessians will not be used

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The `no_hessians` specification means that the method does not require Dakota to manage the computation of any Hessian information. Therefore, it will neither be retrieved from the simulation nor computed by Dakota. The `no_hessians` keyword is a complete specification for this case. Note that, in some cases, Hessian information may still be being approximated internal to an algorithm (e.g., within a quasi-Newton optimizer such as `optpp_q_newton`); however, Dakota has no direct involvement in this process and the responses specification need not include it.


---

### responses → numerical_gradients

# numerical_gradients

Gradients are needed and will be approximated by finite differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [method_source](responses-numerical_gradients-method_source.html) | Specify which finite difference routine is used  
Optional (Choose One) | Gradient Source | [dakota](responses-numerical_gradients-dakota.html) | (Default) Use internal Dakota finite differences algorithm  
[vendor](responses-numerical_gradients-vendor.html) | Use non-Dakota fd algorithm  
Optional | [interval_type](responses-numerical_gradients-interval_type.html) | Specify how to compute gradients and hessians  
Optional (Choose One) | Finite Difference Type | [forward](responses-numerical_gradients-forward.html) | (Default) Use forward differences  
[central](responses-numerical_gradients-central.html) | Use central differences  
Optional | [fd_step_size](responses-numerical_gradients-fd_step_size.html) | Step size used when computing gradients and Hessians  
  
**Description**

The `numerical_gradients` specification means that gradient information is needed and will be computed with finite differences using either the native or one of the vendor finite differencing routines.


---

#### responses → numerical_gradients → central

# central

Use central differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

#### responses → numerical_gradients → dakota

# dakota

(Default) Use internal Dakota finite differences algorithm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [ignore_bounds](responses-numerical_gradients-dakota-ignore_bounds.html) | Do not respect bounds when computing gradients or Hessians  
Optional (Choose One) | Step Scaling | [relative](responses-numerical_gradients-dakota-relative.html) | (Default) Scale step size by the parameter value  
[absolute](responses-numerical_gradients-dakota-absolute.html) | Do not scale step-size  
[bounds](responses-numerical_gradients-dakota-bounds.html) | Scale step-size by the domain of the parameter  
  
**Description**

The `dakota` routine is the default since it can execute in parallel and exploit the concurrency in finite difference evaluations (see the discussion on [exploiting parallelism](../advanced/parallelcomputing.html#parallel-overview-cat)).

When the `method_source` is `dakota`, the user may also specify the type of scaling desired when determining the finite difference step size. The choices are `absolute`, `bounds`, and `relative`. For `absolute`, the step size will be applied as is. For `bounds`, it will be scaled by the range of each parameter. For `relative`, it will be scaled by the parameter value.


---

##### responses → numerical_gradients → dakota → absolute

# absolute

Do not scale step-size

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Description**

Do not scale step-size


---

##### responses → numerical_gradients → dakota → bounds

# bounds

Scale step-size by the domain of the parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step-size by the domain of the parameter


---

##### responses → numerical_gradients → dakota → ignore_bounds

# ignore_bounds

Do not respect bounds when computing gradients or Hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ bounds respected

**Description**

When Dakota computes gradients or Hessians by finite differences and the variables in question have bounds, it by default chooses finite-differencing steps that keep the variables within their specified bounds. Older versions of Dakota generally ignored bounds when computing finite differences. To restore the older behavior, one can add keyword <tt>ignore_bounds</tt> to the <tt>response</tt> specification when <tt>method_source dakota</tt> (or just <tt>dakota</tt>) is also specified.

In forward difference or backward difference computations, honoring bounds is straightforward.

To honor bounds when approximating \\(\partial f / \partial x_i\\) , i.e., component \\(i\\) of the gradient of \\(f\\) , by central differences, Dakota chooses two steps \\(h_1\\) and \\(h_2\\) with \\(h_1 \ne h_2\\) , such that \\(x + h_1 e_i\\) and \\(x + h_2 e_i\\) both satisfy the bounds, and then computes

\\[\frac{\partial f}{\partial x_i} ong \frac{h_2^2(f_1 - f_0) - h_1^2(f_2 - f_0)}{h_1 h_2 (h_2 - h_1)} ,\\]

with \\(f_0 = f(x)\\) , \\(f_1 = f(x + h_1 e_i)\\) , and \\(f_2 = f(x + h_2 e_i)\\) .


---

##### responses → numerical_gradients → dakota → relative

# relative

(Default) Scale step size by the parameter value

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step size by the parameter value


---

#### responses → numerical_gradients → fd_step_size

# fd_step_size

Step size used when computing gradients and Hessians

**Specification**

  * _Alias:_ fd_gradient_step_size

  * _Arguments:_ REALLIST

  * _Default:_ 0.001

**Description**

`fd_step_size` specifies the relative finite difference step size to be used in the computations. Either a single value may be entered for use with all parameters, or a list of step sizes may be entered, one for each parameter.

The latter option of a list of step sizes is only valid for use with the Dakota finite differencing routine. For Dakota with an interval scaling type of `absolute`, the differencing interval will be `fd_step_size`.

For Dakota with and interval scaling type of `bounds`, the differencing intervals are computed by multiplying `fd_step_size` with the range of the parameter. For Dakota (with an interval scaling type of `relative`), DOT, CONMIN, and OPT++, the differencing intervals are computed by multiplying the `fd_step_size` with the current parameter value. In this case, a minimum absolute differencing interval is needed when the current parameter value is close to zero. This prevents finite difference intervals for the parameter which are too small to distinguish differences in the response quantities being computed. Dakota, DOT, CONMIN, and OPT++ all use <tt>.01*fd_step_size</tt> as their minimum absolute differencing interval. With a <tt>fd_step_size = .001</tt>, for example, Dakota, DOT, CONMIN, and OPT++ will use intervals of .001*current value with a minimum interval of 1.e-5. NPSOL and NLSSOL use a different formula for their finite difference intervals: <tt>fd_step_size*(1+|current parameter value|)</tt>. This definition has the advantage of eliminating the need for a minimum absolute differencing interval since the interval no longer goes to zero as the current parameter value goes to zero.

ROL’s finite difference step size can not be controlled via Dakota. Therefore, `fd_step_size` will be ignored when ROL’s finite differencing routines are used (vendor FD gradients are specified). ROL’s differencing intervals are computed by multiplying the current parameter value with the square root of machine precision.


---

#### responses → numerical_gradients → forward

# forward

(Default) Use forward differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

See parent page for usage notes.


---

#### responses → numerical_gradients → interval_type

# interval_type

Specify how to compute gradients and hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

The `interval_type` setting is used to select between `forward` and `central` differences in the numerical gradient calculations. The `dakota`, DOT `vendor`, and OPT++ `vendor` routines have both forward and central differences available, the CONMIN, NL2SOL and ROL `vendor` routines support forward differences only, and the NPSOL and NLSSOL `vendor` routines start with forward differences and automatically switch to central differences as the iteration progresses (the user has no control over this). The following forward difference expression

\\[\nabla f ({\bf x}) ong \frac{f ({\bf x} + h {\bf e}_i) - f ({\bf x})}{h}\\]

and the following central difference expression

\\[\nabla f ({\bf x}) ong \frac{f ({\bf x} + h {\bf e}_i) - f ({\bf x} - h {\bf e}_i)}{2h}\\]

are used to estimate the \\(i^{th}\\) component of the gradient vector.


---

#### responses → numerical_gradients → method_source

# method_source

Specify which finite difference routine is used

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ dakota

**Description**

The `method_source` setting specifies the source of the finite differencing routine that will be used to compute the numerical gradients:

  * `dakota` (default)

  * `vendor`

`dakota` denotes Dakota’s internal finite differencing algorithm and `vendor` denotes the finite differencing algorithm supplied by the iterator package in use (DOT, CONMIN, NPSOL, NL2SOL, NLSSOL, ROL, and OPT++ each have their own internal finite differencing routines). The `dakota` routine is the default since it can execute in parallel and exploit the concurrency in finite difference evaluations (see the discussion on [exploiting parallelism](../advanced/parallelcomputing.html#parallel-overview-cat)).

However, the `vendor` setting can be desirable in some cases since certain libraries will modify their algorithm when the finite differencing is performed internally. Since the selection of the `dakota` routine hides the use of finite differencing from the optimizers (the optimizers are configured to accept user-supplied gradients, which some algorithms assume to be of analytic accuracy), the potential exists for the `vendor` setting to trigger the use of an algorithm more optimized for the higher expense and/or lower accuracy of finite-differencing. For example, NPSOL uses gradients in its line search when in user-supplied gradient mode (since it assumes they are inexpensive), but uses a value-based line search procedure when internally finite differencing. The use of a value-based line search will often reduce total expense in serial operations. However, in parallel operations, the use of gradients in the NPSOL line search (user-supplied gradient mode) provides excellent load balancing without need to resort to speculative optimization approaches.

In summary, then, the `dakota` routine is preferred for parallel optimization, and the `vendor` routine may be preferred for serial optimization in special cases.


---

#### responses → numerical_gradients → vendor

# vendor

Use non-Dakota fd algorithm

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

### responses → numerical_hessians

# numerical_hessians

Hessians are needed and will be approximated by finite differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [fd_step_size](responses-numerical_hessians-fd_step_size.html) | Step size used when computing gradients and Hessians  
Optional (Choose One) | Step Scaling | [relative](responses-numerical_hessians-relative.html) | (Default) Scale step size by the parameter value  
[absolute](responses-numerical_hessians-absolute.html) | Do not scale step-size  
[bounds](responses-numerical_hessians-bounds.html) | Scale step-size by the domain of the parameter  
Optional (Choose One) | Finite Difference Type | [forward](responses-numerical_hessians-forward.html) | (Default) Use forward differences  
[central](responses-numerical_hessians-central.html) | Use central differences  
  
**Description**

The `numerical_hessians` specification means that Hessian information is needed and will be computed with finite differences using either first-order gradient differencing (for the cases of `analytic_gradients` or for the functions identified by `id_analytic_gradients` in the case of `mixed_gradients`) or first- or second-order function value differencing (all other gradient specifications). In the former case, the following expression

\\[\nabla^2 f ({\bf x})_i ong \frac{\nabla f ({\bf x} + h {\bf e}_i) - \nabla f ({\bf x})}{h}\\]

estimates the \\(i^{th}\\) Hessian column, and in the latter case, the following expressions

\\[\nabla^2 f ({\bf x})_{i,j} ong ````\frac{f({\bf x} + h_i {\bf e}_i + h_j {\bf e}_j) - f({\bf x} + h_i {\bf e}_i) - f({\bf x} - h_j {\bf e}_j) + f({\bf x})}{h_i h_j}\\]

and

\\[\nabla^2 f ({\bf x})_{i,j} ong ````\frac{f({\bf x} + h {\bf e}_i + h {\bf e}_j) - f({\bf x} + h {\bf e}_i - h {\bf e}_j) - f({\bf x} - h {\bf e}_i + h {\bf e}_j) + f({\bf x} - h {\bf e}_i - h {\bf e}_j)}{4h^2}\\]

provide first- and second-order estimates of the \\(ij^{th}\\) Hessian term. Prior to Dakota 5.0, Dakota always used second-order estimates. In Dakota 5.0 and newer, the default is to use first-order estimates (which honor bounds on the variables and require only about a quarter as many function evaluations as do the second-order estimates), but specifying `central` after `numerical_hessians` causes Dakota to use the old second-order estimates, which do not honor bounds. In optimization algorithms that use Hessians, there is little reason to use second-order differences in computing Hessian approximations.


---

#### responses → numerical_hessians → absolute

# absolute

Do not scale step-size

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ relative

**Description**

Do not scale step-size


---

#### responses → numerical_hessians → bounds

# bounds

Scale step-size by the domain of the parameter

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step-size by the domain of the parameter


---

#### responses → numerical_hessians → central

# central

Use central differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

See parent page for usage notes.


---

#### responses → numerical_hessians → fd_step_size

# fd_step_size

Step size used when computing gradients and Hessians

**Specification**

  * _Alias:_ fd_hessian_step_size

  * _Arguments:_ REALLIST

  * _Default:_ 0.001 (forward), 0.002 (central)

**Description**

`fd_step_size` specifies the relative finite difference step size to be used in the computations. Either a single value may be entered for use with all parameters, or a list of step sizes may be entered, one for each parameter.

The latter option of a list of step sizes is only valid for use with the Dakota finite differencing routine. For Dakota with an interval scaling type of `absolute`, the differencing interval will be `fd_step_size`.

For Dakota with and interval scaling type of `bounds`, the differencing intervals are computed by multiplying `fd_step_size` with the range of the parameter. For Dakota (with an interval scaling type of `relative`), DOT, CONMIN, and OPT++, the differencing intervals are computed by multiplying the `fd_step_size` with the current parameter value. In this case, a minimum absolute differencing interval is needed when the current parameter value is close to zero. This prevents finite difference intervals for the parameter which are too small to distinguish differences in the response quantities being computed. Dakota, DOT, CONMIN, and OPT++ all use <tt>.01*fd_step_size</tt> as their minimum absolute differencing interval. With a <tt>fd_step_size = .001</tt>, for example, Dakota, DOT, CONMIN, and OPT++ will use intervals of .001*current value with a minimum interval of 1.e-5. NPSOL and NLSSOL use a different formula for their finite difference intervals: <tt>fd_step_size*(1+|current parameter value|)</tt>. This definition has the advantage of eliminating the need for a minimum absolute differencing interval since the interval no longer goes to zero as the current parameter value goes to zero.

ROL’s finite difference step size can not be controlled via Dakota. Therefore, `fd_step_size` will be ignored when ROL’s finite differencing routines are used (vendor FD gradients are specified). ROL’s differencing intervals are computed by multiplying the current parameter value with the square root of machine precision.


---

#### responses → numerical_hessians → forward

# forward

(Default) Use forward differences

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ forward

**Description**

See parent page for usage notes.


---

#### responses → numerical_hessians → relative

# relative

(Default) Scale step size by the parameter value

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Scale step size by the parameter value


---

### responses → objective_functions

# objective_functions

Response type suitable for optimization

**Specification**

  * _Alias:_ num_objective_functions

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [sense](responses-objective_functions-sense.html) | Whether to minimize or maximize each objective function  
Optional | [primary_scale_types](responses-objective_functions-primary_scale_types.html) | How to scale each objective function  
Optional | [primary_scales](responses-objective_functions-primary_scales.html) | Characteristic values to scale each objective function  
Optional | [weights](responses-objective_functions-weights.html) | Specify weights for each objective function  
Optional | [nonlinear_inequality_constraints](responses-objective_functions-nonlinear_inequality_constraints.html) | Group to specify nonlinear inequality constraints  
Optional | [nonlinear_equality_constraints](responses-objective_functions-nonlinear_equality_constraints.html) | Group to specify nonlinear equality constraints  
Optional | [scalar_objectives](responses-objective_functions-scalar_objectives.html) | Number of scalar objective functions  
Optional | [field_objectives](responses-objective_functions-field_objectives.html) | Number of field objective functions  
  
**Description**

Specifies the number (1 or more) of objective functions \\(f_j\\) returned to Dakota for use in the general optimization problem formulation:

\\[\begin{split}\begin{eqnarray*} \hbox{minimize:} & & f(\mathbf{x}) = \sum_j{w_j f_j} \\\ & & \mathbf{x} \in \Re^{n} \\\ \hbox{subject to:} & & \mathbf{g}_{L} \leq \mathbf{g(x)} \leq \mathbf{g}_U \\\ & & \mathbf{h(x)}=\mathbf{h}_{t} \\\ & & \mathbf{a}_{L} \leq \mathbf{A}_i\mathbf{x} \leq \mathbf{a}_U \\\ & & \mathbf{A}_{e}\mathbf{x}=\mathbf{a}_{t} \\\ & & \mathbf{x}_{L} \leq \mathbf{x} \leq \mathbf{x}_U \end{eqnarray*}\end{split}\\]

Unless `[sense](../../usingdakota/reference/responses-objective_functions-sense.html)` is specified, Dakota will minimize the objective functions.

The keywords `[nonlinear_inequality_constraints](../../usingdakota/reference/responses-objective_functions-nonlinear_inequality_constraints.html)` and `[nonlinear_equality_constraints](../../usingdakota/reference/responses-objective_functions-nonlinear_equality_constraints.html)` specify the number of nonlinear inequality constraints _g_ , and nonlinear equality constraints _h_ , respectively. When interfacing to external applications, the responses must be returned to Dakota in this order in the `[results_file](../../usingdakota/reference/interface-analysis_drivers-fork-results_file.html)` :

  1. objective functions

  2. nonlinear_inequality_constraints

  3. nonlinear_equality_constraints

An optimization problem’s linear constraints are provided to the solver at startup only and do not need to be included in the data returned on every function evaluation. Linear constraints are therefore specified in the `[variables](../../usingdakota/reference/variables.html)` block through the `[linear_inequality_constraint_matrix](../../usingdakota/reference/variables-linear_inequality_constraint_matrix.html)` \\(A_i\\) and `[linear_equality_constraint_matrix](../../usingdakota/reference/variables-linear_equality_constraint_matrix.html)` \\(A_e\\) .

Lower and upper bounds on the design variables _x_ are also specified in the `[variables](../../usingdakota/reference/variables.html)` block.

The optional keywords relate to scaling the objective functions (for better numerical results), formulating the problem as minimization or maximization, and dealing with multiple objective functions through `[weights](../../usingdakota/reference/responses-objective_functions-weights.html)` _w_. If scaling is used, it is applied before multi-objective weighted sums are formed, so, e.g, when both weighting and characteristic value scaling are present the ultimate objective function would be:

\\[f = \sum_{j=1}^{n} w_{j} \frac{ f_{j} }{ s_j }\\]


---

#### responses → objective_functions → field_objectives

# field_objectives

Number of field objective functions

**Specification**

  * _Alias:_ num_field_objectives

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lengths](responses-objective_functions-field_objectives-lengths.html) | Lengths of field responses  
Optional | [num_coordinates_per_field](responses-objective_functions-field_objectives-num_coordinates_per_field.html) | Number of independent coordinates for field responses  
Optional | [read_field_coordinates](responses-objective_functions-field_objectives-read_field_coordinates.html) | Add context to data: flag to indicate that field coordinates should be read  
  
**Description**

This keyword describes the number of field objective functions. A field function is a set of related response values collected over a range of independent coordinate values which may or may not be specified by the user. For example, voltage over time would be a field function, where voltage is the `field_objective` and time is the independent coordinate. Similarly, temperature over time and space would be a field response, where the independent coordinates would be both time and spatial coordinates such as (x,y) or (x,y,z), depending on the application. The main difference between scalar objectives and field objectives is that for field data, we plan to implement methods that take advantage of the correlation or relationship between the field values.

Note that if there is one `field_objective`, and it has length 100 (meaning 100 values), then the user’s simulation code must return 100 values. Also, if there are both scalar and field objectives, the user should specify the number of scalar objectives as `scalar_objectives`. If there are only field objectives, it still is necessary to specify both `objective_functions` = NN and `field_objectives` = NN, where NN is the number of field objectives.

Objective functions are responses that are used with optimization methods in Dakota. Currently, each term in a field objective is added to the total objective function presented to the optimizer. For example, if you have one field objective with 100 terms (e.g. a time-temperature trace with 100 time points and 100 corresponding temperature points), the 100 temperature values will be added to create the overall objective.


---

##### responses → objective_functions → field_objectives → lengths

# lengths

Lengths of field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the lengths of each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `lengths` = 50 200, indicating that the first field response has 50 field elements but the second one has 200. The coordinate values (e.g. the independent variables) corresponding to these field responses are read in files labeled response_descriptor.coords.


---

##### responses → objective_functions → field_objectives → num_coordinates_per_field

# num_coordinates_per_field

Number of independent coordinates for field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the number of independent coordinates for each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `num_coordinates_per_field` = 2 1 means that the first field response has two sets of independent coordinates (perhaps x, y locations), but the second response only has one (for example, time where the field response is only dependent upon time). The actual coordinate values (e.g. the independent variables) corresponding to these field responses are defined in a file call response_descriptor.coords, where response_descriptor is the name of the individual field.


---

##### responses → objective_functions → field_objectives → read_field_coordinates

# read_field_coordinates

Add context to data: flag to indicate that field coordinates should be read

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Field coordinates specify independent variables (e.g. spatial or temporal coordinates) upon which

the field depends. For example, the voltage level above might be a function of time, so time is the field coordinate. If the user has field coordinates to read, they need to specify `read_field_coordinates`. The field coordinates will then be read from a file named response_descriptor.coords, where response_descriptor is the user-provided descriptor for the field response. The number of columns in the coords file should be equal to the number of field coordinates.


---

#### responses → objective_functions → nonlinear_equality_constraints

# nonlinear_equality_constraints

Group to specify nonlinear equality constraints

**Topics**

nonlinear_constraints

**Specification**

  * _Alias:_ num_nonlinear_equality_constraints

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [targets](responses-objective_functions-nonlinear_equality_constraints-targets.html) | Target values for the nonlinear equality constraint  
Optional | [scale_types](responses-objective_functions-nonlinear_equality_constraints-scale_types.html) | How to scale each nonlinear constraint  
Optional | [scales](responses-objective_functions-nonlinear_equality_constraints-scales.html) | Characteristic values to scale each nonlinear constraint  
  
**Description**

Specifies the number of nonlinear equality constraint functions returned by the interface.

The `targets` specification provides the targets for nonlinear equalities of the form

\\[h(x) = h_t\\]

and the defaults for the equality targets enforce a value of `0`. for each constraint

\\[h(x) = 0.0\\]

The `scale_types` and `scales` keywords are related to scaling of \\(h \left( x \right)\\) . See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_equality_constraints → scale_types

# scale_types

How to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_equality_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ no scaling

**Description**

Each string in `scale_types` indicates the scaling type for each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * `value` \- characteristic value by which nonlinear constraint values will be divided. If this is chosen, then `scales` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * `auto` \- automatic scaling based on bounds (inequalities) or targets (equalities)

  * `log` \- logarithmic scaling (can be used together with `scales`, which can be helpfully used to negate values prior to log transformation)

If a single string is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a string must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_equality_constraints → scales

# scales

Characteristic values to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_equality_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `scales` is a nonzero characteristic value to be used in scaling each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `scale_types` of `value` and optional for `log`. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative response functions.

If a single scale value is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a value must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

When `scales` are specified, but not `scale_types`, the scaling type is assumed to be ‘value’ for this set of nonlinear inequality (equality) constraints.

Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_equality_constraints → targets

# targets

Target values for the nonlinear equality constraint

**Specification**

  * _Alias:_ nonlinear_equality_targets

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

The `targets` specification provides the targets for nonlinear equalities of the form

\\[g(x) = g_t\\]

and the defaults for the equality targets enforce a value of `0`.0 for each constraint:

\\[g(x) = 0.0\\]


---

#### responses → objective_functions → nonlinear_inequality_constraints

# nonlinear_inequality_constraints

Group to specify nonlinear inequality constraints

**Topics**

nonlinear_constraints

**Specification**

  * _Alias:_ num_nonlinear_inequality_constraints

  * _Arguments:_ INTEGER

  * _Default:_ 0

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [lower_bounds](responses-objective_functions-nonlinear_inequality_constraints-lower_bounds.html) | Specify minimum values  
Optional | [upper_bounds](responses-objective_functions-nonlinear_inequality_constraints-upper_bounds.html) | Specify maximium values  
Optional | [scale_types](responses-objective_functions-nonlinear_inequality_constraints-scale_types.html) | How to scale each nonlinear constraint  
Optional | [scales](responses-objective_functions-nonlinear_inequality_constraints-scales.html) | Characteristic values to scale each nonlinear constraint  
  
**Description**

Specifies the number of nonlinear inequality constraint functions returned by the interface.

The `lower_bounds` and `upper_bounds` specifications provide the lower and upper bounds for 2-sided nonlinear inequalities of the form

\\[g_l \leq g(x) \leq g_u\\]

When constraint bounds are not specified, the problem is assumed to have one-sided inequalities bounded above by zero:

\\[g(x) \leq 0.0.\\]

This provides backwards compatibility with previous Dakota versions.

In a user bounds specification, any upper bound values greater than ```+bigRealBoundSize (1.e+30, as defined in Minimizer) are treated as +infinity and any lower bound values less than ``-bigRealBoundSize` are treated as -infinity. This feature is commonly used to drop one of the bounds in order to specify a 1-sided constraint (just as the default lower bounds drop out since `-DBL_MAX` <

`-bigRealBoundSize`). The same approach is used for nonexistent linearinequality bounds and for nonexistent design variable bounds.

The `scale_types` and `scales` keywords are related to scaling of \\(g \left( x \right)\\) . See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_inequality_constraints → lower_bounds

# lower_bounds

Specify minimum values

**Specification**

  * _Alias:_ nonlinear_inequality_lower_bounds

  * _Arguments:_ REALLIST

  * _Default:_ vector values = -infinity

**Description**

Specify minimum values


---

##### responses → objective_functions → nonlinear_inequality_constraints → scale_types

# scale_types

How to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_inequality_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ no scaling

**Description**

Each string in `scale_types` indicates the scaling type for each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

The options are:

  * `value` \- characteristic value by which nonlinear constraint values will be divided. If this is chosen, then `scales` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * `auto` \- automatic scaling based on bounds (inequalities) or targets (equalities)

  * `log` \- logarithmic scaling (can be used together with `scales`, which can be helpfully used to negate values prior to log transformation)

If a single string is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a string must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_inequality_constraints → scales

# scales

Characteristic values to scale each nonlinear constraint

**Specification**

  * _Alias:_ nonlinear_inequality_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `scales` is a nonzero characteristic value to be used in scaling each nonlinear inequality (equality) constraint. They only have effect when the associated method specifies `scaling`.

This keyword is required for `scale_types` of `value` and optional for `log`. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative response functions.

If a single scale value is specified it will apply to all of the nonlinear inequality (equality) constraints. Otherwise a value must be specified for each nonlinear inequality (equality) constraint.

_Usage Tips:_

When `scales` are specified, but not `scale_types`, the scaling type is assumed to be ‘value’ for this set of nonlinear inequality (equality) constraints.

Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

See the scaling information under specific methods, e.g., method-*-scaling for details on how to use this keyword.


---

##### responses → objective_functions → nonlinear_inequality_constraints → upper_bounds

# upper_bounds

Specify maximium values

**Specification**

  * _Alias:_ nonlinear_inequality_upper_bounds

  * _Arguments:_ REALLIST

  * _Default:_ vector values = 0 .

**Description**

Specify maximium values


---

#### responses → objective_functions → primary_scale_types

# primary_scale_types

How to scale each objective function

**Specification**

  * _Alias:_ objective_function_scale_types

  * _Arguments:_ STRINGLIST

  * _Default:_ no scaling

**Description**

Each string in `primary_scale_types` indicates the scaling type for each objective function. They only have effect when the associated method specifies `scaling`.

The options are:

  * <tt>’value’</tt> \- characteristic value by which response functions will be divided. If this is chosen, then `[primary_scales](../../usingdakota/reference/responses-objective_functions-primary_scales.html)` must also be specified; ‘value’ is assumed if scales are given without `scale_types`

  * <tt>’log’</tt> \- logarithmic scaling (can be used together with `[primary_scales](../../usingdakota/reference/responses-objective_functions-primary_scales.html)`, which can be helpfully used to negate values prior to log transformation)

(Automatic scaling is disallowed due to lack of bounds or targets for primary responses.)

If a single string is specified it will apply to all objective_functions. Otherwise the length of `primary_scale_types` must have length equal to `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`. Thus, when scalar and/or field responses are specified, the number of `primary_scale_types` must equal the number of scalars plus the number of fields, _not_ the total elements in the fields.

_Usage Tips:_

When scaling is active, it is applied to objective functions prior to any `[weights](../../usingdakota/reference/responses-objective_functions-weights.html)` and multi-objective sum formation.

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

#### responses → objective_functions → primary_scales

# primary_scales

Characteristic values to scale each objective function

**Specification**

  * _Alias:_ objective_function_scales

  * _Arguments:_ REALLIST

  * _Default:_ 1.0 (no scaling)

**Description**

Each real value in `primary_scales` is a nonzero characteristic value to be used in scaling each objective function. They only have effect when the associated method specifies `scaling`.

This keyword is required for `[primary_scale_types](../../usingdakota/reference/responses-objective_functions-primary_scale_types.html)` of <tt>’value’</tt> and optional for <tt>’log’</tt>. When specified in conjunction with log, scale values are applied prior to the logarithm, to permit log scaling of strictly negative response functions.

Length: When specified, `primary_scales` must have length one of:

  * One (the single value will be applied to each objective function); or

  * Number of response groups ( `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`), so the number of scalars plus the number of fields when fields are present; or

  * Total number of response elements, so the number of scalar responses plus the sum of the lengths of the fields.

_Usage Tips:_

When `primary_scales` are specified, but not `[primary_scale_types](../../usingdakota/reference/responses-objective_functions-primary_scale_types.html)`, the scaling type is assumed to be ‘value’ for all primary response functions.

Use scale values of 1.0 to selectively avoid scaling a subset of response functions.

When scaling is active, it is applied to objective functions prior to any `[weights](../../usingdakota/reference/responses-objective_functions-weights.html)` and multi-objective sum formation. See the equations in `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`.

See the scaling information under specific methods, e.g., `method-*-scaling` for details on how to use this keyword.


---

#### responses → objective_functions → scalar_objectives

# scalar_objectives

Number of scalar objective functions

**Specification**

  * _Alias:_ num_scalar_objectives

  * _Arguments:_ INTEGER

**Description**

This keyword describes the number of scalar objective functions. It is meant to be used in conjunction with `field_objectives`, which describes the number of field objectives functions. The total number of objective functions, both scalar and field, is given by `objective_functions`. If only scalar objective functions are specified, it is not necessary to specify the number of scalar terms explicitly: one can simply say `objective_functions` = 5 and get 5 scalar objectives. However, if there are three scalar objectives and 2 field objectives, then `objective_functions` = 5 but `scalar_objectives` = 3 and `field_objectives` = 2.

Objective functions are responses that are used with optimization methods in Dakota. Currently, each term in a field objective is added to the total objective function presented to the optimizer. For example, if you have one field objective with 100 terms (e.g. a time-temperature trace with 100 time points and 100 corresponding temperature points), the 100 temperature values will be added to create the overall objective.


---

#### responses → objective_functions → sense

# sense

Whether to minimize or maximize each objective function

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRINGLIST

  * _Default:_ vector values = ‘minimize’

**Description**

The `sense` keyword is used to declare whether each objective function should be minimized or maximized. The argument options are:

  * `"minimization"` (can be abbreviated to “min”)

  * `"maximization"` (can be abbreviated to “max”)

If a single string is specified it will apply to each objective function. Otherwise, the number of strings should either be equal to the number of objective functions ( `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`). Thus, when scalar and/or field responses are specified, the number of `sense` strings must equal the number of scalars plus the number of fields, _not_ the total elements in the fields.


---

#### responses → objective_functions → weights

# weights

Specify weights for each objective function

**Specification**

  * _Alias:_ multi_objective_weights

  * _Arguments:_ REALLIST

  * _Default:_ equal weights

**Description**

For multi-objective optimization problems (where the number of objective functions is greater than 1), then a `weights` specification provides a simple weighted-sum approach to combining multiple objectives into a single objective:

\\[f = \sum_{i=1}^{n} w_{i}f_{i}\\]

Length: The `weights` must have length equal to `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`. Thus, when scalar and/or field responses are specified, the number of `weights` must equal the number of scalars plus the number of fields, _not_ the total elements in the fields.

_Default Behavior_ If weights are not specified, then each response is given equal weighting:

\\[f = \sum_{i=1}^{n} \frac{f_i}{n}\\]

where, in both of these cases, a “minimization” `[sense](../../usingdakota/reference/responses-objective_functions-sense.html)` will retain a positive weighting for a minimizer and a “maximization” sense will apply a negative weighting.

_Usage Tips:_

Weights are applied as multipliers, scales as charateristic values / divisors.

When scaling is active, it is applied to objective functions prior to any `weights` and multi-objective sum formation. See the equations in `[objective_functions](../../usingdakota/reference/responses-objective_functions.html)`.


---

### responses → quasi_hessians

# quasi_hessians

Hessians are needed and will be approximated by secant updates (BFGS or SR1) from a series of gradient evaluations

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Quasi-Hessian Approximation | [bfgs](responses-quasi_hessians-bfgs.html) | Use BFGS method to compute quasi-hessians  
[sr1](responses-quasi_hessians-sr1.html) | Use the Symmetric Rank 1 update method to compute quasi-Hessians  
  
**Description**

The `quasi_hessians` specification means that Hessian information is needed and will be approximated using secant updates (sometimes called “quasi-Newton updates”, though any algorithm that approximates Newton’s method is a quasi-Newton method).

Compared to finite difference numerical Hessians, secant approximations do not expend additional function evaluations in estimating all of the second-order information for every point of interest. Rather, they accumulate approximate curvature information over time using the existing gradient evaluations.

The supported secant approximations include the Broyden-Fletcher-Goldfarb-Shanno (BFGS) update (specified with the keyword `bfgs`) and the Symmetric Rank 1 (SR1) update (specified with the keyword `sr1`).


---

#### responses → quasi_hessians → bfgs

# bfgs

Use BFGS method to compute quasi-hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [damped](responses-quasi_hessians-bfgs-damped.html) | Numerical safeguarding for BFGS updates  
  
**Description**

Broyden-Fletcher-Goldfarb-Shanno (BFGS) update will be used to compute quasi-Hessians.

\\[B_{k+1} = B_{k} - \frac{B_k s_k s_k^T B_k}{s_k^T B_k s_k} + \frac{y_k y_k^T}{y_k^T s_k}\\]

where \\(B_k\\) is the \\(k^{th}\\) approximation to the Hessian, \\(s_k = x_{k+1} - x_k\\) is the step and \\(y_k = \nabla f_{k+1} - \nabla f_k\\) is the corresponding yield in the gradients.

_Notes_

  * Initial scaling of \\(\frac{y_k^T y_k}{y_k^T s_k} I\\) is used for \\(B_0\\) prior to the first update.

  * Numerical safeguarding is used to protect against numerically small denominators within the updates.

  * This safeguarding skips the update if \\(|y_k^T s_k| < 10^{-6} s_k^T B_k s_k\\)

  * Additional safeguarding can be added using the `damped` option, which utilizes an alternative damped BFGS update when the curvature condition \\(y_k^T s_k > 0\\) is nearly violated.


---

##### responses → quasi_hessians → bfgs → damped

# damped

Numerical safeguarding for BFGS updates

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ undamped BFGS

**Description**

See parent page.


---

#### responses → quasi_hessians → sr1

# sr1

Use the Symmetric Rank 1 update method to compute quasi-Hessians

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

The Symmetric Rank 1 (SR1) update (specified with the keyword `sr1`) will be used to compute quasi-Hessians.

\\[B_{k+1} = B_k + \frac{(y_k - B_k s_k)(y_k - B_k s_k)^T}{(y_k - B_k s_k)^T s_k}\\]

where \\(B_k\\) is the \\(k^{th}\\) approximation to the Hessian, \\(s_k = x_{k+1} - x_k\\) is the step and \\(y_k = \nabla f_{k+1} - \nabla f_k\\) is the corresponding yield in the gradients.

_Notes_

  * Initial scaling of \\(\frac{y_k^T y_k}{y_k^T s_k} I\\) is used for \\(B_0\\) prior to the first update.

  * Numerical safeguarding is used to protect against numerically small denominators within the updates.

  * This safeguarding skips the update if \\(|(y_k - B_k s_k)^T s_k| < 10^{-6} ||s_k||_2 ||y_k - B_k s_k||_2\\)


---

### responses → response_functions

# response_functions

Generic response type

**Specification**

  * _Alias:_ num_response_functions

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [scalar_responses](responses-response_functions-scalar_responses.html) | Number of scalar response functions  
Optional | [field_responses](responses-response_functions-field_responses.html) | Number of field responses functions  
  
**Description**

A generic response data set is specified using `response_functions`. Each of these functions is simply a response quantity of interest with no special interpretation taken by the method in use.

Whereas objective, constraint, and residual functions have special meanings for optimization and least squares algorithms, the generic response function data set need not have a specific interpretation and the user is free to define whatever functional form is convenient.

**Theory**

This type of data set is used by uncertainty quantification methods, in which the effect of parameter uncertainty on response functions is quantified, and can also be used in parameter study and design of experiments methods (although these methods are not restricted to this data set), in which the effect of parameter variations on response functions is evaluated.


---

#### responses → response_functions → field_responses

# field_responses

Number of field responses functions

**Specification**

  * _Alias:_ num_field_responses

  * _Arguments:_ INTEGER

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required | [lengths](responses-response_functions-field_responses-lengths.html) | Lengths of field responses  
Optional | [num_coordinates_per_field](responses-response_functions-field_responses-num_coordinates_per_field.html) | Number of independent coordinates for field responses  
Optional | [read_field_coordinates](responses-response_functions-field_responses-read_field_coordinates.html) | Add context to data: flag to indicate that field coordinates should be read  
  
**Description**

This keyword describes the number of field response functions. A field function is a set of related response values collected over a range of independent coordinate values which may or may not be specified by the user. For example, voltage over time would be a field function, where voltage is the `field_objective` and time is the independent coordinate. Similarly, temperature over time and space would be a field response, where the independent coordinates would be both time and spatial coordinates such as (x,y) or (x,y,z), depending on the application. The main difference between scalar responses and field responses is that for field data, we plan to implement methods that take advantage of the correlation or relationship between the field values.

Note that if there is one `field_response`, and it has length 100 (meaning 100 values), then the user’s simulation code must return 100 values. Also, if there are both scalar and field responses, the user should specify the number of scalar responses as `scalar_responses`. If there are only field responses, it still is necessary to specify both `response_functions` = NN and `field_responses` = NN, where NN is the number of field responses.

This type of data set is used by uncertainty quantification methods, in which the effect of parameter uncertainty on response functions is quantified, and can also be used in parameter study and design of experiments methods (although these methods are not restricted to this data set), in which the effect of parameter variations on response functions is evaluated. Currently, field response functions will be translated back to scalar responses. So, a field of length 100 will be treated as 100 separate scalar responses. However, in future versions of Dakota, we plan to implement methods which can exploit the nature of field data.


---

##### responses → response_functions → field_responses → lengths

# lengths

Lengths of field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the lengths of each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `lengths` = 50 200, indicating that the first field response has 50 field elements but the second one has 200. The coordinate values (e.g. the independent variables) corresponding to these field responses are read in files labeled response_descriptor.coords.


---

##### responses → response_functions → field_responses → num_coordinates_per_field

# num_coordinates_per_field

Number of independent coordinates for field responses

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGERLIST

**Description**

This keyword describes the number of independent coordinates for each field response. It is an integer vector of length `field_responses`. For example, if the `field_responses` = 2, an example would be `num_coordinates_per_field` = 2 1 means that the first field response has two sets of independent coordinates (perhaps x, y locations), but the second response only has one (for example, time where the field response is only dependent upon time). The actual coordinate values (e.g. the independent variables) corresponding to these field responses are defined in a file call response_descriptor.coords, where response_descriptor is the name of the individual field.


---

##### responses → response_functions → field_responses → read_field_coordinates

# read_field_coordinates

Add context to data: flag to indicate that field coordinates should be read

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Description**

Field coordinates specify independent variables (e.g. spatial or temporal coordinates) upon which

the field depends. For example, the voltage level above might be a function of time, so time is the field coordinate. If the user has field coordinates to read, they need to specify `read_field_coordinates`. The field coordinates will then be read from a file named response_descriptor.coords, where response_descriptor is the user-provided descriptor for the field response. The number of columns in the coords file should be equal to the number of field coordinates.


---

#### responses → response_functions → scalar_responses

# scalar_responses

Number of scalar response functions

**Specification**

  * _Alias:_ num_scalar_responses

  * _Arguments:_ INTEGER

**Description**

This keyword describes the number of scalar response functions. It is meant to be used in conjunction with `field_responses`, which describes the number of field response functions. The total number of response functions, both scalar and field, is given by `response_functions`. If only scalar responses functions are specified, it is not necessary to specify the number of scalar terms explicitly: one can simply say `response_functions` = 5 and get 5 scalar responses. However, if there are three scalar responses and 2 field responses, then `response_functions` = 5 but `scalar_responses` = 3 and `field_responses` = 2.

This type of data set is used by uncertainty quantification methods, in which the effect of parameter uncertainty on response functions is quantified, and can also be used in parameter study and design of experiments methods (although these methods are not restricted to this data set), in which the effect of parameter variations on response functions is evaluated.

