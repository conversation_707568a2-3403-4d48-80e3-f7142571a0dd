#include "CustomTreeWidget.h"
#include <QInputDialog>
#include <QMessageBox>
#include <QStyle>
#include <QIcon>
#include <QFile>
#include <QTextStream>
#include <QRegularExpression>
#include <QMenu>
#include <QAction>
#include "PipeWidget.h"
#include "TubeWidget.h"
#include "../utils/Logger.h"

CustomTreeWidget::CustomTreeWidget(QWidget *parent)
    : QTreeWidget(parent)
{
    // Initialize pointer member variables
    m_optimizeItem = nullptr;
    m_sensitivityItem = nullptr;
    m_uqItem = nullptr;
    m_inputItem = nullptr;
    m_outputItem = nullptr;
    m_solverItem = nullptr;
    mCurrentItems = nullptr;
    m_iFileExample = nullptr;
    
    // Initialize menu and action pointers
    this->m_contextMenu = nullptr;
    m_addOptimizationAction = nullptr;
    m_addSensitivityAction = nullptr;
    m_addUQAction = nullptr;
    m_deleteNodeAction = nullptr;
    m_renameNodeAction = nullptr;
    m_moveUpAction = nullptr;
    m_moveDownAction = nullptr;
    
    // Initialize string member
    m_inputFilePath = "";
    setHeaderLabel(tr("Project Explorer"));
    setMinimumWidth(200);
    
    // Initialize IFileExample
    m_iFileExample = new IFileExample(this);
    
    // Connect IFileExample signals
    connect(m_iFileExample, &IFileExample::logMessage, this, [this](const QString& message) {
        LOG_INFO(message, "IFileExample");
    });
    
    connect(m_iFileExample, &IFileExample::fileProcessed, this, [this](const QString& filePath, bool success) {
        LOG_INFO(QString("File processed: %1, Success: %2").arg(filePath).arg(success), "IFileExample");
    });
    
    // Initialize components
    createActions();
    createMenus();
    setupConnections();
    
    // Start with empty tree - no project loaded
    setupEmptyProject();
    
    LOG_INFO("CustomTreeWidget initialized with empty project", "CustomTreeWidget");
}

CustomTreeWidget::~CustomTreeWidget()
{
    if (m_iFileExample) {
        delete m_iFileExample;
        m_iFileExample = nullptr;
    }
    LOG_INFO("CustomTreeWidget destroyed", "CustomTreeWidget");
}

void CustomTreeWidget::initTree()
{
    clear();
    setupTreeStructure();
}

void CustomTreeWidget::setupTreeStructure()
{
    clear();
    
    // Create three main analysis type nodes at root level
    
    // 1. Optimization Analysis Node
    m_optimizeItem = new QTreeWidgetItem(this);
    m_optimizeItem->setText(0, tr("Optimization"));
    m_optimizeItem->setIcon(0, getNodeIcon(NodeType::Optimize));
    m_optimizeItem->setExpanded(true);
    setNodeType(m_optimizeItem, NodeType::Optimize);
    
    // Add variables and responses nodes under Optimization
    QTreeWidgetItem* optInputItem = new QTreeWidgetItem(m_optimizeItem);
    optInputItem->setText(0, tr("variables"));
    optInputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(optInputItem, NodeType::Input);
    
    QTreeWidgetItem* optOutputItem = new QTreeWidgetItem(m_optimizeItem);
    optOutputItem->setText(0, tr("responses"));
    optOutputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(optOutputItem, NodeType::Output);
    
    // 2. Sensitivity Analysis Node
    m_sensitivityItem = new QTreeWidgetItem(this);
    m_sensitivityItem->setText(0, tr("Sensitivity Analysis"));
    m_sensitivityItem->setIcon(0, getNodeIcon(NodeType::Sensitivity));
    m_sensitivityItem->setExpanded(true);
    setNodeType(m_sensitivityItem, NodeType::Sensitivity);
    
    // Add variables and responses nodes under Sensitivity Analysis
    QTreeWidgetItem* sensInputItem = new QTreeWidgetItem(m_sensitivityItem);
    sensInputItem->setText(0, tr("variables"));
    sensInputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(sensInputItem, NodeType::Input);
    
    QTreeWidgetItem* sensOutputItem = new QTreeWidgetItem(m_sensitivityItem);
    sensOutputItem->setText(0, tr("responses"));
    sensOutputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(sensOutputItem, NodeType::Output);
    
    // 3. Uncertainty Quantification (UQ) Node
    m_uqItem = new QTreeWidgetItem(this);
    m_uqItem->setText(0, tr("Uncertainty Quantification"));
    m_uqItem->setIcon(0, getNodeIcon(NodeType::UQ));
    m_uqItem->setExpanded(true);
    setNodeType(m_uqItem, NodeType::UQ);
    
    // Add variables and responses nodes under UQ
    QTreeWidgetItem* uqInputItem = new QTreeWidgetItem(m_uqItem);
    uqInputItem->setText(0, tr("variables"));
    uqInputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(uqInputItem, NodeType::Input);
    
    QTreeWidgetItem* uqOutputItem = new QTreeWidgetItem(m_uqItem);
    uqOutputItem->setText(0, tr("responses"));
    uqOutputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(uqOutputItem, NodeType::Output);

    // Add Solver node (same level as analysis types)
    m_solverItem = new QTreeWidgetItem(this);
    m_solverItem->setText(0, tr("Solver"));
    m_solverItem->setIcon(0, getNodeIcon(NodeType::Solver));
    m_solverItem->setExpanded(true);
    setNodeType(m_solverItem, NodeType::Solver);
    
    // Add input and output nodes under Solver
    m_inputItem = new QTreeWidgetItem(m_solverItem);
    m_inputItem->setText(0, tr("Input"));
    m_inputItem->setIcon(0, getNodeIcon(NodeType::Input));
    m_inputItem->setExpanded(true);
    setNodeType(m_inputItem, NodeType::Input);
    
    m_outputItem = new QTreeWidgetItem(m_solverItem);
    m_outputItem->setText(0, tr("Output"));
    m_outputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(m_outputItem, NodeType::Output);
    
    // Note: Input file path will be set when user opens a file
    // Don't load modules by default - wait for user to open a file
}

void CustomTreeWidget::retranslate()
{
    setHeaderLabel(tr("Project Explorer"));
    
    if (m_optimizeItem) {
        m_optimizeItem->setText(0, tr("Optimization"));
    }
    
    if (m_sensitivityItem) {
        m_sensitivityItem->setText(0, tr("Sensitivity Analysis"));
    }
    
    if (m_uqItem) {
        m_uqItem->setText(0, tr("Uncertainty Quantification"));
    }
    
    if (m_solverItem) {
        m_solverItem->setText(0, tr("Solver"));
    }
    
    // Update solver's input and output nodes (keep as Input/Output for Solver)
    if (m_inputItem) {
        m_inputItem->setText(0, tr("Input"));
    }
    
    if (m_outputItem) {
        m_outputItem->setText(0, tr("Output"));
    }
}

QTreeWidgetItem* CustomTreeWidget::addNode(const QString& name, NodeType type, QTreeWidgetItem* parent)
{
    QTreeWidgetItem* newItem;
    if (!parent) {
        // Add as top-level item
        newItem = new QTreeWidgetItem(this);
    } else {
        newItem = new QTreeWidgetItem(parent);
    }
    
    newItem->setText(0, name);
    newItem->setIcon(0, getNodeIcon(type));
    setNodeType(newItem, type);
    
    LOG_INFO(QString("Node added: %1 (type: %2)").arg(name).arg(nodeTypeToString(type)), "CustomTreeWidget");
    
    return newItem;
}

void CustomTreeWidget::removeNode(QTreeWidgetItem* item)
{
    if (!item) {
        return;
    }
    
    QString itemName = item->text(0);
    NodeType type = getNodeType(item);
    
    delete item;
    
    LOG_INFO(QString("Node removed: %1 (type: %2)").arg(itemName).arg(nodeTypeToString(type)), "CustomTreeWidget");
}

void CustomTreeWidget::renameNode(QTreeWidgetItem* item, const QString& newName)
{
    if (!item || newName.isEmpty()) {
        return;
    }
    
    QString oldName = item->text(0);
    item->setText(0, newName);
    
    LOG_INFO(QString("Node renamed: %1 -> %2").arg(oldName, newName), "CustomTreeWidget");
}

QTreeWidgetItem* CustomTreeWidget::findNode(const QString& name, NodeType type)
{
    QTreeWidgetItemIterator it(this);
    while (*it) {
        QTreeWidgetItem* item = *it;
        if (item->text(0) == name) {
            if (type == NodeType::Unknown || getNodeType(item) == type) {
                return item;
            }
        }
        ++it;
    }
    return nullptr;
}

QList<QTreeWidgetItem*> CustomTreeWidget::getNodesByType(NodeType type)
{
    QList<QTreeWidgetItem*> nodes;
    QTreeWidgetItemIterator it(this);
    while (*it) {
        QTreeWidgetItem* item = *it;
        if (getNodeType(item) == type) {
            nodes.append(item);
        }
        ++it;
    }
    return nodes;
}

NodeType CustomTreeWidget::getNodeType(QTreeWidgetItem* item) const
{
    if (!item) {
        return NodeType::Unknown;
    }
    
    QVariant data = item->data(0, Qt::UserRole);
    if (data.isValid()) {
        return stringToNodeType(data.toString());
    }
    
    return NodeType::Unknown;
}

void CustomTreeWidget::setNodeType(QTreeWidgetItem* item, NodeType type)
{
    if (item) {
        item->setData(0, Qt::UserRole, nodeTypeToString(type));
    }
}

void CustomTreeWidget::createActions()
{
    m_addOptimizationAction = new QAction(tr("Add Optimization"), this);
    m_addOptimizationAction->setIcon(getNodeIcon(NodeType::Optimize));
    
    m_addSensitivityAction = new QAction(tr("Add Sensitivity Analysis"), this);
    m_addSensitivityAction->setIcon(getNodeIcon(NodeType::Sensitivity));
    
    m_addUQAction = new QAction(tr("Add Uncertainty Quantification"), this);
    m_addUQAction->setIcon(getNodeIcon(NodeType::UQ));
    
    m_deleteNodeAction = new QAction(tr("Delete"), this);
    m_deleteNodeAction->setIcon(style()->standardIcon(QStyle::SP_TrashIcon));
    
    m_renameNodeAction = new QAction(tr("Rename"), this);
    m_renameNodeAction->setIcon(style()->standardIcon(QStyle::SP_FileDialogDetailedView));
    
    m_moveUpAction = new QAction(tr("Move Up"), this);
    m_moveUpAction->setIcon(style()->standardIcon(QStyle::SP_ArrowUp));
    
    m_moveDownAction = new QAction(tr("Move Down"), this);
    m_moveDownAction->setIcon(style()->standardIcon(QStyle::SP_ArrowDown));
}

void CustomTreeWidget::createMenus()
{
    m_contextMenu = new QMenu(this);
    m_contextMenu->addAction(m_addOptimizationAction);
    m_contextMenu->addAction(m_addSensitivityAction);
    m_contextMenu->addAction(m_addUQAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_deleteNodeAction);
    m_contextMenu->addAction(m_renameNodeAction);
    m_contextMenu->addSeparator();
    m_contextMenu->addAction(m_moveUpAction);
    m_contextMenu->addAction(m_moveDownAction);
}

void CustomTreeWidget::setupConnections()
{
    connect(this, &QTreeWidget::itemDoubleClicked, this, &CustomTreeWidget::onItemDoubleClicked);
    connect(this, &QTreeWidget::itemSelectionChanged, this, &CustomTreeWidget::onItemSelectionChanged);
    
    connect(m_addOptimizationAction, &QAction::triggered, this, &CustomTreeWidget::onAddOptimization);
    connect(m_addSensitivityAction, &QAction::triggered, this, &CustomTreeWidget::onAddSensitivityAnalysis);
    connect(m_addUQAction, &QAction::triggered, this, &CustomTreeWidget::onAddUncertaintyQuantification);
    connect(m_deleteNodeAction, &QAction::triggered, this, &CustomTreeWidget::onDeleteNode);
    connect(m_renameNodeAction, &QAction::triggered, this, &CustomTreeWidget::onRenameNode);
    connect(m_moveUpAction, &QAction::triggered, this, &CustomTreeWidget::onMoveNodeUp);
    connect(m_moveDownAction, &QAction::triggered, this, &CustomTreeWidget::onMoveNodeDown);
}

void CustomTreeWidget::contextMenuEvent(QContextMenuEvent *event)
{
    mCurrentItems = itemAt(event->pos());
    if (m_contextMenu) {
        // Enable/disable menu items based on context
        bool hasItem = (mCurrentItems != nullptr);
        NodeType type = hasItem ? getNodeType(mCurrentItems) : NodeType::Unknown;
        
        // Add actions are always enabled (can add to root or any node)
        m_addOptimizationAction->setEnabled(true);
        m_addSensitivityAction->setEnabled(true);
        m_addUQAction->setEnabled(true);
        
        // Delete action - can delete analysis nodes but not system nodes
        bool canDelete = hasItem && (type == NodeType::Optimize || type == NodeType::Sensitivity || type == NodeType::UQ) &&
                        mCurrentItems != m_solverItem;
        m_deleteNodeAction->setEnabled(canDelete);
        
        // Rename action - can rename analysis nodes
        bool canRename = hasItem && (type == NodeType::Optimize || type == NodeType::Sensitivity || type == NodeType::UQ);
        m_renameNodeAction->setEnabled(canRename);
        
        // Move actions - can move analysis nodes
        bool canMove = hasItem && (type == NodeType::Optimize || type == NodeType::Sensitivity || type == NodeType::UQ);
        m_moveUpAction->setEnabled(canMove && canMoveNodeUp(mCurrentItems));
        m_moveDownAction->setEnabled(canMove && canMoveNodeDown(mCurrentItems));
        
        if (hasItem) {
            emit nodeContextMenuRequested(mCurrentItems, event->pos());
        }
        m_contextMenu->exec(event->globalPos());
    }
}

void CustomTreeWidget::onItemDoubleClicked(QTreeWidgetItem *item, int column)
{
    Q_UNUSED(column)
    
    if (!item) {
        return;
    }
    
    NodeType type = getNodeType(item);
    
    // Check if this is a module under Solver/Input node or its categories
    QTreeWidgetItem* parentItem = item->parent();
    bool isModuleItem = false;
    
    // Check the hierarchy: Solver -> Input -> Category -> Module
    if (parentItem == m_inputItem) {
        // This might be a category node (Pipes, Junctions, etc.), not a module
        return;
    } else if (parentItem && parentItem->parent() == m_inputItem) {
        // This is a module under a category under Solver/Input
        isModuleItem = true;
    }
    
    if (isModuleItem) {
        QString moduleType = item->data(0, Qt::UserRole + 2).toString();
        
        if (moduleType == "pipe") {
            // Get PipeComponent from node data
            QVariant componentData = item->data(0, Qt::UserRole + 1);
            if (componentData.canConvert<PipeComponent>()) {
                PipeComponent pipe = componentData.value<PipeComponent>();
                QWidget* widget = new PipeWidget(pipe);
                QString title = QString("Pipe Component - %1").arg(pipe.componentName);
                emit moduleWidgetRequested(widget, title);
                
                LOG_INFO(QString("Created PipeWidget for: %1").arg(pipe.componentId), "CustomTreeWidget");
                return;
            }
        }
        else if (moduleType == "sngljun") {
            // Get JunctionComponent from node data
            QVariant componentData = item->data(0, Qt::UserRole + 1);
            if (componentData.canConvert<JunctionComponent>()) {
                JunctionComponent junction = componentData.value<JunctionComponent>();
                QWidget* widget = new TubeWidget(junction);
                QString title = QString("Junction Component - %1").arg(junction.componentName);
                emit moduleWidgetRequested(widget, title);
                
                LOG_INFO(QString("Created TubeWidget for: %1").arg(junction.componentId), "CustomTreeWidget");
                return;
            }
        }
        else if (moduleType == "snglvol" || moduleType.contains("vol")) {
            // For volume components, show parameter information instead of widget
            LOG_INFO(QString("Volume component selected: %1 (Widget not yet implemented)").arg(moduleType), "CustomTreeWidget");
            // The parameter display will be handled by MainWindow::showComponentParameters
        }
        else if (moduleType == "branch" || moduleType.contains("branch")) {
            // For branch components, show parameter information instead of widget
            LOG_INFO(QString("Branch component selected: %1 (Widget not yet implemented)").arg(moduleType), "CustomTreeWidget");
            // The parameter display will be handled by MainWindow::showComponentParameters
        }
        else {
            LOG_INFO(QString("No widget available for module type: %1").arg(moduleType), "CustomTreeWidget");
        }
    }
    
    emit nodeDoubleClicked(item, type);
    
    LOG_INFO(QString("Item double clicked: %1 (type: %2)")
             .arg(item->text(0))
             .arg(nodeTypeToString(type)), "CustomTreeWidget");
}

void CustomTreeWidget::onItemSelectionChanged()
{
    QTreeWidgetItem* current = currentItem();
    if (current) {
        NodeType type = getNodeType(current);
        emit nodeSelected(current, type);
        
        LOG_INFO(QString("Item selected: %1 (type: %2)")
                 .arg(current->text(0))
                 .arg(nodeTypeToString(type)), "CustomTreeWidget");
    }
}

void CustomTreeWidget::onCustomContextMenuRequested(const QPoint &pos)
{
    mCurrentItems = itemAt(pos);
    if (mCurrentItems && m_contextMenu) {
        m_contextMenu->exec(mapToGlobal(pos));
    }
}

void CustomTreeWidget::onAddOptimization()
{
    QString nodeName = generateUniqueNodeName(NodeType::Optimize);
    QTreeWidgetItem* newItem = new QTreeWidgetItem(this);
    newItem->setText(0, nodeName);
    newItem->setIcon(0, getNodeIcon(NodeType::Optimize));
    newItem->setExpanded(true);
    setNodeType(newItem, NodeType::Optimize);
    
    // Add variables and responses nodes under the new optimization node
    QTreeWidgetItem* inputItem = new QTreeWidgetItem(newItem);
    inputItem->setText(0, tr("variables"));
    inputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(inputItem, NodeType::Input);
    
    QTreeWidgetItem* outputItem = new QTreeWidgetItem(newItem);
    outputItem->setText(0, tr("responses"));
    outputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(outputItem, NodeType::Output);
    
    LOG_INFO(QString("Added new optimization node: %1").arg(nodeName), "CustomTreeWidget");
}

void CustomTreeWidget::onAddSensitivityAnalysis()
{
    QString nodeName = generateUniqueNodeName(NodeType::Sensitivity);
    QTreeWidgetItem* newItem = new QTreeWidgetItem(this);
    newItem->setText(0, nodeName);
    newItem->setIcon(0, getNodeIcon(NodeType::Sensitivity));
    newItem->setExpanded(true);
    setNodeType(newItem, NodeType::Sensitivity);
    
    // Add variables and responses nodes under the new sensitivity analysis node
    QTreeWidgetItem* inputItem = new QTreeWidgetItem(newItem);
    inputItem->setText(0, tr("variables"));
    inputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(inputItem, NodeType::Input);
    
    QTreeWidgetItem* outputItem = new QTreeWidgetItem(newItem);
    outputItem->setText(0, tr("responses"));
    outputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(outputItem, NodeType::Output);
    
    LOG_INFO(QString("Added new sensitivity analysis node: %1").arg(nodeName), "CustomTreeWidget");
}

void CustomTreeWidget::onAddUncertaintyQuantification()
{
    QString nodeName = generateUniqueNodeName(NodeType::UQ);
    QTreeWidgetItem* newItem = new QTreeWidgetItem(this);
    newItem->setText(0, nodeName);
    newItem->setIcon(0, getNodeIcon(NodeType::UQ));
    newItem->setExpanded(true);
    setNodeType(newItem, NodeType::UQ);
    
    // Add variables and responses nodes under the new UQ node
    QTreeWidgetItem* inputItem = new QTreeWidgetItem(newItem);
    inputItem->setText(0, tr("variables"));
    inputItem->setIcon(0, getNodeIcon(NodeType::Input));
    setNodeType(inputItem, NodeType::Input);
    
    QTreeWidgetItem* outputItem = new QTreeWidgetItem(newItem);
    outputItem->setText(0, tr("responses"));
    outputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(outputItem, NodeType::Output);
    
    LOG_INFO(QString("Added new uncertainty quantification node: %1").arg(nodeName), "CustomTreeWidget");
}

void CustomTreeWidget::onDeleteNode()
{
    if (!mCurrentItems) {
        return;
    }
    
    NodeType type = getNodeType(mCurrentItems);
    if (type != NodeType::Optimize && type != NodeType::Sensitivity && type != NodeType::UQ) {
        QMessageBox::warning(this, tr("Cannot Delete"), 
                           tr("Only analysis nodes can be deleted."));
        return;
    }
    
    int ret = QMessageBox::question(this, tr("Delete Node"),
                                  tr("Are you sure you want to delete '%1'?")
                                  .arg(mCurrentItems->text(0)),
                                  QMessageBox::Yes | QMessageBox::No);
    if (ret == QMessageBox::Yes) {
        removeNode(mCurrentItems);
        mCurrentItems = nullptr;
        LOG_INFO(QString("Deleted analysis node"), "CustomTreeWidget");
    }
}

void CustomTreeWidget::onRenameNode()
{
    if (!mCurrentItems) {
        return;
    }
    
    bool ok;
    QString newName = QInputDialog::getText(this, tr("Rename Node"),
                                          tr("New name:"), QLineEdit::Normal,
                                          mCurrentItems->text(0), &ok);
    if (ok && !newName.isEmpty()) {
        renameNode(mCurrentItems, newName);
        LOG_INFO(QString("Renamed node to: %1").arg(newName), "CustomTreeWidget");
    }
}

void CustomTreeWidget::onMoveNodeUp()
{
    if (!mCurrentItems || !canMoveNodeUp(mCurrentItems)) {
        return;
    }
    
    QTreeWidgetItem* parent = mCurrentItems->parent();
    if (!parent) {
        // Moving at root level
        int index = indexOfTopLevelItem(mCurrentItems);
        if (index > 0) {
            QTreeWidgetItem* item = takeTopLevelItem(index);
            insertTopLevelItem(index - 1, item);
            setCurrentItem(item);
            LOG_INFO(QString("Moved node up: %1").arg(item->text(0)), "CustomTreeWidget");
        }
    }
}

void CustomTreeWidget::onMoveNodeDown()
{
    if (!mCurrentItems || !canMoveNodeDown(mCurrentItems)) {
        return;
    }
    
    QTreeWidgetItem* parent = mCurrentItems->parent();
    if (!parent) {
        // Moving at root level
        int index = indexOfTopLevelItem(mCurrentItems);
        if (index < topLevelItemCount() - 1) {
            QTreeWidgetItem* item = takeTopLevelItem(index);
            insertTopLevelItem(index + 1, item);
            setCurrentItem(item);
            LOG_INFO(QString("Moved node down: %1").arg(item->text(0)), "CustomTreeWidget");
        }
    }
}

QString CustomTreeWidget::nodeTypeToString(NodeType type) const
{
    switch (type) {
        case NodeType::Root:        return "Root";
        case NodeType::Optimize:    return "Optimize";
        case NodeType::Sensitivity: return "Sensitivity";
        case NodeType::UQ:          return "UQ";
        case NodeType::Input:       return "Input";
        case NodeType::Output:      return "Output";
        case NodeType::Solver:      return "Solver";
        default:                    return "Unknown";
    }
}

NodeType CustomTreeWidget::stringToNodeType(const QString& str) const
{
    if (str == "Root") return NodeType::Root;
    if (str == "Optimize") return NodeType::Optimize;
    if (str == "Sensitivity") return NodeType::Sensitivity;
    if (str == "UQ") return NodeType::UQ;
    if (str == "Input") return NodeType::Input;
    if (str == "Output") return NodeType::Output;
    if (str == "Solver") return NodeType::Solver;
    return NodeType::Unknown;
}

QIcon CustomTreeWidget::getNodeIcon(NodeType type) const
{
    switch (type) {
        case NodeType::Root:
            return style()->standardIcon(QStyle::SP_ComputerIcon);
        case NodeType::Optimize:
            return style()->standardIcon(QStyle::SP_FileDialogDetailedView);
        case NodeType::Sensitivity:
            return style()->standardIcon(QStyle::SP_DialogApplyButton);
        case NodeType::UQ:
            return style()->standardIcon(QStyle::SP_DialogHelpButton);
        case NodeType::Input:
            return style()->standardIcon(QStyle::SP_FileDialogListView);
        case NodeType::Output:
            return style()->standardIcon(QStyle::SP_FileDialogInfoView);
        case NodeType::Solver:
            return style()->standardIcon(QStyle::SP_DesktopIcon);
        default:
            return style()->standardIcon(QStyle::SP_FileDialogNewFolder);
    }
}

void CustomTreeWidget::loadSolverModules()
{
    if (m_inputFilePath.isEmpty()) {
        LOG_INFO("Input file path is empty", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("=== Starting .i file parsing using IFileExample class ===", "CustomTreeWidget");
    LOG_INFO(QString("Target file: %1").arg(m_inputFilePath), "CustomTreeWidget");
    
    // Use IFileExample-based parsing approach
    if (!parseIFileWithExample(m_inputFilePath)) {
        LOG_INFO(QString("Failed to parse .i file using IFileExample class: %1").arg(m_inputFilePath), "CustomTreeWidget");
        
        // Show error details if available
        if (m_iFileExample && m_iFileExample->hasError()) {
            LOG_INFO(QString("Error details: %1").arg(m_iFileExample->getLastError()), "CustomTreeWidget");
        }
        return;
    }
    
    // Display parsing results using IFileExample's detailed analysis
    displayParsingResults();
    
    // Add components to Solver node with enhanced organization
    addComponentsToSolverNode();
    
    // Generate and display file preview using IFileExample
    generateFilePreview();
    
    // Validate file format using IFileExample
    validateFileFormat();
    
    LOG_INFO("=== .i file parsing completed successfully ===", "CustomTreeWidget");
}

void CustomTreeWidget::setInputFilePath(const QString& filePath)
{
    m_inputFilePath = filePath;
    LOG_INFO(QString("Input file path set to: %1").arg(filePath), "CustomTreeWidget");
    
    // Clear existing solver input modules
    if (m_inputItem) {
        // Remove all child items from solver input node
        QList<QTreeWidgetItem*> children;
        for (int i = 0; i < m_inputItem->childCount(); ++i) {
            children.append(m_inputItem->child(i));
        }
        
        for (QTreeWidgetItem* child : children) {
            m_inputItem->removeChild(child);
            delete child;
        }
    }
    
    // Reload solver modules with new file
    loadSolverModules();
    
    // Ensure Solver node is expanded and visible
    if (m_solverItem) {
        expandItem(m_solverItem);
        m_solverItem->setExpanded(true);
        
        // Select the Solver node to make it visible to user
        setCurrentItem(m_solverItem);
        
        // Scroll to make sure the Solver node is visible
        scrollToItem(m_solverItem, QAbstractItemView::PositionAtTop);
        
        LOG_INFO("Solver node expanded and selected", "CustomTreeWidget");
    }
}

bool CustomTreeWidget::parseIFileWithExample(const QString& filePath)
{
    LOG_INFO(QString("--- Loading .i file using IFileExample class ---"), "CustomTreeWidget");
    
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available", "CustomTreeWidget");
        return false;
    }
    
    // Use IFileExample to load and analyze the file
    bool success = m_iFileExample->loadAndAnalyzeIFile(filePath);
    
    if (success) {
        // Get the processed data from IFileExample
        m_inputData = m_iFileExample->getLastProcessedData();
        
        LOG_INFO(QString("Successfully loaded file using IFileExample: %1").arg(m_inputData.fileName), "CustomTreeWidget");
        LOG_INFO(QString("File type: %1").arg(InputData::fileTypeToString(m_inputData.fileType)), "CustomTreeWidget");
        LOG_INFO(QString("Problem title: %1").arg(m_inputData.problemTitle), "CustomTreeWidget");
        LOG_INFO(QString("Total components: %1").arg(m_inputData.getTotalComponents()), "CustomTreeWidget");
        
        return true;
    } else {
        LOG_INFO(QString("Failed to load file using IFileExample: %1").arg(filePath), "CustomTreeWidget");
        LOG_INFO(QString("Error: %1").arg(m_iFileExample->getLastError()), "CustomTreeWidget");
        return false;
    }
}

void CustomTreeWidget::displayParsingResults()
{
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available for detailed analysis", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("--- Displaying parsing results using IFileExample analysis ---", "CustomTreeWidget");
    
    // Use IFileExample's display methods for comprehensive analysis
    m_iFileExample->displayFileStatistics(m_inputData);
    m_iFileExample->displayControlCardInfo(m_inputData);
    m_iFileExample->displayUnitSystemInfo(m_inputData);
    m_iFileExample->displayComponentStatistics(m_inputData);
    
    // Additional component details for TreeWidget organization
    LOG_INFO("TreeWidget organization details:", "CustomTreeWidget");
    LOG_INFO(QString("  Total components for tree: %1").arg(m_inputData.getTotalComponents()), "CustomTreeWidget");
    
    // Display component type breakdown
    int pipeCount = m_inputData.pipes.size();
    int junctionCount = m_inputData.junctions.size();
    int volumeCount = m_inputData.volumes.size();
    int branchCount = m_inputData.branches.size();
    
    LOG_INFO(QString("  Categories to create: %1").arg(
        (pipeCount > 0 ? 1 : 0) + 
        (junctionCount > 0 ? 1 : 0) + 
        (volumeCount > 0 ? 1 : 0) + 
        (branchCount > 0 ? 1 : 0)), "CustomTreeWidget");
    
    // Display sample components for verification
    if (pipeCount > 0) {
        const auto& firstPipe = m_inputData.pipes.first();
        LOG_INFO(QString("  Sample pipe: %1 (%2)").arg(
            firstPipe.componentName.isEmpty() ? firstPipe.componentId : firstPipe.componentName,
            firstPipe.componentId), "CustomTreeWidget");
    }
    
    if (junctionCount > 0) {
        const auto& firstJunction = m_inputData.junctions.first();
        LOG_INFO(QString("  Sample junction: %1 (%2)").arg(
            firstJunction.componentName.isEmpty() ? firstJunction.componentId : firstJunction.componentName,
            firstJunction.componentId), "CustomTreeWidget");
    }
    
    if (volumeCount > 0) {
        const auto& firstVolume = m_inputData.volumes.first();
        LOG_INFO(QString("  Sample volume: %1 (%2)").arg(
            firstVolume.componentName.isEmpty() ? firstVolume.componentId : firstVolume.componentName,
            firstVolume.componentId), "CustomTreeWidget");
    }
    
    if (branchCount > 0) {
        const auto& firstBranch = m_inputData.branches.first();
        LOG_INFO(QString("  Sample branch: %1 (%2)").arg(
            firstBranch.componentName.isEmpty() ? firstBranch.componentId : firstBranch.componentName,
            firstBranch.componentId), "CustomTreeWidget");
    }
}

void CustomTreeWidget::addComponentsToSolverNode()
{
    if (!m_solverItem || !m_inputItem) {
        LOG_INFO("Solver node or Input node not found", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("--- Adding components to Solver Input node ---", "CustomTreeWidget");
    
    // Create category nodes for better organization under Solver Input
    QTreeWidgetItem* pipeCategory = nullptr;
    QTreeWidgetItem* junctionCategory = nullptr;
    QTreeWidgetItem* volumeCategory = nullptr;
    QTreeWidgetItem* branchCategory = nullptr;
    
    // Add pipe components
    if (!m_inputData.pipes.isEmpty()) {
        pipeCategory = addNode(QString("Pipes (%1)").arg(m_inputData.pipes.size()), NodeType::Unknown, m_inputItem);
        pipeCategory->setIcon(0, style()->standardIcon(QStyle::SP_DriveHDIcon));
        
        for (const auto& pipe : m_inputData.pipes) {
            QString displayName = QString("%1 (%2)").arg(pipe.componentName.isEmpty() ? pipe.componentId : pipe.componentName, pipe.componentId);
            QTreeWidgetItem* moduleItem = addNode(displayName, NodeType::Unknown, pipeCategory);
            
            // Store component data to node
            moduleItem->setData(0, Qt::UserRole + 1, QVariant::fromValue(pipe));
            moduleItem->setData(0, Qt::UserRole + 2, "pipe");
            moduleItem->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
            
            LOG_INFO(QString("Added pipe component: %1").arg(displayName), "CustomTreeWidget");
        }
        expandItem(pipeCategory);
    }
    
    // Add junction components
    if (!m_inputData.junctions.isEmpty()) {
        junctionCategory = addNode(QString("Junctions (%1)").arg(m_inputData.junctions.size()), NodeType::Unknown, m_inputItem);
        junctionCategory->setIcon(0, style()->standardIcon(QStyle::SP_DriveNetIcon));
        
        for (const auto& junction : m_inputData.junctions) {
            QString displayName = QString("%1 (%2)").arg(junction.componentName.isEmpty() ? junction.componentId : junction.componentName, junction.componentId);
            QTreeWidgetItem* moduleItem = addNode(displayName, NodeType::Unknown, junctionCategory);
            
            // Store component data to node
            moduleItem->setData(0, Qt::UserRole + 1, QVariant::fromValue(junction));
            moduleItem->setData(0, Qt::UserRole + 2, "sngljun");
            moduleItem->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
            
            LOG_INFO(QString("Added junction component: %1").arg(displayName), "CustomTreeWidget");
        }
        expandItem(junctionCategory);
    }
    
    // Add volume components
    if (!m_inputData.volumes.isEmpty()) {
        volumeCategory = addNode(QString("Volumes (%1)").arg(m_inputData.volumes.size()), NodeType::Unknown, m_inputItem);
        volumeCategory->setIcon(0, style()->standardIcon(QStyle::SP_DriveCDIcon));
        
        for (const auto& volume : m_inputData.volumes) {
            QString displayName = QString("%1 (%2)").arg(volume.componentName.isEmpty() ? volume.componentId : volume.componentName, volume.componentId);
            QTreeWidgetItem* moduleItem = addNode(displayName, NodeType::Unknown, volumeCategory);
            
            // Store component data to node
            moduleItem->setData(0, Qt::UserRole + 1, QVariant::fromValue(volume));
            moduleItem->setData(0, Qt::UserRole + 2, volume.componentType);
            moduleItem->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
            
            LOG_INFO(QString("Added volume component: %1").arg(displayName), "CustomTreeWidget");
        }
        expandItem(volumeCategory);
    }
    
    // Add branch components
    if (!m_inputData.branches.isEmpty()) {
        branchCategory = addNode(QString("Branches (%1)").arg(m_inputData.branches.size()), NodeType::Unknown, m_inputItem);
        branchCategory->setIcon(0, style()->standardIcon(QStyle::SP_DirIcon));
        
        for (const auto& branch : m_inputData.branches) {
            QString displayName = QString("%1 (%2)").arg(branch.componentName.isEmpty() ? branch.componentId : branch.componentName, branch.componentId);
            QTreeWidgetItem* moduleItem = addNode(displayName, NodeType::Unknown, branchCategory);
            
            // Store component data to node
            moduleItem->setData(0, Qt::UserRole + 1, QVariant::fromValue(branch));
            moduleItem->setData(0, Qt::UserRole + 2, branch.componentType);
            moduleItem->setIcon(0, style()->standardIcon(QStyle::SP_FileIcon));
            
            LOG_INFO(QString("Added branch component: %1").arg(displayName), "CustomTreeWidget");
        }
        expandItem(branchCategory);
    }
    
    // Expand Solver and Input nodes to show all categories
    if (m_solverItem && m_inputItem) {
        expandItem(m_solverItem);
        m_solverItem->setExpanded(true);
        expandItem(m_inputItem);
        m_inputItem->setExpanded(true);
        
        // Count total components across all categories
        int totalComponents = m_inputData.pipes.size() + m_inputData.junctions.size() + 
                             m_inputData.volumes.size() + m_inputData.branches.size();
        
        if (totalComponents > 0) {
            LOG_INFO(QString("Successfully loaded %1 components organized in %2 categories under Solver Input node")
                    .arg(totalComponents).arg(m_inputItem->childCount()), "CustomTreeWidget");
        } else {
            LOG_INFO("No components found in the .i file", "CustomTreeWidget");
        }
    }
    
    LOG_INFO("--- Component addition completed ---", "CustomTreeWidget");
}

void CustomTreeWidget::generateFilePreview()
{
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available for preview generation", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("--- Generating file preview using IFileExample ---", "CustomTreeWidget");
    
    QString preview = m_iFileExample->generateIFilePreview(m_inputFilePath, 15);
    if (!preview.isEmpty()) {
        LOG_INFO("File preview generated:", "CustomTreeWidget");
        QStringList lines = preview.split('\n');
        for (const QString& line : lines) {
            LOG_INFO(line, "CustomTreeWidget");
        }
    } else {
        LOG_INFO("Failed to generate file preview", "CustomTreeWidget");
    }
}

void CustomTreeWidget::validateFileFormat()
{
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available for validation", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("--- Validating file format using IFileExample ---", "CustomTreeWidget");
    
    bool isValid = m_iFileExample->validateIFile(m_inputFilePath);
    LOG_INFO(QString("File format validation result: %1").arg(isValid ? "VALID" : "INVALID"), "CustomTreeWidget");
    
    // Check consistency
    QStringList issues = m_iFileExample->checkIFileConsistency(m_inputFilePath);
    if (!issues.isEmpty()) {
        LOG_INFO("Consistency check found issues:", "CustomTreeWidget");
        for (const QString& issue : issues) {
            LOG_INFO(QString("  - %1").arg(issue), "CustomTreeWidget");
        }
    } else {
        LOG_INFO("Consistency check: PASSED", "CustomTreeWidget");
    }
}

void CustomTreeWidget::demonstrateIFileFeatures()
{
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available for demonstration", "CustomTreeWidget");
        return;
    }
    
    LOG_INFO("=== Demonstrating IFileExample features ===", "CustomTreeWidget");
    
    // Demonstrate read/write functionality
    LOG_INFO("Running IFileExample read/write demonstration...", "CustomTreeWidget");
    bool success = m_iFileExample->demonstrateIFileReadWrite(m_inputFilePath);
    LOG_INFO(QString("Read/write demonstration result: %1").arg(success ? "SUCCESS" : "FAILED"), "CustomTreeWidget");
    
    // Demonstrate creating new file
    LOG_INFO("Running IFileExample create new file demonstration...", "CustomTreeWidget");
    QString outputFile = "output/demo_created_file.i";
    success = m_iFileExample->demonstrateCreateNewIFile(outputFile);
    LOG_INFO(QString("Create new file demonstration result: %1").arg(success ? "SUCCESS" : "FAILED"), "CustomTreeWidget");
    
    // Demonstrate batch processing
    LOG_INFO("Running IFileExample batch processing demonstration...", "CustomTreeWidget");
    success = m_iFileExample->demonstrateBatchIFileProcessing("input");
    LOG_INFO(QString("Batch processing demonstration result: %1").arg(success ? "SUCCESS" : "FAILED"), "CustomTreeWidget");
    
    LOG_INFO("=== IFileExample features demonstration completed ===", "CustomTreeWidget");
}

void CustomTreeWidget::runIFileExampleDemo()
{
    LOG_INFO("=== Running IFileExample demonstration from CustomTreeWidget ===", "CustomTreeWidget");
    
    if (!m_iFileExample) {
        LOG_INFO("IFileExample instance not available", "CustomTreeWidget");
        return;
    }
    
    // Run the full demonstration
    demonstrateIFileFeatures();
    
    LOG_INFO("=== IFileExample demonstration completed ===", "CustomTreeWidget");
}

QString CustomTreeWidget::generateUniqueNodeName(NodeType type) const
{
    QString baseName;
    switch (type) {
        case NodeType::Optimize:
            baseName = tr("Optimization");
            break;
        case NodeType::Sensitivity:
            baseName = tr("Sensitivity Analysis");
            break;
        case NodeType::UQ:
            baseName = tr("Uncertainty Quantification");
            break;
        default:
            baseName = tr("Node");
            break;
    }
    
    // Find existing nodes of the same type
    QList<QTreeWidgetItem*> existingNodes;
    for (int i = 0; i < topLevelItemCount(); ++i) {
        QTreeWidgetItem* item = topLevelItem(i);
        if (getNodeType(item) == type) {
            existingNodes.append(item);
        }
    }
    
    // If no existing nodes, return base name
    if (existingNodes.isEmpty()) {
        return baseName;
    }
    
    // Find the highest number used
    int maxNumber = 0;
    QRegularExpression regex(QString("%1\\s*(\\d+)?").arg(QRegularExpression::escape(baseName)));
    
    for (QTreeWidgetItem* item : existingNodes) {
        QString itemName = item->text(0);
        QRegularExpressionMatch match = regex.match(itemName);
        if (match.hasMatch()) {
            QString numberStr = match.captured(1);
            if (numberStr.isEmpty()) {
                // This is the base name without number, treat as 1
                maxNumber = qMax(maxNumber, 1);
            } else {
                int number = numberStr.toInt();
                maxNumber = qMax(maxNumber, number);
            }
        }
    }
    
    // Return base name with next number
    return QString("%1 %2").arg(baseName).arg(maxNumber + 1);
}

bool CustomTreeWidget::canMoveNodeUp(QTreeWidgetItem* item) const
{
    if (!item) {
        return false;
    }
    
    QTreeWidgetItem* parent = item->parent();
    if (!parent) {
        // Root level item
        int index = indexOfTopLevelItem(item);
        return index > 0;
    }
    
    // Child item - not implemented for now
    return false;
}

bool CustomTreeWidget::canMoveNodeDown(QTreeWidgetItem* item) const
{
    if (!item) {
        return false;
    }
    
    QTreeWidgetItem* parent = item->parent();
    if (!parent) {
        // Root level item
        int index = indexOfTopLevelItem(item);
        return index < topLevelItemCount() - 1;
    }
    
    // Child item - not implemented for now
    return false;
}

void CustomTreeWidget::setupEmptyProject()
{
    clear();
    setHeaderLabel(tr("Project Explorer"));
    
    // Reset all item pointers
    m_optimizeItem = nullptr;
    m_sensitivityItem = nullptr;
    m_uqItem = nullptr;
    m_inputItem = nullptr;
    m_outputItem = nullptr;
    m_solverItem = nullptr;
    
    LOG_INFO("Empty project setup completed", "CustomTreeWidget");
}

void CustomTreeWidget::clearProject()
{
    clear();
    setupEmptyProject();
    LOG_INFO("Project cleared", "CustomTreeWidget");
}

void CustomTreeWidget::createNewProject(const QString& projectName, const QString& projectPath)
{
    // Clear existing content
    clear();
    
    // Set header to project name
    setHeaderLabel(projectName);
    
    // Create only Solver node initially
    m_solverItem = new QTreeWidgetItem(this);
    m_solverItem->setText(0, tr("Solver"));
    m_solverItem->setIcon(0, getNodeIcon(NodeType::Solver));
    m_solverItem->setExpanded(true);
    setNodeType(m_solverItem, NodeType::Solver);
    
    // Add input and output nodes under Solver
    m_inputItem = new QTreeWidgetItem(m_solverItem);
    m_inputItem->setText(0, tr("Input"));
    m_inputItem->setIcon(0, getNodeIcon(NodeType::Input));
    m_inputItem->setExpanded(true);
    setNodeType(m_inputItem, NodeType::Input);
    
    m_outputItem = new QTreeWidgetItem(m_solverItem);
    m_outputItem->setText(0, tr("Output"));
    m_outputItem->setIcon(0, getNodeIcon(NodeType::Output));
    setNodeType(m_outputItem, NodeType::Output);
    
    // Reset other item pointers since they're not created initially
    m_optimizeItem = nullptr;
    m_sensitivityItem = nullptr;
    m_uqItem = nullptr;
    
    LOG_INFO(QString("New project created: %1 at %2").arg(projectName, projectPath), "CustomTreeWidget");
}


