#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QCloseEvent>
#include <QEvent>
#include "../SARibbonBar/SARibbonMainWindow.h"
#include "../SARibbonBar/SARibbonBar.h"
#include "../SARibbonBar/SARibbonCategory.h"
#include "../SARibbonBar/SARibbonPannel.h"
#include "../SARibbonBar/SARibbonContextCategory.h"
#include "../core/ThemeManager.h"
#include "../widgets/CustomTreeWidget.h"
#include "../widgets/ParamWidget.h"

class QSplitter;
class QTextEdit;
class QLabel;
class QStatusBar;
class QTreeWidgetItem;
class QPushButton;
class QXmlStreamWriter;
class QXmlStreamReader;
class SolverOutputWidget;

class MainWindow : public SARibbonMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

    // Log output methods
    void appendLog(const QString& message, const QString& level = "INFO");

protected:
    // Override close event to handle application closure
    void closeEvent(QCloseEvent *event) override;
    
    // Handle change events (language, style, etc.)
    void changeEvent(QEvent *event) override;

    // Setup methods
    void setupCentralWidget();
    void setupStatusBar();
    void setupRibbonInterface();
    void createHomeTab();
    void createEditTab();
    void createViewTab();
    void createToolsTab();
    void createHelpTab();

    // New methods for dynamic ribbon customization
    void addCustomRibbonCategory(const QString& categoryName);
    void removeRibbonCategory(const QString& categoryName);
    void addActionToPannel(const QString& categoryName, const QString& pannelName, QAction* action);
    void setRibbonContextCategory(const QString& categoryName, bool isVisible);

    // Contextual tab management
    void addContextualTab(const QString& name);
    void toggleContextualTab(const QString& name, bool visible);

    // Ribbon customization
    void setupRibbonStyle();

private Q_SLOTS:
    // File operations
    void onNewFile();
    void onOpenFile();
    void onSaveFile();
    void onSaveAs();

    // Project operations
    void onNewProject();
    void onOpenProject();
    void onCloseProject();

    // Edit operations
    void onCut();
    void onCopy();
    void onPaste();
    void onUndo();
    void onRedo();

    // View operations
    void onZoomIn();
    void onZoomOut();
    void onResetView();
    void onActualSize();
    void onToggleToolsPanel(bool checked);
    void onTogglePropertiesPanel(bool checked);
    void onToggleFullScreen(bool checked);

    // Tools operations
    void onApplyFilter();
    void onAdjustImage();
    void onApplyEffects();
    void onShowHistogram();
    void onShowStatistics();
    void onMeasure();

    // Help operations
    void onShowHelp();
    void onAbout();
    void onCheckUpdates();
    void onPreferences();
    void onCustomize();

    // Theme operations
    void onThemeChanged();

    // Legacy slots (keeping for compatibility)
    void onImportData();
    void onExportData();
    void onSettings();
    void onHelp();

    // New dynamic customization slots
    void onContextualTabChanged(bool checked);
    void onRibbonCompactModeToggled(bool checked);
    void onTreeItemChanged();
    void onTreeItemDoubleClicked(QTreeWidgetItem* item, NodeType type);
    void onStyleChanged();

    // Log output slots
    void onClearLog();
    void onSaveLog();
    void onToggleLogPanel(bool checked);
    
    // Module widget slots
    void onModuleWidgetRequested(QWidget* widget, const QString& title);

private:
    // Prompt user to save unsaved changes before closing
    bool maybeSave();
    
    // Component parameter display methods
    void showComponentParameters(QTreeWidgetItem* item);
    void showSolverOverview();
    
    // Project file management
    bool saveProjectToXML(const QString& filePath);
    bool loadProjectFromXML(const QString& filePath);
    void saveTreeNodeToXML(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void saveParametersToXML(QXmlStreamWriter& writer);
    void saveNodeParametersToXML(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    QTreeWidgetItem* loadTreeNodeFromXML(QXmlStreamReader& reader, QTreeWidgetItem* parent = nullptr);
    void loadParametersFromXML(QXmlStreamReader& reader);
    
    // Node parameter management
    void saveOptimizationParametersToNode(QTreeWidgetItem* item, OptimizeWidget* widget);
    void saveSensitivityParametersToNode(QTreeWidgetItem* item, SensitivityWidget* widget);
    void saveUQParametersToNode(QTreeWidgetItem* item, UQWidget* widget);
    void saveInputParametersToNode(QTreeWidgetItem* item, InputWidget* widget);
    void saveOutputParametersToNode(QTreeWidgetItem* item, OutputWidget* widget);
    void saveSolverOutputParametersToNode(QTreeWidgetItem* item, SolverOutputWidget* widget);
    void loadOptimizationParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadSensitivityParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadUQParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadInputParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadOutputParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadSolverOutputParametersFromNode(QXmlStreamWriter& writer, QTreeWidgetItem* item);
    void loadOptimizationParametersToWidget(QTreeWidgetItem* item, OptimizeWidget* widget);
    void loadSensitivityParametersToWidget(QTreeWidgetItem* item, SensitivityWidget* widget);
    void loadUQParametersToWidget(QTreeWidgetItem* item, UQWidget* widget);
    void loadInputParametersToWidget(QTreeWidgetItem* item, InputWidget* widget);
    void loadOutputParametersToWidget(QTreeWidgetItem* item, OutputWidget* widget);
    void loadSolverOutputParametersToWidget(QTreeWidgetItem* item, SolverOutputWidget* widget);
    
    // XML parameter loading helpers
    void loadNodeParametersFromXML(QXmlStreamReader& reader);
    QTreeWidgetItem* findTreeNodeByNameAndType(const QString& name, NodeType type);
    void loadOptimizationParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item);
    void loadSensitivityParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item);
    void loadUQParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item);
    void loadInputParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item);
    void loadOutputParametersFromXML(QXmlStreamReader& reader, QTreeWidgetItem* item);

    // Update UI state based on project status
    void updateActionStates();

    SARibbonBar* m_ribbonBar;
    ThemeManager* m_themeManager;

    // UI components
    QSplitter* m_splitter;
    QSplitter* m_workAreaSplitter;
    QTextEdit* m_textEdit;
    QTextEdit* m_logOutput;
    CustomTreeWidget* m_treeWidget;
    ParamWidget* m_paramWidget;
    QWidget* m_stackedWidget;
    QPushButton* m_clearLogButton;
    QPushButton* m_saveLogButton;
    
    // File management
    QString m_currentFile;
    
    // Project management
    QString m_currentProjectName;
    QString m_currentProjectPath;
    QDateTime m_lastModified;

    // Contextual category tracking
    QMap<QString, SARibbonCategory*> m_ribbonCategories;
    QMap<QString, SARibbonPannel*> m_ribbonPannels;
    QMap<QString, SARibbonContextCategory*> m_contextCategories;

    // Track if there are unsaved changes
    bool m_isModified;
    
    // Store action references for enable/disable control
    QAction* m_openFileAction;
    QAction* m_saveFileAction;
    
    // Store input file path for project management
    QString m_inputFilePath;

    void adjustFontSizesForDPI();
};

#endif // MAINWINDOW_H 
