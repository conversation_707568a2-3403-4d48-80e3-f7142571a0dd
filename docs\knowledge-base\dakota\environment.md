# Environment Module

This document contains all environment related documentation organized by hierarchy.

---

## environment

# environment

Top-level settings for Dakota execution

**Topics**

block

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no environment

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [tabular_data](environment-tabular_data.html) | Write a tabular results file with variable and response history  
Optional | [output_file](environment-output_file.html) | Base filename for output redirection  
Optional | [error_file](environment-error_file.html) | Base filename for error redirection  
Optional | [read_restart](environment-read_restart.html) | Base filename for restart file read  
Optional | [write_restart](environment-write_restart.html) | Base filename for restart file write  
Optional | [output_precision](environment-output_precision.html) | Control the output precision  
Optional | [results_output](environment-results_output.html) | (Experimental) Write a summary file containing the final results  
Optional | [graphics](environment-graphics.html) | (DEPRECATED) Display plots of variables and responses  
Optional | [check](environment-check.html) | Invoke Dakota in input check mode  
Optional | [pre_run](environment-pre_run.html) | Invoke Dakota with pre-run mode active  
Optional | [run](environment-run.html) | Invoke Dakota with run mode active  
Optional | [post_run](environment-post_run.html) | Invoke Dakota with post-run mode active  
Optional | [top_method_pointer](environment-top_method_pointer.html) | Identify which method leads the Dakota study  
  
**Description**

The environment section in a Dakota input file is optional. It specifies the top-level solution environment, optionally including run modes, output controls, and identification of the primary iterative method ( `top_method_pointer`). The output-related keywords address generation of tabular and results data, and precision of numerical output.

_Run Mode Defaults_

Dakota run phases include `check`, `pre_run`, `run`, and `post_run`. The default behavior is to `pre_run`, `run`, and `post_run`, though any or all of these may be specified to select specific run phases. Specifying `check` will cause Dakota to exit before any selected run modes.


---

### environment → check

# check

Invoke Dakota in input check mode

**Topics**

command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no check; proceed to run

**Description**

When specified, Dakota input will be parsed and the problem instantiated. Dakota will exit reporting whether any errors were found.


---

### environment → error_file

# error_file

Base filename for error redirection

**Topics**

dakota_IO, command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ errors to console, not file

**Description**

Specify a base filename to which Dakota errors will be directed. Errors will (necessarily) be redirected after the input file is parsed. This option is overridden by any command-line -error option.

_Default Behavior_

Errors to console (screen).


---

### environment → graphics

# graphics

(DEPRECATED) Display plots of variables and responses

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ graphics off

**Description**

Activate Dakota’s legacy X Windows-based graphics feature. Dakota plotting and visualization capabilities are increasingly available in the Dakota graphical user interface (GUI); see the Dakota GUI User Manual in the documentation section of the Dakota website for additional details.

The `graphics` flag activates a 2D graphics window containing history plots for the variables and response functions in the study. This window is updated approximately every two seconds. Some study types such as surrogate-based optimization or local reliability specialize the use of the graphics window.

There is no dependence between the `graphics` flag and the `tabular_data` flag; they may be used independently or concurrently.


---

### environment → output_file

# output_file

Base filename for output redirection

**Topics**

dakota_IO, command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ output to console, not file

**Description**

Specify a base filename to which Dakota output will be directed. Output will (necessarily) be redirected after the input file is parsed. This option is overridden by any command-line -output option.

_Default Behavior_

Output to console (screen).


---

### environment → output_precision

# output_precision

Control the output precision

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ 10

**Description**

The precision of numeric output precision can be set with `output_precision`, with an upper limit of 16. When not specified, most Dakota output will default to a precision of 10, though filesystem interfaces and pre-run output use higher precision for accuracy and better results reproducibility.


---

### environment → post_run

# post_run

Invoke Dakota with post-run mode active

**Topics**

command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ pre-run, run, post-run all executed

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [input](environment-post_run-input.html) | Base filename for post-run mode data input  
Optional | [output](environment-post_run-output.html) | Base filename for post-run mode data output  
  
**Description**

When specified, Dakota execution will include the post-run mode, which analyzes parameter/response data sets and computes final results.. This mode is currently useful for parameter study, DACE, and Monte Carlo sampling methods.

_Default Behavior_

When no run modes are specified, Dakota will perform pre-run, run, and post-run phases.


---

#### environment → post_run → input

# input

Base filename for post-run mode data input

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no post-run specific input read

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](environment-post_run-input-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](environment-post_run-input-annotated.html) | Selects annotated tabular file format  
[freeform](environment-post_run-input-freeform.html) | Selects freeform file format  
  
**Description**

Specify a base filename from which Dakota will read any post-run input data, such as parameter/response data on which to calulate final statistics. This option is overridden by any command-line -post_run arguments.

_Usage Tips_

Dakota imports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

##### environment → post_run → input → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### environment → post_run → input → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](environment-post_run-input-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](environment-post_run-input-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](environment-post_run-input-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### environment → post_run → input → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no eval_id column

**Description**

See description of parent `custom_annotated`


---

###### environment → post_run → input → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no header

**Description**

See description of parent `custom_annotated`


---

###### environment → post_run → input → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no interface_id column

**Description**

See description of parent `custom_annotated`


---

##### environment → post_run → input → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

#### environment → post_run → output

# output

Base filename for post-run mode data output

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no post-run specific output written

**Description**

(For future expansion; not currently used by any methods.) Specify a base filename to which Dakota will write any post-run output data. This option is overridden by any command-line

-post_run arguments.


---

### environment → pre_run

# pre_run

Invoke Dakota with pre-run mode active

**Topics**

command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ pre-run, run, post-run all executed

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [input](environment-pre_run-input.html) | Base filename for pre-run mode data input  
Optional | [output](environment-pre_run-output.html) | Base filename for pre-run mode data output  
  
**Description**

When specified, Dakota execution will include the pre-run mode, which sets up methods and often generates parameter sets to evaluate. This mode is currently useful for parameter study, DACE, and Monte Carlo sampling methods.

_Default Behavior_

When no run modes are specified, Dakota will perform pre-run, run, and post-run phases.


---

#### environment → pre_run → input

# input

Base filename for pre-run mode data input

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no pre-run specific input read

**Description**

(For future expansion; not currently used by any methods.) Specify a base filename from which Dakota will read any pre-run input data. This option is overridden by any command-line

-pre_run arguments.


---

#### environment → pre_run → output

# output

Base filename for pre-run mode data output

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no pre-run specific output written

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional (Choose One) | Tabular Format | [custom_annotated](environment-pre_run-output-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](environment-pre_run-output-annotated.html) | Selects annotated tabular file format  
[freeform](environment-pre_run-output-freeform.html) | Selects freeform file format  
  
**Description**

Specify a base filename to which Dakota will write any pre-run output data (typically parameter sets to be evaluated). This option is overridden by any command-line -pre_run arguments.

_Usage Tips_

Dakota exports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

##### environment → pre_run → output → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### environment → pre_run → output → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](environment-pre_run-output-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](environment-pre_run-output-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](environment-pre_run-output-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

###### environment → pre_run → output → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no eval_id column

**Description**

See description of parent `custom_annotated`


---

###### environment → pre_run → output → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no header

**Description**

See description of parent `custom_annotated`


---

###### environment → pre_run → output → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no interface_id column

**Description**

See description of parent `custom_annotated`


---

##### environment → pre_run → output → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

### environment → read_restart

# read_restart

Base filename for restart file read

**Topics**

dakota_IO, command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no restart read

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [stop_restart](environment-read_restart-stop_restart.html) | Restart record number at which to stop reading the restart file.  
  
**Description**

Specify a base filename for the restart file Dakota should read. This option is overridden by any command-line

-read_restart option.

_Default Behavior_

No restart file is read.


---

#### environment → read_restart → stop_restart

# stop_restart

Restart record number at which to stop reading the restart file.

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ INTEGER

  * _Default:_ read all records

**Description**

This option is overridden by any command-line

-stop_restart option.


---

### environment → results_output

# results_output

(Experimental) Write a summary file containing the final results

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no results output

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [results_output_file](environment-results_output-results_output_file.html) | The base file name of the results file  
Optional | [text](environment-results_output-text.html) | Write results to file in text format  
Optional | [hdf5](environment-results_output-hdf5.html) | Write results to file in HDF5 format  
  
**Description**

(Experimental)

Dakota writes final results for most methods to the screen (console). This keyword enables experimental support for writing them to disk, as well. By default, they are written in a structured text format to a file named `dakota_results`.txt. Text format can be explicitly selected using the `txt.text` sub-keyword. If Dakota was built with `HDF5` enabled, the sub-keyword `hdf5` causes results to be written in HDF5 format, as well.

The name of the results file can be changed using the `results_output_file` keyword.

Text output eventually will be deprecated, and HDF5 will be enabled in all distributed builds of Dakota. The layout of the HDF5 file is described in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual. The contents, organization, and format of results files are all under active development and are subject to change.


---

#### environment → results_output → hdf5

# hdf5

Write results to file in HDF5 format

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no HDF5 output

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [model_selection](environment-results_output-hdf5-model_selection.html) | Select the models that write evaluation data to HDF5  
Optional | [interface_selection](environment-results_output-hdf5-interface_selection.html) | Select the models that write evaluation data to HDF5  
  
**Description**

When this keyword is present, Dakota will right (some) final method results to disk in HDF5 format instead of just to the console. This is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

With `hdf5` selected, the default name of the output file is `dakota_results`.h5. This can be changed using the `h5.results_output_file` keyword.

**Examples**

    environment
      results_output
          hdf5
          results_output_file 'my_results'  # The .h5 extension will be added


---

##### environment → results_output → hdf5 → interface_selection

# interface_selection

Select the models that write evaluation data to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Interface Evaluation Storage Selection | [none](environment-results_output-hdf5-interface_selection-none.html) | Write evaluation data for no interfaces to HDF5  
[simulation](environment-results_output-hdf5-interface_selection-simulation.html) | Write evaluation data only for simulation interfaces to HDF5  
[all](environment-results_output-hdf5-interface_selection-all.html) | Write evaluation data for all interfaces to HDF5  
  
**Description**

By default, when HDF5 output is enabled, Dakota writes evaluation data for all simulation interfaces (interfaces of type fork, system, direct, etc, but not approximation interfaces, which are constructed by models of type surrogate). This keyword group is used to override the default.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

The example below will be used to explain the effect of each keyword.

**Examples**

    environment
      results_output
          hdf5
           # interface_selection
           #  simulation
           #  all
           #  none
          results_output_file 'my_results'  # The .h5 extension will be added
    
       method_pointer 'opt'
    
    method
      id_method 'opt'
        optpp_q_newton
      model_pointer 'surr'
    
    model
      id_model 'surr'
      surrogate global gaussian_process surfpack
      dace_method_pointer 'training'
    
    method
      id_method 'training'
      sampling
        seed 1234
        samples 20
      model_pointer 'truth_m'
    
    model
      id_model 'truth_m'
      simulation
    
    interface
      id_interface 'truth'
      direct
        analysis_drivers 'text_book'
    
    variables
      continuous_design 2
        descriptors 'x1' 'x2'
        lower_bounds -2.0 -2.0
        upper_bounds  2.0  2.0
    
    responses
      objective_functions 2
        descriptors 'f1' 'f2'
      analytic_gradients
      no_hessians


---

###### environment → results_output → hdf5 → interface_selection → all

# all

Write evaluation data for all interfaces to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ top_simulation

**Description**

When this option is selected, evaluation data for all interfaces, including both simulations and approximations, will be written to HDF5.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

**Examples**

For the example input in the parent keyword, the following interface groups would be written:

  * `/interfaces/truth/truth_m/`

  * `/interfaces/APPROX_INTERFACE_1/surr/`

Depending on the `[model_selection](../../usingdakota/reference/environment-results_output-hdf5-model_selection.html)`, the following links may be added to model `sources` groups.

  * `/models/simulation/truth_m/sources/truth` \\(\rightarrow\\) `/interfaces/truth/truth_m/`

  * `/models/surrogate/surr/sources/APPROX_INTERFACE_1` \\(\rightarrow\\) `/interfaces/APPROX_INTERFACE_1/surr/`


---

###### environment → results_output → hdf5 → interface_selection → none

# none

Write evaluation data for no interfaces to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ simulation

**Description**

When this option is selected, no interface evaluation data is written to HDF5, and the `/interfaces` group is not created.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.


---

###### environment → results_output → hdf5 → interface_selection → simulation

# simulation

Write evaluation data only for simulation interfaces to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ simulation

**Description**

When this option is selected, evaluation data only for simulation interfaces will be written to HDF5. Simulation interfaces include fork, system, direct, matlab, python, scilab, and grid interfaces, but not approximation interfaces, which are automatically generated by Dakota to be used by surrogate models.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

**Examples**

For the example input in the parent keyword, the following interface groups would be written:

  * `/interfaces/truth/truth_m/`

Depending on the `[model_selection](../../usingdakota/reference/environment-results_output-hdf5-model_selection.html)`, the following links may be added to model `sources` groups.

  * `/models/simulation/truth_m/sources/truth` \\(\rightarrow\\) `/interfaces/truth/truth_m/`


---

##### environment → results_output → hdf5 → model_selection

# model_selection

Select the models that write evaluation data to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Required (Choose One) | Model Evaluation Storage Selection | [top_method](environment-results_output-hdf5-model_selection-top_method.html) | Write evaluation data only for the top-level method’s model to HDF5  
[none](environment-results_output-hdf5-model_selection-none.html) | Write evaluation data for no models to HDF5  
[all_methods](environment-results_output-hdf5-model_selection-all_methods.html) | Write evaluation data to HDF5 for all models that belong directly to methods  
[all](environment-results_output-hdf5-model_selection-all.html) | Write evaluation data to HDF5 for all models  
  
**Description**

By default, when HDF5 output is enabled, Dakota writes evaluation data only for the model that belongs to the top-level method. This keyword group is used to override the default.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

The example below will be used to explain the effect of each keyword.

**Examples**

    environment
      results_output
          hdf5
           # model_selection
           #  top_method
           #  all_methods
           #  all
           #  none
          results_output_file 'my_results'  # The .h5 extension will be added
    
       method_pointer 'opt'
    
    method
      id_method 'opt'
        optpp_q_newton
      model_pointer 'surr'
    
    model
      id_model 'surr'
      surrogate global gaussian_process surfpack
      dace_method_pointer 'training'
    
    method
      id_method 'training'
      sampling
        seed 1234
        samples 20
      model_pointer 'truth_m'
    
    model
      id_model 'truth_m'
      simulation
    
    interface
      id_interface 'truth'
      direct
        analysis_drivers 'text_book'
    
    variables
      continuous_design 2
        descriptors 'x1' 'x2'
        lower_bounds -2.0 -2.0
        upper_bounds  2.0  2.0
    
    responses
      objective_functions 2
        descriptors 'f1' 'f2'
      analytic_gradients
      no_hessians


---

###### environment → results_output → hdf5 → model_selection → all

# all

Write evaluation data to HDF5 for all models

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ top_method

**Description**

When this option is selected, evaluation for all models will be written to HDF5.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

**Examples**

For the example input in the parent keyword, the following model groups would be written:

  * `/models/recast/RECAST_surr_RECAST_1/`

  * `/models/simulation/truth_m/`

  * `/models/surrogate/surr/`

The recast model, in this case, is automatically generated by Dakota to handle summing the pair of objective functions. See [hdf5_evaluations-hdf5_eval_sources](../output/hdf/organizationofevaluations.html#hdf5-evaluations-hdf5-eval-sources) for further explanation.

The following links would be added to methods and models `sources` groups:

  * `/methods/opt/sources/RECAST_surr_RECAST_1` \\(\rightarrow\\) `/models/recast/RECAST_surr_RECAST_1/`

  * `/methods/training/sources/truth_m` \\(\rightarrow\\) `/models/simulation/truth_m/`

  * `/models/recast/RECAST_surr_RECAST_1/sources/surr` \\(\rightarrow\\) `/models/surrogate/surr/`

Depending on `[interface_selection](../../usingdakota/reference/environment-results_output-hdf5-interface_selection.html)`, the following links may also be added to model `sources` groups:

  * `/models/surrogate/surr/sources/APPROX_INTERFACE_1` \\(\rightarrow\\) `/interfaces/APPROX_INTERFACE_1/surr/`

  * `/models/simulation/truth_m/sources/truth` \\(\rightarrow\\) `/interfaces/truth/truth_m/`


---

###### environment → results_output → hdf5 → model_selection → all_methods

# all_methods

Write evaluation data to HDF5 for all models that belong directly to methods

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ top_method

**Description**

When this option is selection, evaluation data for all models that belong directly to methods will be written to HDF5. Models that belong to other models (e.g. wrapped models in a recast relationship) are not stored.

By default, when HDF5 output is enabled, Dakota writes evaluation data only for the model that belongs to the top-level method. This keyword group is used to override the default.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

**Examples**

For the example input in the parent keyword, the following model groups would be written:

  * `/models/recast/RECAST_surr_RECAST_1/`

  * `/models/simulation/truth_m/`

The recast model, in this case, is automatically generated by Dakota to handle summing the pair of objective functions. It is the model that belongs directly to the method ‘opt’. See [hdf5_evaluations-hdf5_eval_sources](../output/hdf/organizationofevaluations.html#hdf5-evaluations-hdf5-eval-sources) for further explanation.

The following links would be added to methods and models `sources` groups:

  * `/methods/opt/sources/RECAST_surr_RECAST_1` \\(\rightarrow\\) `/models/recast/RECAST_surr_RECAST_1/`

  * `/methods/training/sources/truth_m` \\(\rightarrow\\) `/models/simulation/truth_m/`

Depending on `[interface_selection](../../usingdakota/reference/environment-results_output-hdf5-interface_selection.html)`, the following links may also be added to model `sources` groups:

  * `/models/simulation/truth_m/sources/truth` \\(\rightarrow\\) `/interfaces/truth/truth_m/`


---

###### environment → results_output → hdf5 → model_selection → none

# none

Write evaluation data for no models to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ top_method

**Description**

When this option is selected, no model evaluation data is written to HDF5, and the `/models` group itself is not created.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.


---

###### environment → results_output → hdf5 → model_selection → top_method

# top_method

Write evaluation data only for the top-level method’s model to HDF5

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ top_method

**Description**

When this option is selected, evaluation data only for the model that belongs to the top-level method will be written to HDF5.

HDF5 output is an experimental feature, and the contents and organization of the output file is subject to change. The current organization and a brief explanation of HDF5 is provided in the [hdf5_output](../output/hdf.html#hdf5-output) section of this manual.

**Examples**

For the example input in the parent keyword, the following model groups would be written:

  * `/models/recast/RECAST_surr_RECAST_1/`

The recast model, in this case, is automatically generated by Dakota to handle summing the pair of objective functions. It is the model that belongs directly to the method ‘opt’, which is the top-level method. See [hdf5_evaluations-hdf5_eval_sources](../output/hdf/organizationofevaluations.html#hdf5-evaluations-hdf5-eval-sources) for further explanation.

The following links would be added to methods and models `sources` groups:

  * `/methods/opt/sources/RECAST_surr_RECAST_1` \\(\rightarrow\\) `/models/recast/RECAST_surr_RECAST_1/`


---

#### environment → results_output → results_output_file

# results_output_file

The base file name of the results file

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ dakota_results

**Description**

The default filename is `dakota_results`.txt for text output and `dakota_results`.h5 for `h5HDF5`. The `dakota_results` stem can be changed using this keyword. Do not include the extension; these will be added by Dakota.


---

#### environment → results_output → text

# text

Write results to file in text format

**Topics**

dakota_output

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ text output

**Description**

When this keyword is present, Dakota will right (some) final method results to disk in a custom structured text format instead of just to the console. Support for text format results output will be deprecated in a future release, and the contents and organization of the output file is subject to change.

The default name of the file is `dakota_results`.txt. This can be changed using the `results_output_file` keyword.

**Examples**

    environment
      results_output
          text
          results_output_file 'my_results'  # The .txt extension will be added


---

### environment → run

# run

Invoke Dakota with run mode active

**Topics**

command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ pre-run, run, post-run all executed

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [input](environment-run-input.html) | Base filename for run mode data input  
Optional | [output](environment-run-output.html) | Base filename for run mode data output  
  
**Description**

When specified, Dakota execution will include the run mode, which invokes interfaces to map parameters to responses.

_Default Behavior_

When no run modes are specified, Dakota will perform pre-run, run, and post-run phases.


---

#### environment → run → input

# input

Base filename for run mode data input

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no run specific input read

**Description**

(For future expansion; not currently used by any methods.) Specify a base filename from which Dakota will read any run input data, such as parameter sets to evaluate. This option is overridden by any command-line -run arguments.


---

#### environment → run → output

# output

Base filename for run mode data output

**Topics**

dakota_IO

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ no run specific output written

**Description**

(For future expansion; not currently used by any methods.) Specify a base filename to which Dakota will write any run output data (typically parameter, response pairs). This option is overridden by any command-line -run arguments.


---

### environment → tabular_data

# tabular_data

Write a tabular results file with variable and response history

**Topics**

dakota_output

**Specification**

  * _Alias:_ tabular_graphics_data

  * _Arguments:_ None

  * _Default:_ no tabular data output

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [tabular_data_file](environment-tabular_data-tabular_data_file.html) | File name for tabular data output  
Optional (Choose One) | Tabular Data Format | [custom_annotated](environment-tabular_data-custom_annotated.html) | Selects custom-annotated tabular file format  
[annotated](environment-tabular_data-annotated.html) | Selects annotated tabular file format  
[freeform](environment-tabular_data-freeform.html) | Selects freeform file format  
  
**Description**

Specifying the `tabular_data` flag writes to a data file the same variable and response function history data plotted when using the (deprecated) `graphics` flag. Within the generated data file, the variables and response functions appear as columns and each function evaluation provides a new table row. This capability is most useful for post-processing of Dakota results with third-party graphics tools such as MatLab, Excel, Tecplot, etc.

There is no dependence between the `graphics` flag and the `tabular_data` flag; they may be used independently or concurrently.

Dakota exports tabular data in one of three formats:

  * `annotated` (default)

  * `custom_annotated`

  * `freeform`


---

#### environment → tabular_data → annotated

# annotated

Selects annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

An annotated tabular file is a whitespace-separated text file with one leading header row of comments/column labels. Each subsequent row contains an evaluation ID and interface ID, followed by data for variables, or variables followed by responses, depending on context.

_Default Behavior_

By default, Dakota imports and exports tabular files in annotated format. The `annotated` keyword can be used to explicitly specify this.

_Usage Tips_

  * To specify pre-Dakota 6.1 tabular format, which did not include interface_id, specify `custom_annotated` `header` `eval_id`

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export an annotated top-level tabular data file containing a header row, leading eval_id and interface_id columns, and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        annotated

Resulting tabular file:

    eval_id interface             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1            NO_ID            0.9            1.1         0.0002           0.26           0.76
    2            NO_ID        0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3            NO_ID        0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

#### environment → tabular_data → custom_annotated

# custom_annotated

Selects custom-annotated tabular file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Child Keywords:**

Required/Optional | Description of Group | Dakota Keyword | Dakota Keyword Description  
---|---|---|---  
Optional | [header](environment-tabular_data-custom_annotated-header.html) | Enable header row in custom-annotated tabular file  
Optional | [eval_id](environment-tabular_data-custom_annotated-eval_id.html) | Enable evaluation ID column in custom-annotated tabular file  
Optional | [interface_id](environment-tabular_data-custom_annotated-interface_id.html) | Enable interface ID column in custom-annotated tabular file  
  
**Description**

A custom-annotated tabular file is a whitespace-separated text file typically containing row data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well. Custom-annotated allows user options for whether `header` row, `eval_id` column, and `interface_id` column appear in the tabular file, thus bridging `freeform` and (fully) `annotated`.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To control which header row and columns are in the input/output, specify `custom_annotated`, followed by options, in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were in free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a custom-annotated tabular file in Dakota 6.0 format, which contained only header and eval_id (no interface_id), and data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        custom_annotated header eval_id

Resulting tabular file:

    eval_id             x1             x2         obj_fn nln_ineq_con_1 nln_ineq_con_2
    1                   0.9            1.1         0.0002           0.26           0.76
    2               0.90009            1.1 0.0001996404857   0.2601620081       0.759955
    3               0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

##### environment → tabular_data → custom_annotated → eval_id

# eval_id

Enable evaluation ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no eval_id column

**Description**

See description of parent `custom_annotated`


---

##### environment → tabular_data → custom_annotated → header

# header

Enable header row in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no header

**Description**

See description of parent `custom_annotated`


---

##### environment → tabular_data → custom_annotated → interface_id

# interface_id

Enable interface ID column in custom-annotated tabular file

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ no interface_id column

**Description**

See description of parent `custom_annotated`


---

#### environment → tabular_data → freeform

# freeform

Selects freeform file format

**Topics**

file_formats

**Specification**

  * _Alias:_ None

  * _Arguments:_ None

  * _Default:_ annotated format

**Description**

A freeform tabular file is whitespace-separated text file with no leading header row and no leading columns. Most commonly, each row contains data for variables, or variables followed by responses, though the format is used for other tabular exports/imports as well.

_Default Behavior_

The `annotated` format is the default for tabular export/import. To change this behavior, specify `freeform` in the relevant export/import context.

_Usage Tips_

  * Prior to October 2011, calibration and surrogate data files were free-form format. They now default to `annotated` format, though `freeform` remains an option.

  * When importing tabular data, a warning will be generated if a specific number of data are expected, but extra is found and an error generated when there is insufficient data.

  * In `freeform`, the num_rows x num_cols total data entries may be separated with any whitespace including spaces, tabs, and newlines. In this format, vectors may therefore appear as a single row or single column (or mixture; entries will populate the vector in order).

  * Some TPLs like SCOLIB and JEGA manage their own file I/O and only support the `freeform` option.

**Examples**

Export a freeform tabular file containing only data for variables and responses. Input file fragment:

    environment
      tabular_data
        tabular_data_file = 'dakota_summary.dat'
        freeform

Resulting tabular file:

                0.9            1.1         0.0002           0.26           0.76
            0.90009            1.1 0.0001996404857   0.2601620081       0.759955
            0.89991            1.1 0.0002003604863   0.2598380081       0.760045
    ...


---

#### environment → tabular_data → tabular_data_file

# tabular_data_file

File name for tabular data output

**Topics**

dakota_output

**Specification**

  * _Alias:_ tabular_graphics_file

  * _Arguments:_ STRING

  * _Default:_ dakota_tabular.dat

**Description**

Specifies a name to use for the tabular data file, overriding the default `dakota_tabular`.dat.


---

### environment → top_method_pointer

# top_method_pointer

Identify which method leads the Dakota study

**Topics**

block_pointer

**Specification**

  * _Alias:_ method_pointer

  * _Arguments:_ STRING

  * _Default:_ see discussion

**Description**

An optional `top_method_pointer` specification may be used to point to a particular method specification that will lead the Dakota analysis. The associated string must be a method identifier specified via `[id_method](../../usingdakota/reference/method-id_method.html)`. If `top_method_pointer` is not used, then it will be inferred as decribed below (no `top_method_pointer` within an environment specification is treated the same as no environment specification).

_Default Behavior_

The `top_method_pointer` keyword is typically used in Dakota studies consisting of more than one `[method](../../usingdakota/reference/method.html)` block to clearly indicate which is the leading method. This method provides the starting point for the iteration. The corresponding method specification may recurse with additional sub-method pointers in the case of “meta-iteration” (see `[method](../../usingdakota/reference/method.html)`) or may specify a single method without recursion. Either case will ultimately result in identification of one or more model specifications using `model_pointer`, which again may or may not involve further recursion (see `[nested](../../usingdakota/reference/model-nested.html)` and `[surrogate](../../usingdakota/reference/model-surrogate.html)` for recursion cases). Each of the model specifications identify the variables and responses specifications (using `[variables_pointer](../../usingdakota/reference/model-variables_pointer.html)` and `[responses_pointer](../../usingdakota/reference/model-responses_pointer.html)`) that are used to build the model, and depending on the type of model, may also identify an interface specification (for example, using `[interface_pointer](../../usingdakota/reference/model-single-interface_pointer.html)`). If one of these specifications does not provide an optional pointer, then that component will be constructed using the last specification parsed.

When the environment block is omitted, the top level method will be inferred as follows: When a single method is specified, there is no ambiguity and the sole method will be the top method. When multiple methods are specified, the top level method will be deduced from the hierarchical relationships implied by method pointers. If this inference is not well defined (e.g., there are multiple method specifications without any pointer relationship), then the default behavior is to employ the last method specification parsed.

**Examples**

Specify that the optimization method is the outermost method in an optimization under uncertainty study

    environment
      top_method_pointer 'OPTIMIZATION_METHOD'
    method
      id_method 'UQ_METHOD'
    ...
    method
      id_method 'OPTIMIZATION_METHOD'
    ...


---

### environment → write_restart

# write_restart

Base filename for restart file write

**Topics**

dakota_IO, command_line_options

**Specification**

  * _Alias:_ None

  * _Arguments:_ STRING

  * _Default:_ dakota.rst

**Description**

Specify a base filename for the restart file Dakota should write. This option is overridden by any command-line

-write_restart option.

