#ifndef PARAMWIDGET_H
#define PARAMWIDGET_H

#include <QWidget>
#include <QStackedWidget>
#include <QLabel>
#include <QVBoxLayout>

class OptimizeWidget;
class InputWidget;
class OutputWidget;
class SolverOutputWidget;
class SensitivityWidget;
class UQWidget;

class ParamWidget : public QWidget
{
    Q_OBJECT

public:
    explicit ParamWidget(QWidget *parent = nullptr);
    ~ParamWidget();

    // 根据节点类型显示不同的参数设置界面
    void showOptimizationParams();
    void showSensitivityParams();
    void showUQParams();
    void showInputParams();
    void showOutputParams();
    void showSolverOutputParams();
    
    // 显示模块组件编辑器（如PipeWidget、TubeWidget等）
    void setModuleWidget(QWidget* widget, const QString& title);
    void clearModuleWidget();

    // 获取各个widget的引用
    OptimizeWidget* getOptimizeWidget() const { return m_optimizeWidget; }
    SensitivityWidget* getSensitivityWidget() const { return m_sensitivityWidget; }
    UQWidget* getUQWidget() const { return m_uqWidget; }
    InputWidget* getInputWidget() const { return m_inputWidget; }
    OutputWidget* getOutputWidget() const { return m_outputWidget; }
    SolverOutputWidget* getSolverOutputWidget() const { return m_solverOutputWidget; }

signals:
    void parametersChanged();

private:
    void initUI();
    void setupConnections();

    QStackedWidget* m_stackedWidget;
    OptimizeWidget* m_optimizeWidget;
    SensitivityWidget* m_sensitivityWidget;
    UQWidget* m_uqWidget;
    InputWidget* m_inputWidget;
    OutputWidget* m_outputWidget;
    SolverOutputWidget* m_solverOutputWidget;
    
    // 当前显示的模块组件编辑器
    QWidget* m_currentModuleWidget;
    QString m_currentModuleTitle;
};

#endif // PARAMWIDGET_H 